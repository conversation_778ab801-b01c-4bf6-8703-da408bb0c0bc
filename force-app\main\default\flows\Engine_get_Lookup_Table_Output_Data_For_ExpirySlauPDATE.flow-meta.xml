<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Calcolo_Tempi_di_Lavorazione_Altro</name>
        <label>Calcolo Tempi di Lavorazione Altro</label>
        <locationX>578</locationX>
        <locationY>242</locationY>
        <actionName>Tempi_di_Lavorazione_Altro</actionName>
        <actionType>runDecisionMatrix</actionType>
        <connector>
            <targetReference>CheckForNullOutput</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>TipoTrattativa</name>
            <value>
                <elementReference>TipoTrattativa</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>TipoSoggetto</name>
            <value>
                <elementReference>TipoSoggetto</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Tempi_di_Lavorazione_Altro</nameSegment>
        <offset>0</offset>
        <outputParameters>
            <assignToReference>GiorniSLATrattativa</assignToReference>
            <name>GiorniSLATrattativa</name>
        </outputParameters>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <assignments>
        <name>AssignFlag</name>
        <label>Assign Flag</label>
        <locationX>578</locationX>
        <locationY>542</locationY>
        <assignmentItems>
            <assignToReference>flagNullOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
    </assignments>
    <decisions>
        <description>logica su Ambito in input</description>
        <name>CheckDecisionMatrixByTrattativa</name>
        <label>Check Decision Matrix by Trattativa</label>
        <locationX>446</locationX>
        <locationY>134</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Unica</name>
            <conditionLogic>(1 OR 2) AND (3 OR 4)</conditionLogic>
            <conditions>
                <leftValueReference>TipoTrattativa</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>TipoTrattativa</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Canale</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Preventivatore digitale Unica</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Canale</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Preventivatore Previdenza</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Calcolo_Tempi_di_Lavorazione</targetReference>
            </connector>
            <label>Unica</label>
        </rules>
        <rules>
            <name>Digitale_Unisalute</name>
            <conditionLogic>(1 OR 2) AND 3</conditionLogic>
            <conditions>
                <leftValueReference>TipoTrattativa</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>TipoTrattativa</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Canale</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Digitale Unisalute</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>US_Calcolo_Tempi_di_Lavorazione</targetReference>
            </connector>
            <label>Digitale Unisalute</label>
        </rules>
        <rules>
            <name>NoUnica</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>TipoTrattativa</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Canale Fisico</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>TipoTrattativa</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Appuntamento da Canale Digitale</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Calcolo_Tempi_di_Lavorazione_Altro</targetReference>
            </connector>
            <label>No Unica</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckForNullOutput</name>
        <label>Check For Null Output</label>
        <locationX>446</locationX>
        <locationY>434</locationY>
        <defaultConnector>
            <targetReference>AssignFlag</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Output Is Null</defaultConnectorLabel>
        <rules>
            <name>OutputIsValid</name>
            <conditionLogic>(1 AND 2 AND 3 AND 4) OR (5 AND 6 AND 7 AND 8) OR (7 AND 9)</conditionLogic>
            <conditions>
                <leftValueReference>Assegnazione</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>CanaleAssegnazione</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Riassegnazione</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>CanaleRiassegnazione</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GiorniSLAPresaInCarico</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GiorniSLAPresaInCaricoCallMeBack</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GiorniSLATrattativa</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GiorniSLATrattativaCallMeBack</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>DecisionMatrixName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Tempi di Lavorazione - Altro</stringValue>
                </rightValue>
            </conditions>
            <label>Output Is Valid</label>
        </rules>
    </decisions>
    <description>Messo false come default value a MultiAmbito, altrimenti il debug va in errore.
Rimpiazzata la logica dell&apos;ambito, con quella di tipo trattativa
Aggiiunto controllo su Prodotto PRV</description>
    <environments>Default</environments>
    <formulas>
        <name>TipoSoggettoFormula</name>
        <dataType>String</dataType>
        <expression>CASE({!TipoSoggetto}, &apos;PureProspect&apos;, &apos;Prospect Puro&apos;, &apos;Prospect&apos;, &apos;Prospect&apos;, &apos;Client&apos;, &apos;Cliente&apos;, &apos;Prospect Puro&apos;)</expression>
    </formulas>
    <interviewLabel>Engine - get Lookup Table Output Data For ExpirySlauPDATE {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Engine - get Lookup Table Output Data For ExpirySlauPDATE</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>320</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>CheckDecisionMatrixByTrattativa</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Calcolo_Tempi_di_Lavorazione</name>
        <label>Calcolo Tempi di Lavorazione</label>
        <locationX>50</locationX>
        <locationY>242</locationY>
        <connector>
            <targetReference>CheckForNullOutput</targetReference>
        </connector>
        <flowName>Engine_Module_SLA_Calculation</flowName>
        <inputAssignments>
            <name>Ambito</name>
            <value>
                <elementReference>Ambito</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Canale</name>
            <value>
                <elementReference>Canale</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Multiambito</name>
            <value>
                <elementReference>IsMultiambito</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Temperatura</name>
            <value>
                <elementReference>Temperatura</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>TipoSoggetto</name>
            <value>
                <elementReference>TipoSoggetto</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>GiorniSLAPresaInCarico</assignToReference>
            <name>GiorniSLAPresaInCarico</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>GiorniSLAPresaInCaricoCallMeBack</assignToReference>
            <name>GiorniSLAPresaInCaricoCallMeBack</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>GiorniSLATrattativa</assignToReference>
            <name>GiorniSLATrattativa</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>GiorniSLATrattativaCallMeBack</assignToReference>
            <name>GiorniSLATrattativaCallMeBack</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>US_Calcolo_Tempi_di_Lavorazione</name>
        <label>US Calcolo Tempi di Lavorazione</label>
        <locationX>314</locationX>
        <locationY>242</locationY>
        <connector>
            <targetReference>CheckForNullOutput</targetReference>
        </connector>
        <flowName>Engine_Module_SLA_Calculation</flowName>
        <inputAssignments>
            <name>Ambito</name>
            <value>
                <elementReference>Ambito</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Canale</name>
            <value>
                <elementReference>Canale</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Multiambito</name>
            <value>
                <elementReference>IsMultiambito</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Temperatura</name>
            <value>
                <elementReference>Temperatura</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>TipoSoggetto</name>
            <value>
                <elementReference>TipoSoggetto</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>GiorniSLAPresaInCarico</assignToReference>
            <name>GiorniSLAPresaInCarico</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>GiorniSLAPresaInCaricoCallMeBack</assignToReference>
            <name>GiorniSLAPresaInCaricoCallMeBack</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>GiorniSLATrattativa</assignToReference>
            <name>GiorniSLATrattativa</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>GiorniSLATrattativaCallMeBack</assignToReference>
            <name>GiorniSLATrattativaCallMeBack</name>
        </outputAssignments>
    </subflows>
    <variables>
        <name>AgenziaDefault</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>Ambito</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Assegnazione</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>AssegnazioneDefault</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>Canale</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>CanaleAssegnazione</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>CanaleRiassegnazione</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>DecisionMatrixName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Discrezionalita</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>ExtraCAP</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>flagNullOutput</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>GiorniSLAPresaInCarico</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>GiorniSLAPresaInCaricoCallMeBack</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>GiorniSLATrattativa</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>GiorniSLATrattativaCallMeBack</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>IsMultiambito</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>MaxRiassegnazioni</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>Motore</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>opportunityId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>RaggioCentroideKM</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>RaggioEstensioneKM</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>Riassegnazione</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>TassoContattabilita</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>Temperatura</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>TipoSoggetto</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>TipoTrattativa</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Canale Fisico</stringValue>
        </value>
    </variables>
</Flow>
