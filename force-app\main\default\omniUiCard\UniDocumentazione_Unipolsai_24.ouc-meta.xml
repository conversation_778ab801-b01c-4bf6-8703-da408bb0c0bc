<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDS_Documents&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{recordId}&quot;,&quot;Status&quot;:&quot;&apos;V&apos;&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;Status\&quot;:\&quot;{Status}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;Status&quot;,&quot;val&quot;:&quot;V&quot;,&quot;id&quot;:5}]}}</dataSourceConfig>
    <description>adding Ip count</description>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniDocumentazione</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-text-align_center slds-card &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:12,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:12,&quot;small&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:12,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:12,&quot;small&quot;:12},&quot;padding&quot;:[],&quot;class&quot;:&quot;slds-theme_default &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;background-color: #f3f3f3;&quot;,&quot;theme&quot;:&quot;theme_default&quot;,&quot;style&quot;:&quot;      \n         background-color: #f3f3f3;&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Icon&quot;,&quot;element&quot;:&quot;flexIcon&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;iconType&quot;:&quot;Custom&quot;,&quot;iconName&quot;:&quot;standard:default&quot;,&quot;size&quot;:&quot;small&quot;,&quot;extraclass&quot;:&quot;slds-icon-standard-default&quot;,&quot;variant&quot;:&quot;default&quot;,&quot;imgsrc&quot;:&quot;/img/icon/t4v35/standard/file_120.png&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-top:10px;&quot;,&quot;class&quot;:&quot;slds-theme_shade &quot;,&quot;style&quot;:&quot;      \n         margin-top:10px;&quot;,&quot;height&quot;:&quot;&quot;,&quot;minHeight&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;theme&quot;:&quot;theme_shade&quot;},&quot;elementLabel&quot;:&quot;Block-0-Icon-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;margin-top:10px;&quot;,&quot;class&quot;:&quot;slds-theme_shade &quot;,&quot;style&quot;:&quot;      \n         margin-top:10px;&quot;,&quot;height&quot;:&quot;&quot;,&quot;minHeight&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;theme&quot;:&quot;theme_shade&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_flexIcon_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;7&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_7-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;7&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_0_0_block_1_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-heading_medium%22%3E%3Cspan%20style=%22font-size:%2012pt;%22%3E%3Cstrong%3EDocumentazione%20(%7BdocumentazioneCount%7D)%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element1block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-0-Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_7-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;7&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_block_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element1&quot;},{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Nuovo Documento&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;OmniScripts&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;layoutType&quot;:&quot;lightning&quot;,&quot;osName&quot;:&quot;UniDocumento/Nuovo/Italian&quot;,&quot;flyoutLwc&quot;:&quot;uni-documento-nuovo-italian&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;ContextId&quot;:&quot;{recordId}&quot;,&quot;docTypesSOC1&quot;:&quot;{docTypesSOC1}&quot;,&quot;docTypesSOC4&quot;:&quot;{docTypesSOC4}&quot;}},&quot;key&quot;:&quot;*************-ah9fnweav&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;outline-brand&quot;,&quot;flyoutChannel&quot;:&quot;&quot;,&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;},&quot;ariaLabel&quot;:&quot;Nuovo Documento&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_4-of-12 slds-medium-size_4-of-12 slds-small-size_4-of-12 slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center slds-p-top_x-small &quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;height&quot;:&quot;&quot;},&quot;elementLabel&quot;:&quot;Block-0-Action-7&quot;,&quot;key&quot;:&quot;element_element_block_0_0_action_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_4-of-12 slds-medium-size_4-of-12 slds-small-size_4-of-12 slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center slds-p-top_x-small &quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;height&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;parsedProperty&quot;:{&quot;label&quot;:&quot;Nuovo Documento&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:{&quot;documentazioneCount&quot;:4,&quot;documentDetails&quot;:[{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.572Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;nomeCompagnia&quot;:&quot;unipolsai&quot;,&quot;numeroDocumento&quot;:&quot;Documento 1&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;comuneEmissione&quot;:&quot;Firenze&quot;,&quot;stato&quot;:&quot;Italia&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;15-12-2025&quot;,&quot;ciu&quot;:&quot;1719&quot;,&quot;provincia&quot;:&quot;Firenze&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000Ct1J8QAJ&quot;,&quot;enteEmissione&quot;:&quot;Ministero&quot;},{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.573Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;nomeCompagnia&quot;:&quot;unipolsai&quot;,&quot;numeroDocumento&quot;:&quot;123ca123&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;comuneEmissione&quot;:&quot;Napoli&quot;,&quot;stato&quot;:&quot;Italia&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;10-01-2030&quot;,&quot;ciu&quot;:&quot;1719&quot;,&quot;provincia&quot;:&quot;Napoli&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000D6eHpQAJ&quot;,&quot;dataValidita&quot;:&quot;10-01-2020&quot;,&quot;enteEmissione&quot;:&quot;COMUNE&quot;},{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.576Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;24-12-2025&quot;,&quot;nomeCompagnia&quot;:&quot;unisalute&quot;,&quot;ciu&quot;:&quot;********&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000DEtmrQAD&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006dsrVQAQ&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;},{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.577Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;23-12-2025&quot;,&quot;nomeCompagnia&quot;:&quot;unisalute&quot;,&quot;ciu&quot;:&quot;********&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000DAQLBQA5&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006dsrVQAQ&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;}]},&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:{&quot;documentazioneCount&quot;:4,&quot;documentDetails&quot;:[{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.572Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;nomeCompagnia&quot;:&quot;unipolsai&quot;,&quot;numeroDocumento&quot;:&quot;Documento 1&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;comuneEmissione&quot;:&quot;Firenze&quot;,&quot;stato&quot;:&quot;Italia&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;15-12-2025&quot;,&quot;ciu&quot;:&quot;1719&quot;,&quot;provincia&quot;:&quot;Firenze&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000Ct1J8QAJ&quot;,&quot;enteEmissione&quot;:&quot;Ministero&quot;},{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.573Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;nomeCompagnia&quot;:&quot;unipolsai&quot;,&quot;numeroDocumento&quot;:&quot;123ca123&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;comuneEmissione&quot;:&quot;Napoli&quot;,&quot;stato&quot;:&quot;Italia&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;10-01-2030&quot;,&quot;ciu&quot;:&quot;1719&quot;,&quot;provincia&quot;:&quot;Napoli&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000D6eHpQAJ&quot;,&quot;dataValidita&quot;:&quot;10-01-2020&quot;,&quot;enteEmissione&quot;:&quot;COMUNE&quot;},{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.576Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;24-12-2025&quot;,&quot;nomeCompagnia&quot;:&quot;unisalute&quot;,&quot;ciu&quot;:&quot;********&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000DEtmrQAD&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006dsrVQAQ&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;},{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.577Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;23-12-2025&quot;,&quot;nomeCompagnia&quot;:&quot;unisalute&quot;,&quot;ciu&quot;:&quot;********&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000DAQLBQA5&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006dsrVQAQ&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;}]},&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;OmniScripts&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;layoutType&quot;:&quot;lightning&quot;,&quot;osName&quot;:&quot;UniDocumento/Nuovo/Italian&quot;,&quot;flyoutLwc&quot;:&quot;uni-documento-nuovo-italian&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;ContextId&quot;:&quot;{recordId}&quot;,&quot;docTypesSOC1&quot;:&quot;{docTypesSOC1}&quot;,&quot;docTypesSOC4&quot;:&quot;{docTypesSOC4}&quot;}},&quot;key&quot;:&quot;*************-ah9fnweav&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;outline-brand&quot;,&quot;flyoutChannel&quot;:&quot;&quot;,&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;},&quot;ariaLabel&quot;:&quot;Nuovo Documento&quot;},&quot;datasourceKey&quot;:&quot;state0element0block_element2&quot;},{&quot;key&quot;:&quot;element_element_block_0_0_block_3_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;width:100%;\npadding:0px;\npadding-top: 3px;\nmargin-top:0px;&quot;,&quot;style&quot;:&quot;      \n     height:46px;    width:100%;\npadding:0px;\npadding-top: 3px;\nmargin-top:0px;&quot;,&quot;height&quot;:&quot;46px&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_0_0_block_3_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%22%3E%3Cstrong%3ETipologia%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;height: 40px&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-top_x-small &quot;,&quot;style&quot;:&quot;     border-top: #e5e5e5 1px solid;border-right: #e5e5e5 1px solid;border-bottom: #e5e5e5 1px solid;border-left: #e5e5e5 1px solid; \n         height: 40px&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Block-5-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;height: 40px&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-top_x-small &quot;,&quot;style&quot;:&quot;     border-top: #e5e5e5 1px solid;border-right: #e5e5e5 1px solid;border-bottom: #e5e5e5 1px solid;border-left: #e5e5e5 1px solid; \n         height: 40px&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element3block_element0&quot;},{&quot;key&quot;:&quot;element_element_element_block_0_0_block_3_0_outputField_1_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%22%3E%3Cstrong%3EData%20Scadenza%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;height: 40px&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-top_x-small &quot;,&quot;style&quot;:&quot;     border-top: #e5e5e5 1px solid;border-right: #e5e5e5 1px solid;border-bottom: #e5e5e5 1px solid;border-left: #e5e5e5 1px solid; \n         height: 40px&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Block-5-Text-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;height: 40px&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-top_x-small &quot;,&quot;style&quot;:&quot;     border-top: #e5e5e5 1px solid;border-right: #e5e5e5 1px solid;border-bottom: #e5e5e5 1px solid;border-left: #e5e5e5 1px solid; \n         height: 40px&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element3block_element1&quot;},{&quot;key&quot;:&quot;element_element_element_block_0_0_block_3_0_outputField_2_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%22%3E%3Cstrong%3ENome%20Compagnia%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;height: 40px&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-top_x-small &quot;,&quot;style&quot;:&quot;     border-top: #e5e5e5 1px solid;border-right: #e5e5e5 1px solid;border-bottom: #e5e5e5 1px solid;border-left: #e5e5e5 1px solid; \n         height: 40px&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Block-5-Text-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;height: 40px&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-top_x-small &quot;,&quot;style&quot;:&quot;     border-top: #e5e5e5 1px solid;border-right: #e5e5e5 1px solid;border-bottom: #e5e5e5 1px solid;border-left: #e5e5e5 1px solid; \n         height: 40px&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element3block_element2&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%22%3E%3Cstrong%3ENumero%20Documento%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_3-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;height: 40px&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-top_x-small &quot;,&quot;style&quot;:&quot;     border-top: #e5e5e5 1px solid;border-right: #e5e5e5 1px solid;border-bottom: #e5e5e5 1px solid;border-left: #e5e5e5 1px solid; \n         height: 40px&quot;},&quot;elementLabel&quot;:&quot;Block-0-Block-5-Text-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_3-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;height: 40px&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-top_x-small &quot;,&quot;style&quot;:&quot;     border-top: #e5e5e5 1px solid;border-right: #e5e5e5 1px solid;border-bottom: #e5e5e5 1px solid;border-left: #e5e5e5 1px solid; \n         height: 40px&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_3_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element3block_element3&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[],&quot;elementLabel&quot;:&quot;Block-4&quot;,&quot;key&quot;:&quot;element_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element3block_element4&quot;},{&quot;key&quot;:&quot;element_element_element_block_0_0_block_3_0_block_5_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[],&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Block-5-Block-4&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element3block_element5&quot;}],&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Block-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;width:100%;\npadding:0px;\npadding-top: 3px;\nmargin-top:0px;&quot;,&quot;style&quot;:&quot;      \n     height:46px;    width:100%;\npadding:0px;\npadding-top: 3px;\nmargin-top:0px;&quot;,&quot;height&quot;:&quot;46px&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element3&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:12,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:12,&quot;small&quot;:12},&quot;padding&quot;:[],&quot;class&quot;:&quot;slds-theme_default &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;background-color: #f3f3f3;&quot;,&quot;theme&quot;:&quot;theme_default&quot;,&quot;style&quot;:&quot;      \n         background-color: #f3f3f3;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1758278011326-985&quot;,&quot;datasourceKey&quot;:&quot;state0element0&quot;},{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;UniDocumentazioneRow&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;&quot;,&quot;selectedState&quot;:&quot;Active&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;FlexCard-1&quot;,&quot;uKey&quot;:&quot;1758278011326-788&quot;,&quot;datasourceKey&quot;:&quot;state0element1&quot;},{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Visualizza Tutto&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;Documentazione_Details&quot;},&quot;hasExtraParams&quot;:true,&quot;targetParams&quot;:{&quot;c__AccountId&quot;:&quot;{recordId}&quot;}},&quot;key&quot;:&quot;*************-o2o5tz3gx&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;flyoutChannel&quot;:&quot;&quot;,&quot;flyoutDetails&quot;:{},&quot;displayAsButton&quot;:true},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Action-2&quot;,&quot;uKey&quot;:&quot;*************-986&quot;,&quot;datasourceKey&quot;:&quot;state0element2&quot;}]}},&quot;childCards&quot;:[&quot;UniDocumentazioneRow&quot;],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDS_Documents&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{recordId}&quot;,&quot;Status&quot;:&quot;&apos;V&apos;&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;Status\&quot;:\&quot;{Status}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;Status&quot;,&quot;val&quot;:&quot;V&quot;,&quot;id&quot;:5}]},&quot;title&quot;:&quot;UniDocumentazione&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfSS_Documentazione&quot;,&quot;Id&quot;:&quot;0Rb9O000003ASFqSAO&quot;,&quot;MasterLabel&quot;:&quot;cfSS_Documentazione&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlObject&quot;:{&quot;isExplicitImport&quot;:false,&quot;targetConfigs&quot;:&quot;CiAgICAgICAgPHRhcmdldENvbmZpZyB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX1JlY29yZFBhZ2UiPjxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPjxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIi8+PHByb3BlcnR5IG5hbWU9ImNmVXNlcklkIiB0eXBlPSJTdHJpbmciIGxhYmVsPSJVc2VyIElEIi8+PC90YXJnZXRDb25maWc+PHRhcmdldENvbmZpZyB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX0FwcFBhZ2UiPjxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPjxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIi8+PHByb3BlcnR5IG5hbWU9ImNmVXNlcklkIiB0eXBlPSJTdHJpbmciIGxhYmVsPSJVc2VyIElEIi8+PC90YXJnZXRDb25maWc+PHRhcmdldENvbmZpZyB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX0hvbWVQYWdlIj48cHJvcGVydHkgbmFtZT0iY2ZVc2VySWQiIHR5cGU9IlN0cmluZyIgbGFiZWw9IlVzZXIgSUQiLz48L3RhcmdldENvbmZpZz4KICAgICAgICA=&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]}},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfUserId&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;User ID&quot;}}]},{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfUserId&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;User ID&quot;}}]},{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__HomePage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfUserId&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;User ID&quot;}}]}],&quot;uniqueKey&quot;:&quot;&quot;,&quot;sessionVars&quot;:[{&quot;name&quot;:&quot;UserId&quot;,&quot;isApi&quot;:true,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3}]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;documentazioneCount&quot;:4,&quot;documentDetails&quot;:[{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.572Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;nomeCompagnia&quot;:&quot;unipolsai&quot;,&quot;numeroDocumento&quot;:&quot;Documento 1&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;comuneEmissione&quot;:&quot;Firenze&quot;,&quot;stato&quot;:&quot;Italia&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;15-12-2025&quot;,&quot;ciu&quot;:&quot;1719&quot;,&quot;provincia&quot;:&quot;Firenze&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000Ct1J8QAJ&quot;,&quot;enteEmissione&quot;:&quot;Ministero&quot;},{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.573Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;nomeCompagnia&quot;:&quot;unipolsai&quot;,&quot;numeroDocumento&quot;:&quot;123ca123&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;comuneEmissione&quot;:&quot;Napoli&quot;,&quot;stato&quot;:&quot;Italia&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;10-01-2030&quot;,&quot;ciu&quot;:&quot;1719&quot;,&quot;provincia&quot;:&quot;Napoli&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000D6eHpQAJ&quot;,&quot;dataValidita&quot;:&quot;10-01-2020&quot;,&quot;enteEmissione&quot;:&quot;COMUNE&quot;},{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.576Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;24-12-2025&quot;,&quot;nomeCompagnia&quot;:&quot;unisalute&quot;,&quot;ciu&quot;:&quot;********&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000DEtmrQAD&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006dsrVQAQ&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;},{&quot;ambito&quot;:&quot;anagrafica&quot;,&quot;timestamp&quot;:&quot;2025-07-28T15:38:16.577Z&quot;,&quot;status&quot;:&quot;V&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;dataScadenza&quot;:&quot;23-12-2025&quot;,&quot;nomeCompagnia&quot;:&quot;unisalute&quot;,&quot;ciu&quot;:&quot;********&quot;,&quot;ContentVersionId&quot;:&quot;0689O00000DAQLBQA5&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006dsrVQAQ&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;}]}</sampleDataSourceResponse>
    <stylingConfiguration>{}</stylingConfiguration>
    <versionNumber>24</versionNumber>
</OmniUiCard>
