import { LightningElement, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getUserNetworkData from '@salesforce/apex/NetworkUserHelper.getUserNetworkData';
import getIdAziendaForCurrentUser from '@salesforce/apex/NetworkUserHelper.getIdAziendaForCurrentUser';
import getListAgencyIdFromNetworkUserId from '@salesforce/apex/NetworkUserHelper.getListAgencyIdFromNetworkUserId';
import setPreferredNetworkUser from '@salesforce/apex/NetworkUserHelper.setPreferredNetworkUser';
import setIdAziendaForCurrentUser from '@salesforce/apex/NetworkUserHelper.setIdAziendaForCurrentUser';
import FEDERATION_ID_WARNING from '@salesforce/label/c.FederationIdWarningMsg';


export default class SetDefaultUser extends LightningElement {
    @track utenze = [];
    @track originalUtenze = [];
    @track loading = false;
    @track showFederationWarning = false;
    @track federationWarningMsg = FEDERATION_ID_WARNING; // Variabile dinamica
    @track selectedAgenzia = '';


    agenzieOptions = [];

    get federationWarningMsg() {
        return this.federationWarningMsg;
    }

    get selectedAgenziaLabel() {
        const found = this.agenzieOptions.find(opt => opt.value === this.selectedAgenzia);
        return found ? found.label : '';
    }


    connectedCallback() {
        this.loadData();
         // Recupera IdAzienda__c dell'utente corrente e imposta la selezione
        getIdAziendaForCurrentUser()
            .then(idAzienda => {
                if (idAzienda) {
                    this.selectedAgenzia = idAzienda;
                }
            })
            .catch(error => {
                console.error('[connectedCallback] Errore getIdAziendaForCurrentUser:', error, error && error.body);
                // Ignora errori, lascia selectedAgenzia vuoto
            });
        this.loadAgenzie();
       
    }

    loadAgenzie() {
        getListAgencyIdFromNetworkUserId()
            .then(data => {
                console.log('@@DEBUG agenzie: ', data);
                console.log('@@DEBUG agenzie Array.isArray(data): ', Array.isArray(data));
                if (data && Array.isArray(data) && data.length > 0) {
                    this.agenzieOptions = data.map(acc => ({
                        label: acc.Name ,
                        value: acc.Id
                    }));
                    // Imposta la prima agenzia come default se non già selezionata
                    if (!this.selectedAgenzia) {
                        this.selectedAgenzia = this.agenzieOptions[0].value;
                        // Aggiorna anche il campo IdAzienda dello user
                        setIdAziendaForCurrentUser({ idAzienda: this.selectedAgenzia });
                    }
                } else {
                    this.agenzieOptions = [];
                }
            })
            .catch(error => {
                this.agenzieOptions = [];
                // Optionally handle error
                console.error('[loadAgenzie] Error fetching agencies:', error, error && error.body);
                this.loading = false;
                if (error && error.body && error.body.message) {
                    this.federationWarningMsg = "ERRORE CRUSCOTTO: " + error.body.message;
                } else {
                    this.federationWarningMsg = FEDERATION_ID_WARNING;
                }
                this.showFederationWarning = true;
            });
    }

    loadData() {
        this.loading = true;
        this.showFederationWarning = false;
        console.log('@@DEBUG: Start caricamento utenze');
        getUserNetworkData()
            .then(data => {
                console.log('@@DEBUG data: ', data);
                if (!data || data.length === 0) {
                    // FederationIdentifier is blank or no utenze found
                    this.showFederationWarning = true;
                    this.utenze = [];
                    this.loading = false;
                    return;
                }
                this.utenze = data.map(item => ({
                    compagnia: item.companyName,
                    companyId: item.companyId,
                    options: [{ label: '— Nessuno —', value: '' }, ...item.options],
                    selected: item.selectedUserId
                }));
                this.originalUtenze = JSON.parse(JSON.stringify(this.utenze));
                this.loading = false;
            })
            .catch(error => {
                this.loading = false;
                console.error('[loadData] Errore getUserNetworkData:', error, error && error.body);
                if (error && error.body && error.body.message) {
                    this.federationWarningMsg = "ERRORE CRUSCOTTO: " + error.body.message;
                } else {
                    this.federationWarningMsg = FEDERATION_ID_WARNING;
                }
                this.showFederationWarning = true;
                this.utenze = [];
            });
    }

    handleChange(event) {
        const companyId = event.target.name;
        const selectedUserId = event.detail.value;
        this.utenze = this.utenze.map(u => 
            u.companyId === companyId ? { ...u, selected: selectedUserId } : u
        );
    }

    handleAgenziaChange(event) {
        this.selectedAgenzia = event.detail.value;
    }

    handleConferma() {
        this.loading = true;
        console.log('[handleConferma] utenze:', this.utenze);
        console.log('[handleConferma] selectedAgenzia:', this.selectedAgenzia);
        const promises = this.utenze.map(u => {
            if (u.selected) {
                // Se è selezionato un utente, setta come preferred
                return setPreferredNetworkUser({ networkUserId: u.selected, companyId: u.companyId });
            } else {
                // Se è selezionato "— Nessuno —", setta preferred__c a false per la company
                return setPreferredNetworkUser({ networkUserId: null, companyId: u.companyId });
            }
        });
        // Dopo aver gestito le utenze, aggiorna anche IdAzienda sull'utente
        Promise.all(promises)
            .then(() => {
                console.log('[handleConferma] Chiamo setIdAziendaForCurrentUser con:', this.selectedAgenzia);
                // Chiamata Apex per aggiornare IdAzienda__c
                return setIdAziendaForCurrentUser({ idAzienda: this.selectedAgenzia });
            })
            .then((result) => {
                console.log('[handleConferma] Risposta setIdAziendaForCurrentUser:', result);
                this.loading = false;
                // Mostra il toast di successo
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Successo',
                        message: 'Salvataggio completato con successo!',
                        variant: 'success'
                    })
                );
                // Attendi un secondo prima di ricaricare la pagina
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            })
            .catch(error => {
                this.loading = false;
                console.error('[handleConferma] Errore:', error);
                // Mostra il toast di errore
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Errore',
                        message: 'Si è verificato un errore durante il salvataggio.',
                        variant: 'error'
                    })
                );
            });
    }

    handleAnnulla() {
        this.utenze = JSON.parse(JSON.stringify(this.originalUtenze));
    }
}