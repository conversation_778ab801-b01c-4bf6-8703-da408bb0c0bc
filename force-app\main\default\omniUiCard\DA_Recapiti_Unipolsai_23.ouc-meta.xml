<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:null,&quot;value&quot;:{},&quot;orderBy&quot;:{},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3}]}}</dataSourceConfig>
    <description><PERSON>: 1464042</description>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>DA_Recapiti</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;,&quot;label&quot;:&quot;around:none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-around_none &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_0_0_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cstrong%3E%3Cspan%20style=%22font-size:%2014pt;%22%3ERecapiti%3C/span%3E%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;}},&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element0block_element0&quot;},{&quot;key&quot;:&quot;element_element_element_block_0_0_block_0_0_action_1_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Aggiungi recapito&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744038535691&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;close_modal&quot;,&quot;cardName&quot;:&quot;DA_Recapiti_Agenzia_Modifica&quot;,&quot;flyoutLwc&quot;:&quot;DA_Recapiti_Agenzia_Modifica&quot;,&quot;cardNode&quot;:&quot;{record.inserisci}&quot;},&quot;key&quot;:&quot;1737128274127-n32li0wg1&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}},&quot;flyoutChannel&quot;:&quot;close_modal&quot;,&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;},&quot;disabled&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Action-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element0block_element1&quot;},{&quot;key&quot;:&quot;element_element_element_block_0_0_block_0_0_action_2_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Modifica&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748532516682&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;close_modal&quot;,&quot;cardName&quot;:&quot;DA_Recapiti_Modifica&quot;,&quot;flyoutLwc&quot;:&quot;DA_Recapiti_Modifica&quot;,&quot;cardNode&quot;:&quot;{record}&quot;,&quot;flyoutContainerClass&quot;:&quot;DA_Recapiti_Modifica&quot;},&quot;key&quot;:&quot;1737125105384-njuj58jvm&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}},&quot;flyoutChannel&quot;:&quot;close_modal&quot;,&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-6&quot;,&quot;field&quot;:&quot;checkCp.customerPermissionCheck.CancellazioneRecapiti&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Action-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element0block_element2&quot;}],&quot;elementLabel&quot;:&quot;Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_block_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1755675192299-98&quot;,&quot;datasourceKey&quot;:&quot;state0element0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Recapiti di agenzia&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Cellulare {UsageCellConcat}&quot;,&quot;fieldName&quot;:&quot; &quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_0_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element0block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-0&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-825&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;RecAgenziaCellPersonale&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_1_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_1_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element1block_element0&quot;,&quot;uKey&quot;:&quot;1758287189965-819&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-1&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-518&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element1&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot; &quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Email {UsageMailConcat}&quot;,&quot;fieldName&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_2_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_2_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element2block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-2&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-912&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element2&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;RecAgenziaEmailPers&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_3_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element3block_element0&quot;,&quot;parsedProperty&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:{},&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;RecAgenziaEmailPers&quot;}}],&quot;elementLabel&quot;:&quot;Block-1-Block-3&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-94&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element3&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot; &quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Fax {UsageFaxConcat}&quot;,&quot;fieldName&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_4_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element4block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-4&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-173&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element4&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;RecAgenziaFax&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_5_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_5_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element5block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-5&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_5_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-388&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element5&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot; &quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Posta certificata {UsagePECConcat}&quot;,&quot;fieldName&quot;:&quot; &quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_6_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_6_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element6block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-6&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_6_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-563&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element6&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;RecAgenziaPEC&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_7_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_7_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element7block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-7&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_7_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-661&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element7&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Numero telefonico {UsageTELConcat}&quot;,&quot;fieldName&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_8_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_8_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element8block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-8&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_8_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-827&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element8&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;RecAgenziaTelefono&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_9_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_9_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element9block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-9&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_9_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-533&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element9&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Tel/Cell Referente {UsageTELREFConcat}&quot;,&quot;fieldName&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_10_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_10_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element10block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-10&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_10_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-859&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element10&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;RecAgenziaTelefonoReferente&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_11_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_11_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element11block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-11&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_11_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;uKey&quot;:&quot;1755675193150-398&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element11&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_block_12_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-4&quot;,&quot;field&quot;:&quot;ShowTelefonoReferente&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Referente&quot;,&quot;fieldName&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Field-12&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_12_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_12_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element12block_element0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-12&quot;,&quot;uKey&quot;:&quot;1755675193150-800&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element12&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_block_13_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-4&quot;,&quot;field&quot;:&quot;ShowTelefonoReferente&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;RecAgenziaReferente&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Field-13&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false,&quot;isopen&quot;:true}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_13_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_13_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element13block_element0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-13&quot;,&quot;uKey&quot;:&quot;1755675193150-626&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element13&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_block_14_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Vedi tutto&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:true,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;font-style: italic;&quot;,&quot;style&quot;:&quot;      \n         font-style: italic;&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_1_0_block_14_0_childCardPreview_0_0&quot;,&quot;name&quot;:&quot;Flexcard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;DA_AltriRecapitiAgenziaContainer&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;&quot;,&quot;selectedState&quot;:&quot;NoContacts&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_14_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element14block_element0&quot;,&quot;uKey&quot;:&quot;1755675293524-47&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-14-Flexcard-0&quot;,&quot;parsedProperty&quot;:{&quot;cardName&quot;:&quot;DA_AltriRecapitiAgenziaContainer&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;&quot;,&quot;selectedState&quot;:&quot;NoContacts&quot;}}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element14&quot;,&quot;uKey&quot;:&quot;1755675254015-746&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-14&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;font-style: italic;&quot;,&quot;style&quot;:&quot;      \n         font-style: italic;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1755675192300-884&quot;,&quot;datasourceKey&quot;:&quot;state0element1&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Area riservata&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;inserisci.userInfo.companyId&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;SOC_4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_2_0_block_0_0_outputField_1_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Cellulare&quot;,&quot;fieldName&quot;:&quot;ARCellulare&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element0block_element0&quot;,&quot;uKey&quot;:&quot;1758622022577-557&quot;,&quot;elementLabel&quot;:&quot;Block-1-Field-1&quot;}],&quot;elementLabel&quot;:&quot;Block-1&quot;,&quot;key&quot;:&quot;element_element_block_2_0_block_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element0&quot;,&quot;uKey&quot;:&quot;1758622017466-653&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Email&quot;,&quot;fieldName&quot;:&quot;AREmail&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Field-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_3_0_block_1_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_1_0&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element1block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;,&quot;key&quot;:&quot;element_element_block_2_0_block_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element1&quot;,&quot;uKey&quot;:&quot;1758622017466-922&quot;}],&quot;elementLabel&quot;:&quot;Block-4&quot;,&quot;uKey&quot;:&quot;1755675192300-860&quot;,&quot;datasourceKey&quot;:&quot;state0element2&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Miglior recapito&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-15&quot;,&quot;field&quot;:&quot;inserisci.userInfo.companyId&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;SOC_4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Cellulare&quot;,&quot;fieldName&quot;:&quot;MigliorRecapitoCell&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-3&quot;,&quot;field&quot;:&quot;MigliorRecCellulareStato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Certified&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-4&quot;,&quot;field&quot;:&quot;MigliorRecCellulareStato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;InError&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-group-789&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-788&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-862&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-899&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-942&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-991&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-20&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom certified-number&quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;,&quot;customClass&quot;:&quot;certified-number&quot;},&quot;elementLabel&quot;:&quot;Block-5-Block-0-Field-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom certified-number&quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;,&quot;customClass&quot;:&quot;certified-number&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_3_0_block_0_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element0&quot;,&quot;uKey&quot;:&quot;1758289289021-941&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Cellulare&quot;,&quot;fieldName&quot;:&quot;MigliorRecapitoCell&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-41&quot;,&quot;field&quot;:&quot;MigliorRecCellulareStato&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;Certified&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-48&quot;,&quot;field&quot;:&quot;MigliorRecCellulareStato&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;InError&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-60&quot;,&quot;field&quot;:&quot;MigliorRecCellulareStato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Unknown&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-group-818&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-817&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-1041&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1085&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1135&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1191&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-82&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-5-Block-0-Field-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false,&quot;isSetForDesignTime&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_3_0_block_0_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element1&quot;,&quot;uKey&quot;:&quot;1758289289021-758&quot;},{&quot;key&quot;:&quot;element_element_element_block_3_0_block_0_0_block_2_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-852&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-1230&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1240&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1253&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1269&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-146&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;height&quot;:&quot;3em&quot;,&quot;style&quot;:&quot;      \n     height:3em;    &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_element_block_4_0_block_0_0_block_2_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-color_success%22%3EIl%20recapito%20&amp;egrave;%20stato%20certificato%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-25&quot;,&quot;field&quot;:&quot;MigliorRecCellulareStato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Certified&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;parentElementKey&quot;:&quot;element_element_element_block_4_0_block_0_0_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-1-Text-1&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element2block_element0&quot;},{&quot;key&quot;:&quot;element_element_element_element_block_4_0_block_0_0_block_2_0_outputField_1_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-color_error%22%3EIl%20recapito%20&amp;egrave;%20errato%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-34&quot;,&quot;field&quot;:&quot;MigliorRecCellulareStato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;InError&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;parentElementKey&quot;:&quot;element_element_element_block_4_0_block_0_0_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-1-Text-0-clone-0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element2block_element1&quot;}],&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-5-Block-0-Block-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;height&quot;:&quot;3em&quot;,&quot;style&quot;:&quot;      \n     height:3em;    &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element2&quot;,&quot;uKey&quot;:&quot;1758289289021-469&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;fieldName&quot;:&quot;RecAgenziaCellPersonale&quot;,&quot;label&quot;:&quot;Cellulare&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-634&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-641&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-651&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-664&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-680&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element3&quot;,&quot;uKey&quot;:&quot;1758287263348-482&quot;,&quot;elementLabel&quot;:&quot;Block-5-Block-0-Field-3&quot;,&quot;key&quot;:&quot;element_element_element_block_3_0_block_0_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_0_0&quot;},{&quot;key&quot;:&quot;element_element_element_block_3_0_block_0_0_block_4_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-106&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-109&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-113&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-118&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-124&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-131&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[],&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element4&quot;,&quot;uKey&quot;:&quot;1758618493829-901&quot;,&quot;elementLabel&quot;:&quot;Block-5-Block-0-Block-5&quot;,&quot;parsedProperty&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:{},&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-106&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-109&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-113&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-118&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-124&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-131&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}}},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Email&quot;,&quot;fieldName&quot;:&quot;MigliorRecEmail&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-77&quot;,&quot;field&quot;:&quot;MigliorRecEmailStato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Certified&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-80&quot;,&quot;field&quot;:&quot;MigliorRecEmailStato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;InError&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-group-1283&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-1282&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-1356&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1393&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1436&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1485&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-209&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom certified-number&quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;,&quot;customClass&quot;:&quot;certified-number&quot;},&quot;elementLabel&quot;:&quot;Block-5-Block-0-Field-6&quot;,&quot;key&quot;:&quot;element_element_element_block_3_0_block_0_0_outputField_5_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_0_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom certified-number&quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;,&quot;customClass&quot;:&quot;certified-number&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element5&quot;,&quot;uKey&quot;:&quot;1758289289021-961&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Email&quot;,&quot;fieldName&quot;:&quot;MigliorRecEmail&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-130&quot;,&quot;field&quot;:&quot;MigliorRecEmailStato&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;Certified&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-133&quot;,&quot;field&quot;:&quot;MigliorRecEmailStato&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;InError&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-142&quot;,&quot;field&quot;:&quot;MigliorRecEmailStato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Unknown&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-group-1312&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-1311&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-1524&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1568&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1618&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1674&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-271&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-5-Block-0-Field-7&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_3_0_block_0_0_outputField_6_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element6&quot;,&quot;uKey&quot;:&quot;1758289289021-970&quot;},{&quot;key&quot;:&quot;element_element_element_block_3_0_block_0_0_block_7_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-1346&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-1713&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1725&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1738&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1754&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-335&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;height&quot;:&quot;3em&quot;,&quot;style&quot;:&quot;      \n     height:3em;    &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_element_block_4_0_block_0_0_block_5_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-color_success%22%3EIl%20recapito%20&amp;egrave;%20stato%20certificato%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-114&quot;,&quot;field&quot;:&quot;MigliorRecEmailStato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Certified&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;parentElementKey&quot;:&quot;element_element_element_block_4_0_block_0_0_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-3-Text-0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element7block_element0&quot;},{&quot;key&quot;:&quot;element_element_element_element_block_4_0_block_0_0_block_5_0_outputField_1_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-color_error%22%3EIl%20recapito%20&amp;egrave;%20errato%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-123&quot;,&quot;field&quot;:&quot;MigliorRecEmailStato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;InError&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;parentElementKey&quot;:&quot;element_element_element_block_4_0_block_0_0_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-3-Text-0-clone-0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element7block_element1&quot;}],&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-5-Block-0-Block-8&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;height&quot;:&quot;3em&quot;,&quot;style&quot;:&quot;      \n     height:3em;    &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element7&quot;,&quot;uKey&quot;:&quot;1758289289021-535&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Email&quot;,&quot;fieldName&quot;:&quot;RecAgenziaEmailPers&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-711&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-718&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-728&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-741&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-757&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-357&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;datasourceKey&quot;:&quot;state0element3block_element0block_element8&quot;,&quot;uKey&quot;:&quot;1758287281004-463&quot;,&quot;elementLabel&quot;:&quot;Block-5-Block-0-Field-9&quot;,&quot;key&quot;:&quot;element_element_element_block_3_0_block_0_0_outputField_8_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_0_0&quot;}],&quot;elementLabel&quot;:&quot;Block-5-Block-0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element0&quot;,&quot;uKey&quot;:&quot;1758287078122-172&quot;,&quot;key&quot;:&quot;element_element_block_3_0_block_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Fonte Cellulare&quot;,&quot;fieldName&quot;:&quot;MigliorRecFonteCellulare&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-1767&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-1774&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1784&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1797&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1813&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-484&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-2-Field-0&quot;,&quot;key&quot;:&quot;element_element_element_block_3_0_block_1_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_1_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element3block_element1block_element0&quot;,&quot;uKey&quot;:&quot;1758290024510-210&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;ANAG&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Fonte Cellulare&quot;,&quot;fieldName&quot;:&quot;ANAG&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-1826&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-1833&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-1843&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-1856&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-1872&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-443&quot;,&quot;field&quot;:&quot;MigliorRecapitoCell&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-5-Block-1-Field-0-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_3_0_block_1_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_1_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element3block_element1block_element1&quot;,&quot;uKey&quot;:&quot;1758290032681-813&quot;},{&quot;key&quot;:&quot;element_element_element_block_3_0_block_1_0_block_2_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;height&quot;:&quot;3em&quot;,&quot;style&quot;:&quot;      \n     height:3em;    &quot;},&quot;children&quot;:[],&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Block-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;height&quot;:&quot;3em&quot;,&quot;style&quot;:&quot;      \n     height:3em;    &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element3block_element1block_element2&quot;,&quot;uKey&quot;:&quot;1758290024510-932&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Fonte Email&quot;,&quot;fieldName&quot;:&quot;MigliorRecFonteEmail&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-1903&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-1910&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1920&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1933&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-1949&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-421&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-2-Field-3&quot;,&quot;key&quot;:&quot;element_element_element_block_3_0_block_1_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_1_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element3block_element1block_element3&quot;,&quot;uKey&quot;:&quot;1758290024511-31&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;ANAG&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Fonte Email&quot;,&quot;fieldName&quot;:&quot;ANAG&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-1962&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-1969&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-1979&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-1992&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&apos;&apos;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-2008&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-386&quot;,&quot;field&quot;:&quot;MigliorRecEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;-&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-5-Block-1-Field-3-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_3_0_block_1_0_outputField_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_3_0_block_1_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element3block_element1block_element4&quot;,&quot;uKey&quot;:&quot;1758290046669-369&quot;}],&quot;elementLabel&quot;:&quot;Block-5-Block-1&quot;,&quot;key&quot;:&quot;element_element_block_3_0_block_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element1&quot;,&quot;uKey&quot;:&quot;1758287078122-243&quot;}],&quot;elementLabel&quot;:&quot;Block-5&quot;,&quot;uKey&quot;:&quot;1755675192300-260&quot;,&quot;datasourceKey&quot;:&quot;state0element3&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Altri recapiti&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-22&quot;,&quot;field&quot;:&quot;inserisci.userInfo.companyId&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;SOC_4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_4_0_childCardPreview_0_0&quot;,&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;DA_AltriRecapitiContainer&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;&quot;,&quot;selectedState&quot;:&quot;NoContacts&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_4_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-FlexCard-0&quot;,&quot;datasourceKey&quot;:&quot;state0element4block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-6&quot;,&quot;uKey&quot;:&quot;1755675192300-669&quot;,&quot;datasourceKey&quot;:&quot;state0element4&quot;}]}},&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[],&quot;childCards&quot;:[&quot;DA_AltriRecapitiAgenziaContainer&quot;,&quot;DA_AltriRecapitiContainer&quot;]}],&quot;dataSource&quot;:{&quot;type&quot;:null,&quot;value&quot;:{},&quot;orderBy&quot;:{},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3}]},&quot;title&quot;:&quot;DA_Recapiti&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfDA_Recapiti_19_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9V000003lW9BSAU&quot;,&quot;MasterLabel&quot;:&quot;cfDA_Recapiti_19_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;events&quot;:[{&quot;eventname&quot;:&quot;closeModal&quot;,&quot;channelname&quot;:&quot;DA_Recapiti&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1737378691416-akwnohduu&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1737378691504&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;close_modal&quot;,&quot;message&quot;:&quot;close&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;DA_Recapiti:closeModal&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}],&quot;globalCSS&quot;:true,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;DA_Recapiti&quot;,&quot;apiVersion&quot;:56,&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyAgdGFyZ2V0cz0ibGlnaHRuaW5nX19BcHBQYWdlIj48cHJvcGVydHkgIG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciPjwvcHJvcGVydHk+PC90YXJnZXRDb25maWc+PHRhcmdldENvbmZpZyAgdGFyZ2V0cz0ibGlnaHRuaW5nX19SZWNvcmRQYWdlIj48cHJvcGVydHkgIG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciPjwvcHJvcGVydHk+PHByb3BlcnR5ICBuYW1lPSJkZWJ1ZyIgdHlwZT0iYm9vbGVhbiI+PC9wcm9wZXJ0eT48L3RhcmdldENvbmZpZz48dGFyZ2V0Q29uZmlnICB0YXJnZXRzPSJsaWdodG5pbmdDb21tdW5pdHlfX0RlZmF1bHQiPjxwcm9wZXJ0eSAgbmFtZT0icmVjb3JkSWQiIHR5cGU9IlN0cmluZyI+PC9wcm9wZXJ0eT48L3RhcmdldENvbmZpZz4=&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;,&quot;lightningCommunity__Page&quot;,&quot;lightningCommunity__Default&quot;]},&quot;isExplicitImport&quot;:false},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;boolean&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightningCommunity__Default&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]}]}</propertySetConfig>
    <stylingConfiguration>{&quot;customStyles&quot;:&quot;.certified-number {\r\n  position: relative;\r\n  padding-left: 24px;\r\n  font-weight: normal;\r\n}\r\n\r\n.certified-number::before {\r\n  content: \&quot;✔\&quot;;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background-color: #2ecc71;\r\n  font-size: 8px;\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: normal;\r\n}\r\n\r\n.not-certified-number {\r\n  position: relative;\r\n  padding-left: 24px;\r\n  font-weight: normal;\r\n}\r\n\r\n.not-certified-number::before {\r\n  content: \&quot;✖\&quot;;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background-color: #e74c3c;\r\n  font-size: 8px;\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: normal;\r\n}\r\n\r\n.block-header-heading, .slds-p-horizontal_x-small {\r\n    background-color: #F3F3F3;\r\n    width: 100%;\r\n}\r\n\r\n.da-recepiti-icon-container .vloc-min-height {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n.da-recapiti-icon-container .field-value::before {\r\n    font-weight: bold;\r\n    width: 20px;\r\n    height: 20px;\r\n    border-radius: 50%;\r\n    border: 2px solid #ffffff;\r\n    box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px;\r\n    font-size: 8pt;\r\n    padding-left: 4px;\r\n    bottom: 11px;\r\n    left: 6px;\r\n    color: white;\r\n    display: block;\r\n    float: left;\r\n    margin-right: 4px;\r\n    margin-top: 2px;\r\n    margin-bottom: 2px;\r\n}\r\n&quot;}</stylingConfiguration>
    <versionNumber>23</versionNumber>
</OmniUiCard>
