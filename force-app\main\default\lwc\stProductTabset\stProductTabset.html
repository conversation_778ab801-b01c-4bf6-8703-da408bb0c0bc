<template>
    <lightning-card class="slds-p-vertical_none">
        <template if:true={isLoading}>
            <lightning-spinner alternative-text="Loading" size="medium"></lightning-spinner>
        </template>
        <div class="slds-p-horizontal_small">
            <template if:false={isLoading}>
                <template if:true={noProducts}>
                    <!-- eventuale messaggio oppure lasciare vuoto -->
                    <p class="mb-3" style="text-align:center;">Non ci sono prodotti associati a questa trattativa</p>
                </template>
                <template if:false={noProducts}>
                    <lightning-tabset active-tab-value={activeProductId}>
                        <template for:each={products} for:item="product">
                            <lightning-tab key={product.id} label={product.productCode} value={product.id}>
<omnistudio-flex-card-standard-runtime-wrapper flexcard-name="ST_ProductContainer" record-id={product.id}></omnistudio-flex-card-standard-runtime-wrapper>                                <!-- <c-cf-s-t-_-product-container cf-product-id={product.id}></c-cf-s-t-_-product-container>
                                 <template if:true={product.quotesAllowed}>
                                    <c-accordion-container-responsive record-id={product.id} is-stored="false"></c-accordion-container-responsive>
                                    <lightning-accordion active-section-name="other" allow-multiple-sections-open>
                                        <lightning-accordion-section name="storedQuotes" label="Altri preventivi">
                                            <c-accordion-container-responsive record-id={product.id} is-stored="true" source={product.channel}></c-accordion-container-responsive>
                                        </lightning-accordion-section>
                                    </lightning-accordion>
                                </template> -->
                            </lightning-tab>
                        </template>
                    </lightning-tabset>
                </template>
            </template>
        </div>
    </lightning-card>
</template>