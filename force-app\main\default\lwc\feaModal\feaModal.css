.accordion {
    background-color: rgb(243, 243, 243);
    padding: 10px;
    cursor: pointer;
    border-radius: 5px;
    margin-bottom: 10px
}

.accordion span {
    margin-left: 5px
}

.icon-container .field-value::before {
    font-weight: bold;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #ffffff;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px;
    font-size: 8pt;
    padding-left: 4px;
    bottom: 11px;
    left: 6px;
    color: white;
    display: block;
    float: left;
    margin-right: 4px;
    margin-top: 2px;
    margin-bottom: 2px;
}

.field-value::before {
    content: "!";
    background-color: #2f844e;
    padding-left: 6px;
}

.field-value::before {
    content: "\2713";
    background-color: #0D9DDA;
}

.font-14 {
    font-size: 14pt
}

.icon-container {
    font-weight: bold;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #ffffff;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px;
    font-size: 8pt;
    padding-left: 4px;
    bottom: 11px;
    left: 6px;
    color: white;
    display: block;
    float: left;
    margin-right: 4px;
    margin-top: 2px;
    margin-bottom: 2px;
    font-weight: bold;
}

.icon-success {
    background-color: #2E804B; 
}

.icon-success::before {
    content: "\2713";
    padding-left: 0;
}

.icon-warning {
    background-color: #F9C029;
}

.icon-warning::before {
    content: "!";
    padding-left: 2px;
}

.icon-error {
    background-color: #FF0000;
}

.icon-error::before {
    content: "X";
    padding-left: 2px;
}

.cursor-pointer {
    cursor: pointer;
}

.pull-right {
    float: right
}