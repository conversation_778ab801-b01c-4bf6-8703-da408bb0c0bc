<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;OpportunityId&quot; : &quot;Text&quot;
}</expectedInputJson>
    <expectedOutputJson>{
  &quot;accountId&quot; : &quot;Text&quot;,
  &quot;accountName&quot; : &quot;Text&quot;,
  &quot;accountSubjectStatus&quot; : &quot;Text&quot;,
  &quot;accountValus&quot; : &quot;Text&quot;,
  &quot;accountBirthdate&quot; : &quot;Text&quot;,
  &quot;accountMobilePhone&quot; : &quot;Text&quot;,
  &quot;accountEmail&quot; : &quot;Text&quot;,
  &quot;accountCity&quot; : &quot;Text&quot;,
  &quot;accountStreet&quot; : &quot;Text&quot;,
  &quot;accountFiscalCode&quot; : &quot;Text&quot;,
  &quot;accountPurchasePower&quot; : &quot;Text&quot;,
  &quot;opportunityStage&quot; : &quot;Text&quot;,
  &quot;opportunityIsClosed&quot; : &quot;Boolean&quot;,
  &quot;societyRelationship&quot; : &quot;Text&quot;,
  &quot;agencyRelationship&quot; : &quot;Text&quot;
}</expectedOutputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>OpportunitySubjectGetData</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Opportunity:AccountId</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem23</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>6.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>societyRelationship</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem26</globalKey>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>6.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>societyRelationship</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem25</globalKey>
        <inputObjectName>Quote</inputObjectName>
        <inputObjectQuerySequence>9.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Quote</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>societyRelationship:Id</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem17</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailsAddress</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Opportunity:Agency__c</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem31</globalKey>
        <inputFieldName>FinServ__RelatedAccount__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>ClientAgencyRelationship</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem54</globalKey>
        <inputFieldName>Account:PersonMailingAddress</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAddress</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Cliente&apos;</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem28</globalKey>
        <inputFieldName>Name</inputFieldName>
        <inputObjectName>FinServ__ReciprocalRole__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>ClientAgencyRole</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>OpportunityId</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem12</globalKey>
        <inputFieldName>Parent__c</inputFieldName>
        <inputObjectName>Opportunity</inputObjectName>
        <inputObjectQuerySequence>8.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>FirstProduct</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>ClientAgencyRelationship:Id</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem27</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>7.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>RelationshipDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem49</globalKey>
        <inputFieldName>Account:PersonMailingStreet</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountStreet</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>OpportunityId</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem30</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Opportunity</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Opportunity</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Individual&apos;</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem11</globalKey>
        <inputFieldName>RecordType.DeveloperName</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailsAddress</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Business&apos;</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem29</globalKey>
        <inputFieldName>RecordType.DeveloperName</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailsAddress</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem48</globalKey>
        <inputFieldName>Opportunity:StageName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>opportunityStage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>FirstProduct:Id</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem24</globalKey>
        <inputFieldName>OpportunityId</inputFieldName>
        <inputObjectName>Quote</inputObjectName>
        <inputObjectQuerySequence>9.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Quote</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem14</globalKey>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>ClientAgencyRelationship</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>ORDER BY</filterOperator>
        <filterValue>CreatedDate ASC</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem21</globalKey>
        <inputObjectName>Quote</inputObjectName>
        <inputObjectQuerySequence>9.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Quote</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>ClientAgencyRelationship:Id</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem2</globalKey>
        <inputFieldName>MasterRecordId__c</inputFieldName>
        <inputObjectName>AccountKPI__c</inputObjectName>
        <inputObjectQuerySequence>12.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountKPI_Valus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem58</globalKey>
        <inputFieldName>Account:PersonEmail</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem39</globalKey>
        <inputFieldName>BlankPurchasePowerValue</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountPurchasePower</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>societyRelationship:Id</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem16</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailsAddress</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Account:PersonBirthdate &quot;dd/MM/yyyy&quot; &quot;Europe/Rome&quot; FORMATDATETIME</formulaConverted>
        <formulaExpression>FORMATDATETIME(Account:PersonBirthdate, &quot;dd/MM/yyyy&quot;, &quot;Europe/Rome&quot;)</formulaExpression>
        <formulaResultPath>FormattedBirthDate</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem53</globalKey>
        <inputFieldName>AccountDetailsAddress:Dug__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Dug</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem38</globalKey>
        <inputFieldName>Account:FinServ__TaxId__pc</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountFiscalCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountKPI_Valus:Value__c ISBLANK &apos;-&apos; var:AccountKPI_Valus:Value__c IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(AccountKPI_Valus:Value__c), &apos;-&apos;, AccountKPI_Valus:Value__c)</formulaExpression>
        <formulaResultPath>BlankValusValue</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem15</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>societyRecord:Id</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem33</globalKey>
        <inputFieldName>FinServ__RelatedAccount__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>6.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>societyRelationship</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem52</globalKey>
        <inputFieldName>societyRelationship:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>societyRelationship</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem35</globalKey>
        <inputFieldName>FormattedBirthDate</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountBirthdate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem18</globalKey>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailsAddress</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem34</globalKey>
        <inputFieldName>Account:Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountName</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem55</globalKey>
        <inputFieldName>ClientAgencyRelationship:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>agencyRelationship</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>ClientAgencyRole:Id</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem32</globalKey>
        <inputFieldName>FinServ__Role__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>ClientAgencyRelationship</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem40</globalKey>
        <inputFieldName>AccountDetailsAddress:StreetNumber__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>number</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>ORDER BY</filterOperator>
        <filterValue>CreatedDate ASC</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem6</globalKey>
        <inputObjectName>Opportunity</inputObjectName>
        <inputObjectQuerySequence>8.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>FirstProduct</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem59</globalKey>
        <inputFieldName>Quote:Phone</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Quote:Phone</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem43</globalKey>
        <inputFieldName>AccountDetailsAddress:Street__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Street</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Opportunity:AccountId</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem20</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Account</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Agenzia&apos;</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem5</globalKey>
        <inputFieldName>FinServ__InverseRole__c</inputFieldName>
        <inputObjectName>FinServ__ReciprocalRole__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>ClientAgencyRole</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem57</globalKey>
        <inputFieldName>Quote:Email</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Quote:Email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem42</globalKey>
        <inputFieldName>AccountDetailsAddress:City__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>City</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Opportunity:AccountId</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem19</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>ClientAgencyRelationship</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem0</globalKey>
        <inputObjectName>Opportunity</inputObjectName>
        <inputObjectQuerySequence>8.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>FirstProduct</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem56</globalKey>
        <inputFieldName>AccountDetail:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>AccountDetails:Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem37</globalKey>
        <inputFieldName>Indirizzo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountdetails_residenza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>&apos;/\/\/&apos;</formulaConverted>
        <formulaExpression>&apos; &apos;</formulaExpression>
        <formulaResultPath>BlankSubjectStatus</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem22</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem36</globalKey>
        <inputFieldName>AccountDetailsAddress:State__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>state</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem46</globalKey>
        <inputFieldName>accSubjectData_temp</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountSubjectStatus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;PP_AGENZIA_SOCIETA_STATO&apos;</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem4</globalKey>
        <inputFieldName>Key__c</inputFieldName>
        <inputObjectName>AccountKPI__c</inputObjectName>
        <inputObjectQuerySequence>11.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountKPI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem41</globalKey>
        <inputFieldName>Opportunity:IsClosed</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>opportunityIsClosed</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;CRMA_VALUS&apos;</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem3</globalKey>
        <inputFieldName>Key__c</inputFieldName>
        <inputObjectName>AccountKPI__c</inputObjectName>
        <inputObjectQuerySequence>12.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountKPI_Valus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:AccountKPI:Value__c &apos;Cliente&apos; == var:AccountKPI:Value__c &apos;Prospect&apos; IF</formulaConverted>
        <formulaExpression>IF(AccountKPI:Value__c == &apos;Cliente&apos;, AccountKPI:Value__c, &apos;Prospect&apos;)</formulaExpression>
        <formulaResultPath>accSubjectData_temp</formulaResultPath>
        <formulaSequence>6.0</formulaSequence>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem7</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem44</globalKey>
        <inputFieldName>AccountDetailsAddress:Dus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Dus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>ClientAgencyRelationship:Id</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem10</globalKey>
        <inputFieldName>MasterRecordId__c</inputFieldName>
        <inputObjectName>AccountKPI__c</inputObjectName>
        <inputObjectQuerySequence>11.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountKPI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem47</globalKey>
        <inputFieldName>AccountDetailsAddress:PostalCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>PostalCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetailsAddress:Street__c ISBLANK | var:AccountDetailsAddress:City__c ISBLANK &amp;&amp; | var:AccountDetailsAddress:PostalCode__c ISBLANK &amp;&amp; | var:AccountDetailsAddress:State__c ISBLANK &amp;&amp; &quot;-&quot; | var:AccountDetailsAddress:Street__c &apos;,/\/\/&apos; + var:AccountDetailsAddress:City__c + &apos;,/\/\/&apos; + var:AccountDetailsAddress:PostalCode__c &apos;,/\/\/&apos; + var:AccountDetailsAddress:State__c + CONCAT IF</formulaConverted>
        <formulaExpression>IF((ISBLANK(AccountDetailsAddress:Street__c) &amp;&amp; ISBLANK(AccountDetailsAddress:City__c) &amp;&amp; ISBLANK(AccountDetailsAddress:PostalCode__c)&amp;&amp; ISBLANK(AccountDetailsAddress:State__c)), &quot;-&quot;, (CONCAT(AccountDetailsAddress:Street__c+&apos;, &apos;+AccountDetailsAddress:City__c+&apos;, &apos;+AccountDetailsAddress:PostalCode__c&apos;, &apos;+AccountDetailsAddress:State__c)))</formulaExpression>
        <formulaResultPath>Indirizzo</formulaResultPath>
        <formulaSequence>5.0</formulaSequence>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem9</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem51</globalKey>
        <inputFieldName>Account:PersonMobilePhone</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountMobilePhone</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;SOC_1&apos;</filterValue>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem13</globalKey>
        <inputFieldName>ExternalId__c</inputFieldName>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>societyRecord</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem50</globalKey>
        <inputFieldName>Account:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>&apos;-&apos;</formulaConverted>
        <formulaExpression>&apos;-&apos;</formulaExpression>
        <formulaResultPath>BlankPurchasePowerValue</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem8</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>OpportunitySubjectGetDataCustom0jI9O000000vkAzUAIItem45</globalKey>
        <inputFieldName>Account:PersonMailingCity</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>OpportunitySubjectGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountCity</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;OpportunityId&quot; : &quot;0069X00000M8xKnQAJ&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>OpportunitySubjectGetData_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
