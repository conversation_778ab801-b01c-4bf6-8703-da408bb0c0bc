<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{&quot;AccountId&quot;:&quot;0019X000015e2ZLQAY&quot;}</customJavaScript>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>false</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>UniDocumentale_RecuperoStoricoCompleto</name>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>DMGetCiuAndCompagniaAccount</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;AccountId&quot;,
    &quot;element&quot; : &quot;AccountId&quot;
  } ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UniGetCiuAndCompagniaAccount&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GenericLog</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;call&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;UniLogIntegrationProcedureSubmit&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Message&quot; : &quot;=&apos;Documentale Recupero Storico Completo&apos;&quot;,
    &quot;ClassName&quot; : &quot;=&apos;UniDocumentale_RecuperoStoricoCompleto&apos;&quot;,
    &quot;Payload&quot; : &quot;%ContextId%&quot;,
    &quot;MethodName&quot; : &quot;=&apos;Integration Service&apos;&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>LAMergeIntegrationService</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;mergeListsOrder&quot; : [ &quot;SVSerializeResponse1:result1&quot;, &quot;SVSerializeResponse2:result2&quot; ],
  &quot;advancedMergeMap&quot; : [ ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;=totalCount != 0&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ ],
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;restOptions&quot; : { },
  &quot;mergeFields&quot; : [ ]
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>List Merge Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <description>Decision Matrix Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>getEnte</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;remoteOptions&quot; : {
    &quot;matrixName&quot; : &quot;TranscodificaEnte&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;matrix Input Parameters&quot; : [ {
    &quot;value&quot; : &quot;codiceEnte&quot;,
    &quot;name&quot; : &quot;RACreateUniqueList:results:datiStoricizzati:enteRilascio&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Matrix Action</type>
        </childElements>
        <description>Loop Block</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>LoopBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : { },
  &quot;loopList&quot; : &quot;RACreateUniqueList:results&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;loopOutput&quot; : {
    &quot;enteRilascio&quot; : &quot;=%getEnte:result:Ente%&quot;,
    &quot;idDocumento&quot; : &quot;=%RACreateUniqueList:results:idRevisione%&quot;
  }
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Loop Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>List Action</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>mergeEnteWithList</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : false,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;mergeListsOrder&quot; : [ &quot;RACreateUniqueList:results&quot;, &quot;LoopBlock&quot; ],
  &quot;advancedMergeMap&quot; : [ {
    &quot;listKey&quot; : &quot;RACreateUniqueList:results&quot;,
    &quot;matchingPath&quot; : &quot;idRevisione&quot;,
    &quot;normalizeKey&quot; : &quot;1&quot;
  }, {
    &quot;listKey&quot; : &quot;LoopBlock&quot;,
    &quot;matchingPath&quot; : &quot;idDocumento&quot;,
    &quot;normalizeKey&quot; : &quot;1&quot;
  } ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : {
    &quot;dataInizioValidita&quot; : &quot;=FORMATDATETIME(%datiStoricizzati:dataRilascio%,\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;societa&quot; : &quot;=IF(%compagnia% == \&quot;unipolsai\&quot;, \&quot;Unipol\&quot;, IF(%compagnia% == \&quot;unisalute\&quot;, \&quot;UniSalute\&quot;, \&quot;\&quot;))&quot;,
    &quot;dataScadenza&quot; : &quot;=FORMATDATETIME(%datiStoricizzati:dataScadenza%,\&quot;dd-MM-yyyy\&quot;)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : false,
  &quot;advancedMerge&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : false,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : { }
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>List Merge Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RACreateUniqueList</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;mergeListForVisualizzaStorico&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;uniUtils&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;documentList&quot; : &quot;%LAMergeIntegrationService%&quot;,
    &quot;accountDetailsList&quot; : &quot;%DMGetCiuAndCompagniaAccount:accountDetails%&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseAction</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;results:totalCount&quot; : &quot;%RACreateUniqueList:totalCount%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;mergeEnteWithList&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;results&quot;
}</propertySetConfig>
        <sequenceNumber>12.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>Set Values Action</description>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetParamsQuery1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;params1:Params:QueryParam&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { }
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>Set Values Action</description>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetParamsQuery2</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;params2:Params:QueryParam&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { }
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetParamsUrl1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;params1:Params:PathParam&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;codiceCompagnia&quot; : &quot;%DMGetCiuAndCompagniaAccount:accountDetails|1:compagnia%&quot;,
    &quot;ciu&quot; : &quot;%DMGetCiuAndCompagniaAccount:accountDetails|1:ciu%&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetParamsUrl2</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;params2:Params:PathParam&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;codiceCompagnia&quot; : &quot;%DMGetCiuAndCompagniaAccount:accountDetails|2:compagnia%&quot;,
    &quot;ciu&quot; : &quot;%DMGetCiuAndCompagniaAccount:accountDetails|2:ciu%&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>IntegrationService1</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;IntegrationService_IntegrationService&quot;,
  &quot;responseJSONNode&quot; : &quot;ipResponse1&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;integrationId&quot; : &quot;=\&quot;GetDocumentale_RecuperoStorico\&quot;&quot;,
    &quot;params&quot; : &quot;=SERIALIZE(params1)&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>IntegrationService2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;IntegrationService_IntegrationService&quot;,
  &quot;responseJSONNode&quot; : &quot;ipResponse2&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;integrationId&quot; : &quot;=\&quot;GetDocumentale_RecuperoStorico\&quot;&quot;,
    &quot;params&quot; : &quot;=SERIALIZE(params2)&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SVSerializeResponse1</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;result1&quot; : &quot;=DESERIALIZE(ipResponse1:result)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SVSerializeResponse2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;result2&quot; : &quot;=DESERIALIZE(ipResponse2:result)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>TryCatchBlock1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;failOnBlockError&quot; : true,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;&quot;,
  &quot;remoteMethod&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;isActive&quot; : true
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Try Catch Block</type>
    </omniProcessElements>
    <omniProcessKey>UniDocumentale_RecuperoStoricoCompleto</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;transientValues&quot; : {
    &quot;activateOrDeactivateConsent&quot; : false
  }
}</propertySetConfig>
    <subType>RecuperoStoricoCompleto</subType>
    <type>UniDocumentale</type>
    <uniqueName>UniDocumentale_RecuperoStoricoCompleto_Procedure_5</uniqueName>
    <versionNumber>5.0</versionNumber>
</OmniIntegrationProcedure>
