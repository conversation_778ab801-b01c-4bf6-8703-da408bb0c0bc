<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{&quot;codProv&quot;:&quot;RM&quot;,&quot;codStato&quot;:&quot;Z404&quot;,&quot;codComune&quot;:&quot;H224&quot;,&quot;actionType&quot;:&quot;setIF&quot;,&quot;provinciaRO&quot;:&quot;false&quot;,&quot;provinciaREQ&quot;:&quot;true&quot;,&quot;statoRO&quot;:&quot;false&quot;,&quot;statoREQ&quot;:&quot;true&quot;,&quot;comuneRO&quot;:&quot;false&quot;,&quot;comuneREQ&quot;:&quot;true&quot;,&quot;provincia&quot;:&quot;RC&quot;,&quot;comune&quot;:&quot;H224&quot;,&quot;stato&quot;:&quot;Z000&quot;,&quot;codTipoDoc&quot;:&quot;CDN&quot;,&quot;codCompagnia&quot;:&quot;SOC_1&quot;,&quot;recordId&quot;:&quot;a1i9X000006dsrVQAQ&quot;,&quot;enteEmissione&quot;:&quot;AMBASCIATA&quot;,&quot;docTypes&quot;:&quot;&quot;,&quot;docTypesSOC1&quot;:&quot;CID;CDN&quot;,&quot;docTypesSOC4&quot;:&quot;TSS;CDN&quot;}</customJavaScript>
    <description>actionType = setInfoTerr, getInfoTerritoriali, getComuni, enteEmissione</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>English</language>
    <name>UniDoc_OmniscriptHelper</name>
    <omniProcessElements>
        <childElements>
            <description>Data Mapper Extract Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetComuniFlex</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;codProv&quot;,
    &quot;element&quot; : &quot;codProv&quot;
  } ],
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options:optionsComuni&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;codProv&quot; : &quot;=%provincia%&quot;
  },
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetComuni&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <description>Data Mapper Extract Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetProvinceFlex</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options:optionsProvince&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetProvince&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <description>Data Mapper Extract Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetStatiFlex</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options:optionsStati&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;prova&quot; : &quot;=\&quot;prova\&quot;&quot;
  },
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetStati&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <description>Response Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseGetComuni</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;returnFullDataJSON&quot; : false,
  &quot;additionalOutput&quot; : {
    &quot;values:stato&quot; : &quot;=%stato%&quot;,
    &quot;values:comune&quot; : &quot;=%comune%&quot;,
    &quot;attributes:provincia:isRequired&quot; : &quot;=%provinciaREQ%&quot;,
    &quot;attributes:stato:isRO&quot; : &quot;=%statoRO%&quot;,
    &quot;attributes:comune:isRequired&quot; : &quot;=%comuneREQ%&quot;,
    &quot;values:provincia&quot; : &quot;=%provincia%&quot;,
    &quot;attributes:comune:isRO&quot; : &quot;=%comuneRO%&quot;,
    &quot;attributes:provincia:isRO&quot; : &quot;=%provinciaRO%&quot;,
    &quot;attributes:stato:isRequired&quot; : &quot;=%statoREQ%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;sendJSONPath&quot; : &quot;response&quot;,
  &quot;responseFormat&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <description>Conditional Block</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetComuni</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionType% == \&quot;getComuni\&quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <description>Data Mapper Extract Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetStatiEstero</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options:optionsStati&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;prova&quot; : &quot;=\&quot;prova\&quot;&quot;
  },
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetStati&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <description>Response Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>ResponseInfoTerritorialiEstero</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;returnFullDataJSON&quot; : false,
  &quot;additionalOutput&quot; : {
    &quot;values:stato&quot; : &quot;=%codStato%&quot;,
    &quot;attributes:provincia:isRequired&quot; : &quot;=false&quot;,
    &quot;attributes:stato:isRO&quot; : &quot;=false&quot;,
    &quot;attributes:comune:isRequired&quot; : &quot;=false&quot;,
    &quot;attributes:comune:isRO&quot; : &quot;=true&quot;,
    &quot;attributes:provincia:isRO&quot; : &quot;=true&quot;,
    &quot;attributes:stato:isRequired&quot; : &quot;=true&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;sendJSONPath&quot; : &quot;response&quot;,
  &quot;responseFormat&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>Response Action</type>
            </childElements>
            <description>Conditional Block</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>InfoTerritorialiEstero</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%codStato% != \&quot;Z000\&quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <childElements>
                <description>Data Mapper Extract Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetComuniItalia</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;codProv&quot;,
    &quot;element&quot; : &quot;codProv&quot;
  } ],
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options:optionsComuni&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;codProv&quot; : &quot;=%codProv%&quot;
  },
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetComuni&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <description>Data Mapper Extract Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetProvinceItalia</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options:optionsProvince&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetProvince&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <description>Data Mapper Extract Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetStatiItalia</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options:optionsStati&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;prova&quot; : &quot;=\&quot;prova\&quot;&quot;
  },
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetStati&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <description>Response Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>ResponseInfoTerritorialiItalia</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;returnFullDataJSON&quot; : false,
  &quot;additionalOutput&quot; : {
    &quot;values:stato&quot; : &quot;=%codStato%&quot;,
    &quot;values:comune&quot; : &quot;=%codComune%&quot;,
    &quot;attributes:provincia:isRequired&quot; : &quot;=true&quot;,
    &quot;attributes:stato:isRO&quot; : &quot;=false&quot;,
    &quot;attributes:comune:isRequired&quot; : &quot;=true&quot;,
    &quot;values:provincia&quot; : &quot;=%codProv%&quot;,
    &quot;attributes:comune:isRO&quot; : &quot;=false&quot;,
    &quot;attributes:provincia:isRO&quot; : &quot;=false&quot;,
    &quot;attributes:stato:isRequired&quot; : &quot;=true&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;sendJSONPath&quot; : &quot;response&quot;,
  &quot;responseFormat&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>4.0</sequenceNumber>
                <type>Response Action</type>
            </childElements>
            <description>Conditional Block</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>InfoTerritorialiItalia</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%codStato% == \&quot;Z000\&quot; || ISBLANK(%codStato%)&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <description>Conditional Block</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>InfoTerritoriali</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionType% == \&quot;getInfoTerritoriali\&quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <description>Remote Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Remote</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteMethod&quot; : &quot;setDocTypePicklist&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;uniUtils&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;society&quot; : &quot;=%codCompagnia%&quot;,
    &quot;docTypes&quot; : &quot;=%docTypes%&quot;
  },
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Remote Action</type>
        </childElements>
        <childElements>
            <description>Response Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetDocTypePickResponse</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;elementValueMap&quot; : { },
  &quot;responseFormat&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;list:options&quot; : &quot;=%Remote:options%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <description>Conditional Block</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetDocTypePicklist</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionType% == \&quot;setDocType\&quot;&quot;,
  &quot;elementValueMap&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <description>Response Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetEnteEmissionePicklistResponse</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;sendJSONPath&quot; : &quot;enteList&quot;,
  &quot;responseFormat&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <childElements>
                <description>Set Values Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CaseCDN</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;enteList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%codTipoDoc% == \&quot;CDN\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;default&quot; : &quot;%enteEmissione%&quot;,
    &quot;readOnly&quot; : false,
    &quot;list&quot; : [ {
      &quot;label&quot; : &quot;Comune&quot;,
      &quot;value&quot; : &quot;COMUNE&quot;
    }, {
      &quot;label&quot; : &quot;Consolato&quot;,
      &quot;value&quot; : &quot;CONSOLATO&quot;
    }, {
      &quot;label&quot; : &quot;Ambasciata&quot;,
      &quot;value&quot; : &quot;AMBASCIATA&quot;
    } ]
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <description>Set Values Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CaseCID</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;enteList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%codTipoDoc% == \&quot;CID\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;default&quot; : &quot;%enteEmissione%&quot;,
    &quot;readOnly&quot; : false,
    &quot;list&quot; : [ {
      &quot;label&quot; : &quot;Comune&quot;,
      &quot;value&quot; : &quot;COMUNE&quot;
    }, {
      &quot;label&quot; : &quot;Ministero Affari Esteri&quot;,
      &quot;value&quot; : &quot;MINISTERO AFFARI ESTERI&quot;
    }, {
      &quot;label&quot; : &quot;Consolato&quot;,
      &quot;value&quot; : &quot;CONSOLATO&quot;
    }, {
      &quot;label&quot; : &quot;Ambasciata&quot;,
      &quot;value&quot; : &quot;AMBASCIATA&quot;
    } ]
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <description>Set Values Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CaseLIP</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;enteList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%codTipoDoc% == \&quot;LIP\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;default&quot; : &quot;%enteEmissione%&quot;,
    &quot;readOnly&quot; : false,
    &quot;list&quot; : [ {
      &quot;label&quot; : &quot;Ministero&quot;,
      &quot;value&quot; : &quot;MINISTERO&quot;
    }, {
      &quot;label&quot; : &quot;Inpdap&quot;,
      &quot;value&quot; : &quot;INPDAP&quot;
    }, {
      &quot;label&quot; : &quot;Inps&quot;,
      &quot;value&quot; : &quot;INPS&quot;
    } ]
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>4.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <description>Set Values Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CasePAS</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;enteList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%codTipoDoc% == \&quot;PAS\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;default&quot; : &quot;%enteEmissione%&quot;,
    &quot;readOnly&quot; : false,
    &quot;list&quot; : [ {
      &quot;label&quot; : &quot;Questura&quot;,
      &quot;value&quot; : &quot;QUESTURA&quot;
    }, {
      &quot;label&quot; : &quot;Ministero Affari Esteri&quot;,
      &quot;value&quot; : &quot;MINISTERO AFFARI ESTERI&quot;
    }, {
      &quot;label&quot; : &quot;Consolato&quot;,
      &quot;value&quot; : &quot;CONSOLATO&quot;
    }, {
      &quot;label&quot; : &quot;Ambasciata&quot;,
      &quot;value&quot; : &quot;AMBASCIATA&quot;
    } ]
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <description>Set Values Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CasePAT</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;enteList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%codTipoDoc% == \&quot;PAT\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;default&quot; : &quot;%enteEmissione%&quot;,
    &quot;readOnly&quot; : false,
    &quot;list&quot; : [ {
      &quot;label&quot; : &quot;DTT (Dipartimento Trasporti Terrestri)&quot;,
      &quot;value&quot; : &quot;DTT&quot;
    }, {
      &quot;label&quot; : &quot;Capitaneria di porto&quot;,
      &quot;value&quot; : &quot;CAPITANERIA PORTO&quot;
    }, {
      &quot;label&quot; : &quot;U.C.O. (Uff. Centro Operativo)&quot;,
      &quot;value&quot; : &quot;UCO&quot;
    }, {
      &quot;label&quot; : &quot;Prefettura&quot;,
      &quot;value&quot; : &quot;PREFETTURA&quot;
    }, {
      &quot;label&quot; : &quot;MCTC&quot;,
      &quot;value&quot; : &quot;MCTC&quot;
    } ]
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>5.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <description>Set Values Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CasePOR</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;enteList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%codTipoDoc% == \&quot;POR\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;default&quot; : &quot;%enteEmissione%&quot;,
    &quot;readOnly&quot; : false,
    &quot;list&quot; : [ {
      &quot;label&quot; : &quot;Prefettura&quot;,
      &quot;value&quot; : &quot;PREFETTURA&quot;
    }, {
      &quot;label&quot; : &quot;Questura&quot;,
      &quot;value&quot; : &quot;QUESTURA&quot;
    } ]
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>6.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <description>Set Values Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CasePSO</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;enteList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%codTipoDoc% == \&quot;PSO\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;default&quot; : &quot;QUESTURA&quot;,
    &quot;readOnly&quot; : true,
    &quot;list&quot; : [ {
      &quot;label&quot; : &quot;Questura&quot;,
      &quot;value&quot; : &quot;QUESTURA&quot;
    } ]
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>9.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <description>Set Values Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CaseTES</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;enteList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%codTipoDoc% == \&quot;TES\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;default&quot; : &quot;%enteEmissione%&quot;,
    &quot;readOnly&quot; : false,
    &quot;list&quot; : [ {
      &quot;label&quot; : &quot;Presidenza del consiglio dei ministri&quot;,
      &quot;value&quot; : &quot;PRESIDENZA DEL CONSIGLIO&quot;
    }, {
      &quot;label&quot; : &quot;Ministero&quot;,
      &quot;value&quot; : &quot;MINISTERO&quot;
    } ]
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>7.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <description>Set Values Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CaseTMR</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;enteList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%codTipoDoc% == \&quot;TMR\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;default&quot; : &quot;MINISTERO DEL LAVORO&quot;,
    &quot;readOnly&quot; : true,
    &quot;list&quot; : [ {
      &quot;label&quot; : &quot;MINISTERO DEL LAVORO&quot;,
      &quot;value&quot; : &quot;Ministero del lavoro&quot;
    } ]
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>10.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <description>Set Values Action</description>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CaseTSS</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;enteList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%codTipoDoc% == \&quot;TSS\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;default&quot; : &quot;MINISTERO&quot;,
    &quot;readOnly&quot; : true,
    &quot;list&quot; : [ {
      &quot;label&quot; : &quot;Ministero&quot;,
      &quot;value&quot; : &quot;MINISTERO&quot;
    } ]
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>8.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <description>Conditional Block</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetEnteList</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <description>Conditional Block</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetEnteEmissionePicklist</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionType% == \&quot;enteEmissione\&quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <description>Response Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetInfoTerritorialiOmniscriptResponse</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;returnFullDataJSON&quot; : false,
  &quot;additionalOutput&quot; : {
    &quot;Step2:InfoTerritoriali:root:codiceComune&quot; : &quot;=%codComune%&quot;,
    &quot;Step2:InfoTerritoriali:root:codiceStato&quot; : &quot;=%codStato%&quot;,
    &quot;inputSocietaPicklist:recordId&quot; : &quot;=%recordId%&quot;,
    &quot;inputUnitaTerrFlex:codComune&quot; : &quot;=%codComune%&quot;,
    &quot;inputUnitaTerrFlex:codProv&quot; : &quot;=%codProv%&quot;,
    &quot;inputUnitaTerrFlex:codStato&quot; : &quot;=%codStato%&quot;,
    &quot;inputSocietaPicklist:soc&quot; : &quot;=%codCompagnia%&quot;,
    &quot;Step2:InfoTerritoriali:root:codiceProv&quot; : &quot;=%codProv%&quot;,
    &quot;inputEnteEmissione:tipoDoc&quot; : &quot;=%codTipoDoc%&quot;,
    &quot;labelTipoDoc&quot; : &quot;=FILTER(LIST(TipoDocList), &apos;Codice == \&quot;&apos; + codTipoDoc + &apos;\&quot;&apos;)&quot;,
    &quot;Step2:societyPicklist:data:soc&quot; : &quot;=%codCompagnia%&quot;,
    &quot;Step2:EnteEmissione:root:enteEmissione&quot; : &quot;=%enteEmissione%&quot;,
    &quot;inputEnteEmissione:enteEmissione&quot; : &quot;=%enteEmissione%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;responseFormat&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <description>Set Values Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TipoDocList</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;lista&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;lista&quot; : [ {
      &quot;Valore&quot; : &quot;CERTIFICATO DI NASCITA&quot;,
      &quot;Codice&quot; : &quot;CDN&quot;
    }, {
      &quot;Valore&quot; : &quot;CARTA D&apos;IDENTITÀ&quot;,
      &quot;Codice&quot; : &quot;CID&quot;
    }, {
      &quot;Valore&quot; : &quot;LIBRETTO PENSIONISTICO&quot;,
      &quot;Codice&quot; : &quot;LIP&quot;
    }, {
      &quot;Valore&quot; : &quot;PASSAPORTO&quot;,
      &quot;Codice&quot; : &quot;PAS&quot;
    }, {
      &quot;Valore&quot; : &quot;PATENTE&quot;,
      &quot;Codice&quot; : &quot;PAT&quot;
    }, {
      &quot;Valore&quot; : &quot;PORTO D&apos;ARMI&quot;,
      &quot;Codice&quot; : &quot;POR&quot;
    }, {
      &quot;Valore&quot; : &quot;PERMESSO DI SOGGIORNO&quot;,
      &quot;Codice&quot; : &quot;PSO&quot;
    }, {
      &quot;Valore&quot; : &quot;TESSERA MINISTERIALE&quot;,
      &quot;Codice&quot; : &quot;TES&quot;
    }, {
      &quot;Valore&quot; : &quot;TESSERA MANUT. RISCALDAMENTO&quot;,
      &quot;Codice&quot; : &quot;TMR&quot;
    }, {
      &quot;Valore&quot; : &quot;TESSERA SANITARIA&quot;,
      &quot;Codice&quot; : &quot;TSS&quot;
    } ]
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <description>Conditional Block</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetInfoTerritorialiOmniscript</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionType% == &apos;setInfoTerr&apos;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <description>Response Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>NuovoDocumentoInputFieldResponse</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;elementValueMap&quot; : { },
  &quot;responseFormat&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;inputSocTipoDocFlex:recordId&quot; : &quot;=%recordId%&quot;,
    &quot;inputSocTipoDocFlex:docTypesSOC1&quot; : &quot;=%docTypesSOC1%&quot;,
    &quot;inputSocTipoDocFlex:docTypesSOC4&quot; : &quot;=%docTypesSOC4%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <description>Conditional Block</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetNDInputField</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionType% == \&quot;setIF\&quot;&quot;,
  &quot;elementValueMap&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessKey>UniDoc_OmniscriptHelper</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;transientValues&quot; : {
    &quot;activateOrDeactivateConsent&quot; : false
  }
}</propertySetConfig>
    <subType>OmniscriptHelper</subType>
    <type>UniDoc</type>
    <uniqueName>UniDoc_OmniscriptHelper_English_2</uniqueName>
    <versionNumber>2.0</versionNumber>
</OmniIntegrationProcedure>
