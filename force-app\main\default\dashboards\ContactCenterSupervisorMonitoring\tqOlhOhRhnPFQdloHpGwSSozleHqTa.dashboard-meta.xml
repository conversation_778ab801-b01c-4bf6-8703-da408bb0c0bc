<?xml version="1.0" encoding="UTF-8"?>
<Dashboard xmlns="http://soap.sforce.com/2006/04/metadata">
    <backgroundEndColor>#FFFFFF</backgroundEndColor>
    <backgroundFadeDirection>Diagonal</backgroundFadeDirection>
    <backgroundStartColor>#FFFFFF</backgroundStartColor>
    <chartTheme>light</chartTheme>
    <colorPalette>unity</colorPalette>
    <dashboardChartTheme>light</dashboardChartTheme>
    <dashboardColorPalette>unity</dashboardColorPalette>
    <dashboardFilters>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>TODAY</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>YESTERDAY</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_WEEK</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_MONTH</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_N_MONTHS:3</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_N_MONTHS:6</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_N_MONTHS:9</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_N_MONTHS:12</values>
        </dashboardFilterOptions>
        <name>Data Ricezione</name>
    </dashboardFilters>
    <dashboardFilters>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>TODAY</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>YESTERDAY</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_WEEK</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_MONTH</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_N_MONTHS:3</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_N_MONTHS:6</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_N_MONTHS:9</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>LAST_N_MONTHS:12</values>
        </dashboardFilterOptions>
        <name>Data Esito</name>
    </dashboardFilters>
    <dashboardFilters>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Client</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Prospect</values>
        </dashboardFilterOptions>
        <name>Tipo Cliente</name>
    </dashboardFilters>
    <dashboardFilters>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Veicoli</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Cane e Gatto</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Casa</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Famiglia</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Viaggio</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Infortuni</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Mobilità</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Salute</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Vita</values>
        </dashboardFilterOptions>
        <name>Ambito di bisogno</name>
    </dashboardFilters>
    <dashboardGridLayout>
        <dashboardGridComponents>
            <colSpan>4</colSpan>
            <columnIndex>0</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>false</autoselectColumnsFromReport>
                <chartAxisRange>Auto</chartAxisRange>
                <chartSummary>
                    <axisBinding>y</axisBinding>
                    <column>RowCount</column>
                </chartSummary>
                <componentType>Donut</componentType>
                <dashboardFilterColumns>
                    <column>Case$Created_Date__c</column>
                </dashboardFilterColumns>
                <dashboardFilterColumns>
                    <column>Case$DueDate__c</column>
                </dashboardFilterColumns>
                <dashboardFilterColumns>
                    <column>Case$DrTipologiaCliente__c</column>
                </dashboardFilterColumns>
                <dashboardFilterColumns>
                    <column>Case$CommercialAreasOfNeed__c</column>
                </dashboardFilterColumns>
                <decimalPrecision>-1</decimalPrecision>
                <displayUnits>Auto</displayUnits>
                <drillEnabled>false</drillEnabled>
                <drillToDetailEnabled>false</drillToDetailEnabled>
                <enableHover>false</enableHover>
                <expandOthers>false</expandOthers>
                <groupingColumn>Case$Owner</groupingColumn>
                <groupingSortProperties>
                    <groupingSorts>
                        <groupingLevel>g1</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                </groupingSortProperties>
                <header>Totale Contatti Gestiti per Operatore</header>
                <legendPosition>Right</legendPosition>
                <maxValuesDisplayed>6</maxValuesDisplayed>
                <report>CONTACTCENTER/New_Attivit_di_Contatto_Report_osG</report>
                <showPercentage>false</showPercentage>
                <showTotal>true</showTotal>
                <showValues>true</showValues>
                <sortBy>RowLabelAscending</sortBy>
                <sortLegendValues>true</sortLegendValues>
                <useReportChart>false</useReportChart>
            </dashboardComponent>
            <rowIndex>0</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <dashboardGridComponents>
            <colSpan>8</colSpan>
            <columnIndex>4</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>false</autoselectColumnsFromReport>
                <chartAxisRange>Auto</chartAxisRange>
                <chartSummary>
                    <axisBinding>y</axisBinding>
                    <column>RowCount</column>
                </chartSummary>
                <componentType>BarStacked</componentType>
                <dashboardFilterColumns>
                    <column>Case$Created_Date__c</column>
                </dashboardFilterColumns>
                <dashboardFilterColumns>
                    <column>Case$DueDate__c</column>
                </dashboardFilterColumns>
                <dashboardFilterColumns>
                    <column>Case$DrTipologiaCliente__c</column>
                </dashboardFilterColumns>
                <dashboardFilterColumns>
                    <column>Case$CommercialAreasOfNeed__c</column>
                </dashboardFilterColumns>
                <decimalPrecision>-1</decimalPrecision>
                <displayUnits>Auto</displayUnits>
                <drillEnabled>false</drillEnabled>
                <drillToDetailEnabled>false</drillToDetailEnabled>
                <enableHover>false</enableHover>
                <expandOthers>false</expandOthers>
                <groupingColumn>Case$Owner</groupingColumn>
                <groupingColumn>CDF1</groupingColumn>
                <groupingSortProperties>
                    <groupingSorts>
                        <groupingLevel>g1</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                    <groupingSorts>
                        <groupingLevel>g2</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                </groupingSortProperties>
                <header>Totale Contatti Suddivisi per Esito</header>
                <legendPosition>Right</legendPosition>
                <report>CONTACTCENTER/Totale_Contatti_Suddivisi_per_Esito_Ts9</report>
                <showPercentage>false</showPercentage>
                <showValues>false</showValues>
                <sortBy>RowLabelAscending</sortBy>
                <sortLegendValues>true</sortLegendValues>
                <useReportChart>false</useReportChart>
            </dashboardComponent>
            <rowIndex>0</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <dashboardGridComponents>
            <colSpan>12</colSpan>
            <columnIndex>0</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>false</autoselectColumnsFromReport>
                <componentType>FlexTable</componentType>
                <dashboardFilterColumns>
                    <column>Case$Created_Date__c</column>
                </dashboardFilterColumns>
                <dashboardFilterColumns>
                    <column>Case$CC_Data_Inserimento_Esito__c</column>
                </dashboardFilterColumns>
                <dashboardFilterColumns>
                    <column>Case$DrTipologiaCliente__c</column>
                </dashboardFilterColumns>
                <dashboardFilterColumns>
                    <column>Case$CommercialAreasOfNeed__c</column>
                </dashboardFilterColumns>
                <flexComponentProperties>
                    <decimalPrecision>-1</decimalPrecision>
                    <flexTableColumn>
                        <reportColumn>Case.Owner.Name</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>grouping</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>a!Case.ActualCalls__c</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>FORMULA3</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>FORMULA1</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>a!VoiceCall.CallDurationInSeconds</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>a!Case.CC_Tempo_medio_presa_in_carico__c</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>FORMULA2</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>FORMULA4</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableColumn>
                        <reportColumn>FORMULA5</reportColumn>
                        <showSubTotal>false</showSubTotal>
                        <showTotal>false</showTotal>
                        <type>aggregate</type>
                    </flexTableColumn>
                    <flexTableSortInfo>
                        <sortOrder>1</sortOrder>
                    </flexTableSortInfo>
                    <hideChatterPhotos>true</hideChatterPhotos>
                </flexComponentProperties>
                <groupingSortProperties>
                    <groupingSorts>
                        <groupingLevel>g1</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                </groupingSortProperties>
                <header>CC Tabella Performance Operatore</header>
                <report>CONTACTCENTER/CC_Tabella_Performance_Operatore_poF</report>
            </dashboardComponent>
            <rowIndex>8</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <numberOfColumns>12</numberOfColumns>
        <rowHeight>36</rowHeight>
    </dashboardGridLayout>
    <dashboardType>LoggedInUser</dashboardType>
    <isGridLayout>true</isGridLayout>
    <textColor>#000000</textColor>
    <title>Supervisor Monitoring</title>
    <titleColor>#000000</titleColor>
    <titleSize>12</titleSize>
</Dashboard>
