<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n\&quot;success\&quot; : true,\n\&quot;Message\&quot; : \&quot;\&quot;,\n\&quot;Status\&quot; : true,\n\&quot;ErrorType\&quot; : 0\n}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;state0element7block_element1block_element0_1&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestPost_Fatca&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;ciu&quot;:&quot;{Ciu}&quot;,&quot;compagnia&quot;:&quot;{Compagnia}&quot;,&quot;cellaCensuaria&quot;:&quot;{CellaCensuaria}&quot;,&quot;indirizzoBreve&quot;:&quot;{IndirizzoBreve}&quot;,&quot;cap&quot;:&quot;{CAP}&quot;,&quot;codiceBelfioreComune&quot;:&quot;{CodiceBelfioreComune}&quot;,&quot;comune&quot;:&quot;{Comune}&quot;,&quot;flagSedeLegale&quot;:&quot;{FlagSedeLegale}&quot;,&quot;indirizzoCompleto&quot;:&quot;{IndirizzoCompleto}&quot;,&quot;numeroCivico&quot;:&quot;{NumeroCivico}&quot;,&quot;giin&quot;:&quot;{Giin}&quot;,&quot;provincia&quot;:&quot;{Provincia}&quot;,&quot;tin&quot;:&quot;{Tin}&quot;,&quot;tipoIndirizzo&quot;:&quot;{TipoIndirizzo}&quot;,&quot;tipologiaSocietaFatca&quot;:&quot;{TipologiaSocietaFatca}&quot;,&quot;stato&quot;:&quot;{Stato}&quot;,&quot;siglaProvincia&quot;:&quot;{SiglaProvincia}&quot;,&quot;residenzaFiscaleUsa&quot;:&quot;{ResidenzaFiscaleUsa}&quot;,&quot;residenzaFiscaleStatoEstero&quot;:&quot;{ResidenzaFiscaleStatoEstero}&quot;,&quot;ActionType&quot;:&quot;ModificaFatcaPG&quot;,&quot;RecordId&quot;:&quot;{RecordId}&quot;,&quot;UserId&quot;:&quot;{User.userId}&quot;,&quot;localitaEstera&quot;:&quot;{LocalitaEstera}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Ciu\&quot;:\&quot;{Ciu}\&quot;,\&quot;Compagnia\&quot;:\&quot;{Compagnia}\&quot;,\&quot;CellaCensuaria\&quot;:\&quot;{CellaCensuaria}\&quot;,\&quot;IndirizzoBreve\&quot;:\&quot;{IndirizzoBreve}\&quot;,\&quot;CAP\&quot;:\&quot;{CAP}\&quot;,\&quot;CodiceBelfioreComune\&quot;:\&quot;{CodiceBelfioreComune}\&quot;,\&quot;Comune\&quot;:\&quot;{Comune}\&quot;,\&quot;FlagSedeLegale\&quot;:\&quot;{FlagSedeLegale}\&quot;,\&quot;IndirizzoCompleto\&quot;:\&quot;{IndirizzoCompleto}\&quot;,\&quot;NumeroCivico\&quot;:\&quot;{NumeroCivico}\&quot;,\&quot;Giin\&quot;:\&quot;{Giin}\&quot;,\&quot;Provincia\&quot;:\&quot;{Provincia}\&quot;,\&quot;Tin\&quot;:\&quot;{Tin}\&quot;,\&quot;TipoIndirizzo\&quot;:\&quot;{TipoIndirizzo}\&quot;,\&quot;TipologiaSocietaFatca\&quot;:\&quot;{TipologiaSocietaFatca}\&quot;,\&quot;Stato\&quot;:\&quot;{Stato}\&quot;,\&quot;SiglaProvincia\&quot;:\&quot;{SiglaProvincia}\&quot;,\&quot;ResidenzaFiscaleUsa\&quot;:\&quot;{ResidenzaFiscaleUsa}\&quot;,\&quot;ResidenzaFiscaleStatoEstero\&quot;:\&quot;{ResidenzaFiscaleStatoEstero}\&quot;,\&quot;RecordId\&quot;:\&quot;{RecordId}\&quot;,\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;LocalitaEstera\&quot;:\&quot;{LocalitaEstera}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Ciu&quot;,&quot;val&quot;:&quot;7176310&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;Compagnia&quot;,&quot;val&quot;:&quot;unipolsai&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;CellaCensuaria&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;name&quot;:&quot;IndirizzoBreve&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;CAP&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:10},{&quot;name&quot;:&quot;CodiceBelfioreComune&quot;,&quot;val&quot;:&quot;G157&quot;,&quot;id&quot;:12},{&quot;name&quot;:&quot;Comune&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:14},{&quot;name&quot;:&quot;FlagSedeLegale&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:16},{&quot;name&quot;:&quot;IndirizzoCompleto&quot;,&quot;val&quot;:&quot;Via San Giovanni&quot;,&quot;id&quot;:18},{&quot;name&quot;:&quot;NumeroCivico&quot;,&quot;val&quot;:&quot;17&quot;,&quot;id&quot;:20},{&quot;name&quot;:&quot;Giin&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:22},{&quot;name&quot;:&quot;Provincia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:24},{&quot;name&quot;:&quot;Tin&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:26},{&quot;name&quot;:&quot;TipoIndirizzo&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:28},{&quot;name&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;val&quot;:&quot;1&quot;,&quot;id&quot;:30},{&quot;name&quot;:&quot;Stato&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:32},{&quot;name&quot;:&quot;SiglaProvincia&quot;,&quot;val&quot;:&quot;AN&quot;,&quot;id&quot;:34},{&quot;name&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;val&quot;:&quot;false&quot;,&quot;id&quot;:36},{&quot;name&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;val&quot;:&quot;false&quot;,&quot;id&quot;:38},{&quot;name&quot;:&quot;RecordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:61},{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:63},{&quot;name&quot;:&quot;LocalitaEstera&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:45}]},&quot;state0element2block_element1_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestGet_Fatca&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;ActionType&quot;:&quot;GetOptions&quot;,&quot;SiglaProvincia&quot;:&quot;{SiglaProvincia}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;Sig\&quot;:\&quot;{Sig}\&quot;,\&quot;SigliaProvincia\&quot;:\&quot;{SigliaProvincia}\&quot;,\&quot;SiglaProvincia\&quot;:\&quot;{SiglaProvincia}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;Sig&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;SigliaProvincia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;name&quot;:&quot;SiglaProvincia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:4}]},&quot;state0element2block_element2_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestGet_Fatca&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;ActionType&quot;:&quot;GetLocalita&quot;,&quot;CodiceBelfioreComune&quot;:&quot;{CodiceBelfioreComune}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;CodiceBelfioreComune\&quot;:\&quot;{CodiceBelfioreComune}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;CodiceBelfioreComune&quot;,&quot;val&quot;:&quot;F205&quot;,&quot;id&quot;:3}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>FlexFatcaModifica</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cspan%20style=%22font-size:%2014pt;%22%3EModifica%20dati%20FATCA/CRS%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;uKey&quot;:&quot;1758035910765-280&quot;,&quot;datasourceKey&quot;:&quot;state0element0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_1_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cstrong%3ESede%20Amministrativa%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element1block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1758035910765-350&quot;,&quot;datasourceKey&quot;:&quot;state0element1&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_2_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Stato&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{Options}&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;searchable&quot;,&quot;value&quot;:&quot;true&quot;,&quot;id&quot;:1},{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:2}],&quot;fieldBinding&quot;:&quot;{Stato}&quot;},&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Select-0&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;parsedProperty&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},{&quot;key&quot;:&quot;element_element_block_2_0_baseInputElement_1_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Province&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{OptionProvince}&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;searchable&quot;,&quot;value&quot;:&quot;true&quot;,&quot;id&quot;:2},{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:2}],&quot;fieldBinding&quot;:&quot;{SiglaProvincia}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742322003205-9ipo5x2rc&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742468287311&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestGet_Fatca\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ActionType\&quot;:\&quot;GetOptions\&quot;,\&quot;SiglaProvincia\&quot;:\&quot;{SiglaProvincia}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;Sig\\\&quot;:\\\&quot;{Sig}\\\&quot;,\\\&quot;SigliaProvincia\\\&quot;:\\\&quot;{SigliaProvincia}\\\&quot;,\\\&quot;SiglaProvincia\\\&quot;:\\\&quot;{SiglaProvincia}\\\&quot;}\&quot;,\&quot;resultVar\&quot;:\&quot;\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;Sig\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:5},{\&quot;name\&quot;:\&quot;SigliaProvincia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:6},{\&quot;name\&quot;:\&quot;SiglaProvincia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:4}]}&quot;},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742831738110-s1pt0sgnt&quot;,&quot;label&quot;:&quot;BlankField&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742833024292&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Latitudine&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;Longitudine&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;IndirizzoCompleto&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;CAP&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;NumeroCivico&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;CellaCensuaria&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;IndirizzoBreve&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;Localita&quot;,&quot;fieldValue&quot;:&quot;-&quot;}]},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742322003205-9ipo5x2rc&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742468287311&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestGet_Fatca\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ActionType\&quot;:\&quot;GetOptions\&quot;,\&quot;SiglaProvincia\&quot;:\&quot;{SiglaProvincia}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;Sig\\\&quot;:\\\&quot;{Sig}\\\&quot;,\\\&quot;SigliaProvincia\\\&quot;:\\\&quot;{SigliaProvincia}\\\&quot;,\\\&quot;SiglaProvincia\\\&quot;:\\\&quot;{SiglaProvincia}\\\&quot;}\&quot;,\&quot;resultVar\&quot;:\&quot;\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;Sig\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:5},{\&quot;name\&quot;:\&quot;SigliaProvincia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:6},{\&quot;name\&quot;:\&quot;SiglaProvincia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:4}]}&quot;},&quot;actionIndex&quot;:0,&quot;datasourceKey&quot;:&quot;state0element2block_element1_0&quot;},{&quot;key&quot;:&quot;1742831738110-s1pt0sgnt&quot;,&quot;label&quot;:&quot;BlankField&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742833024292&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Latitudine&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;Longitudine&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;IndirizzoCompleto&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;CAP&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;NumeroCivico&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;CellaCensuaria&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;IndirizzoBreve&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;Localita&quot;,&quot;fieldValue&quot;:&quot;-&quot;}]},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-30&quot;,&quot;field&quot;:&quot;Stato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Select-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element2block_element1&quot;,&quot;parsedProperty&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},{&quot;key&quot;:&quot;element_element_block_2_0_baseInputElement_2_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Comuni&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{OptionsComuni}&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;searchable&quot;,&quot;value&quot;:&quot;true&quot;,&quot;id&quot;:1},{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:2}],&quot;fieldBinding&quot;:&quot;{CodiceBelfioreComune}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742470343244-c2qu9ix1o&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742470890504&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestGet_Fatca\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ActionType\&quot;:\&quot;GetLocalita\&quot;,\&quot;CodiceBelfioreComune\&quot;:\&quot;{CodiceBelfioreComune}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;CodiceBelfioreComune\\\&quot;:\\\&quot;{CodiceBelfioreComune}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;CodiceBelfioreComune\&quot;,\&quot;val\&quot;:\&quot;F205\&quot;,\&quot;id\&quot;:3}]}&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742470343244-c2qu9ix1o&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742470890504&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestGet_Fatca\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ActionType\&quot;:\&quot;GetLocalita\&quot;,\&quot;CodiceBelfioreComune\&quot;:\&quot;{CodiceBelfioreComune}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;CodiceBelfioreComune\\\&quot;:\\\&quot;{CodiceBelfioreComune}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;CodiceBelfioreComune\&quot;,\&quot;val\&quot;:\&quot;F205\&quot;,\&quot;id\&quot;:3}]}&quot;},&quot;actionIndex&quot;:0,&quot;datasourceKey&quot;:&quot;state0element2block_element2_0&quot;}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-44&quot;,&quot;field&quot;:&quot;Stato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Select-2&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;parsedProperty&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},{&quot;key&quot;:&quot;element_element_block_2_0_baseInputElement_3_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Località&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{OptionsLocalita}&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;searchable&quot;,&quot;value&quot;:&quot;true&quot;,&quot;id&quot;:1},{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:2}],&quot;fieldBinding&quot;:&quot;{Localita}&quot;,&quot;action&quot;:null},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-37&quot;,&quot;field&quot;:&quot;Stato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Select-3&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;parsedProperty&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},{&quot;key&quot;:&quot;element_element_block_2_0_baseInputElement_4_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;CAP&quot;,&quot;fieldBinding&quot;:&quot;{CAP}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:0}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Text-4&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element4&quot;},{&quot;key&quot;:&quot;element_element_block_2_0_baseInputElement_5_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Località&quot;,&quot;fieldBinding&quot;:&quot;{LocalitaEstera}&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-51&quot;,&quot;field&quot;:&quot;Stato&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Text-5&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element5&quot;},{&quot;key&quot;:&quot;element_element_block_2_0_baseInputElement_6_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Indirizzo&quot;,&quot;fieldBinding&quot;:&quot;{IndirizzoCompleto}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:0}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Text-6&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element6&quot;},{&quot;key&quot;:&quot;element_element_block_2_0_baseInputElement_7_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;NR&quot;,&quot;fieldBinding&quot;:&quot;{NumeroCivico}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{FlagRequiredInd}&quot;,&quot;id&quot;:0}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Text-7&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element7&quot;},{&quot;key&quot;:&quot;element_element_block_2_0_block_8_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;class&quot;:&quot;slds-text-align_right slds-p-around_small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[],&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Block-8&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;class&quot;:&quot;slds-text-align_right slds-p-around_small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element2block_element8&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;,&quot;uKey&quot;:&quot;1758035910765-954&quot;,&quot;datasourceKey&quot;:&quot;state0element2&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_3_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cstrong%3EDati%20FATCA/CRS%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_4_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Text-0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1758035910765-100&quot;,&quot;datasourceKey&quot;:&quot;state0element3&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_4_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Tipologia Soc. FATCA/CRS &quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{TipologiaSocietaFatca}&quot;,&quot;value&quot;:&quot;&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;-&quot;,&quot;value&quot;:&quot;0&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;Limited FFI&quot;,&quot;value&quot;:&quot;1&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;label&quot;:&quot;NPFI&quot;,&quot;value&quot;:&quot;2&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:2},{&quot;label&quot;:&quot;Passive NFFE&quot;,&quot;value&quot;:&quot;3&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:3},{&quot;label&quot;:&quot;Active NFFE&quot;,&quot;value&quot;:&quot;4&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:4},{&quot;label&quot;:&quot;Specified US Person&quot;,&quot;value&quot;:&quot;5&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;label&quot;:&quot;Not Specified US Person&quot;,&quot;value&quot;:&quot;6&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;label&quot;:&quot;Direct Reporting NFFE / Sponsored Direct Reporting NFFE&quot;,&quot;value&quot;:&quot;7&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:7},{&quot;label&quot;:&quot;FI / Financial Institution&quot;,&quot;value&quot;:&quot;8&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;label&quot;:&quot;Limited Branch FFI&quot;,&quot;value&quot;:&quot;9&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:9},{&quot;label&quot;:&quot;Partecipating FFI&quot;,&quot;value&quot;:&quot;10&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:10},{&quot;label&quot;:&quot;Registered Deemed Compliant FFI&quot;,&quot;value&quot;:&quot;11&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:11},{&quot;label&quot;:&quot;Certified Deemed Compliant FFI&quot;,&quot;value&quot;:&quot;12&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:12},{&quot;label&quot;:&quot;Exempt Beneficial Owners&quot;,&quot;value&quot;:&quot;13&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:13}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742890395667-8zbdgagyp&quot;,&quot;label&quot;:&quot;GiinWritable&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891693950&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagGiin&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;MinLenghtGiin&quot;,&quot;fieldValue&quot;:&quot;16&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-3&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-10&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;2&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-23&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;7&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-44&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;8&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-65&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;9&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-90&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;10&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-119&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;11&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-152&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;12&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-189&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;13&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742890577174-nt5m1gp3t&quot;,&quot;label&quot;:&quot;GiinNotWritable&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891714134&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagGiin&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;MinLenghtGiin&quot;,&quot;fieldValue&quot;:&quot;0&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-230&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;3&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-237&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-253&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;5&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-266&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;6&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742890395667-8zbdgagyp&quot;,&quot;label&quot;:&quot;GiinWritable&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891693950&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagGiin&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;MinLenghtGiin&quot;,&quot;fieldValue&quot;:&quot;16&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-3&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-10&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;2&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-23&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;7&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-44&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;8&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-65&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;9&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-90&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;10&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-119&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;11&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-152&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;12&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-189&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;13&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742890577174-nt5m1gp3t&quot;,&quot;label&quot;:&quot;GiinNotWritable&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891714134&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;FlagGiin&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;-&quot;},{&quot;fieldName&quot;:&quot;MinLenghtGiin&quot;,&quot;fieldValue&quot;:&quot;0&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-230&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;3&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-237&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-253&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;5&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;},{&quot;id&quot;:&quot;state-new-condition-266&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;6&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-5-Select-0&quot;,&quot;datasourceKey&quot;:&quot;state0element4block_element0&quot;}],&quot;elementLabel&quot;:&quot;Block-6&quot;,&quot;uKey&quot;:&quot;1758035910765-248&quot;,&quot;datasourceKey&quot;:&quot;state0element4&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Costituzione/residenza fiscale USA&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{ResidenzaFiscaleUsa}&quot;,&quot;value&quot;:&quot;&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;Si&quot;,&quot;value&quot;:&quot;true&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;No&quot;,&quot;value&quot;:&quot;false&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:1}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742891761214-3lu01w710&quot;,&quot;label&quot;:&quot;TinNonObbligatorio&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891820173&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;MinLenghtTin&quot;,&quot;fieldValue&quot;:&quot;0&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-5&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742891858312-n2y3faslb&quot;,&quot;label&quot;:&quot;TinObbligatorio&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891923632&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;MinLenghtTin&quot;,&quot;fieldValue&quot;:&quot;9&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-12&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742891761214-3lu01w710&quot;,&quot;label&quot;:&quot;TinNonObbligatorio&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891820173&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;MinLenghtTin&quot;,&quot;fieldValue&quot;:&quot;0&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-5&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742891858312-n2y3faslb&quot;,&quot;label&quot;:&quot;TinObbligatorio&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742891923632&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;MinLenghtTin&quot;,&quot;fieldValue&quot;:&quot;9&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-12&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;elementLabel&quot;:&quot;Block-7-Select-0&quot;,&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;parsedProperty&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Residenza fiscale in uno stato diverso da Italia e USA&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{ResidenzaFiscaleStatoEstero}&quot;,&quot;value&quot;:&quot;&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;Si&quot;,&quot;value&quot;:&quot;true&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;No&quot;,&quot;value&quot;:&quot;false&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:1}]},&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;elementLabel&quot;:&quot;Block-7-Select-1&quot;,&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: left !important;&quot;,&quot;style&quot;:&quot;      \n         text-align: left !important;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;parsedProperty&quot;:{&quot;value&quot;:{&quot;textAlign&quot;:&quot;left&quot;},&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}}},{&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_2_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;TIN USA&quot;,&quot;fieldBinding&quot;:&quot;{Tin}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{ResidenzaFiscaleUsa}&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;minLength&quot;,&quot;value&quot;:&quot;9&quot;,&quot;id&quot;:1},{&quot;label&quot;:&quot;maxLength&quot;,&quot;value&quot;:&quot;9&quot;,&quot;id&quot;:2},{&quot;label&quot;:&quot;messageWhenTooShort&quot;,&quot;value&quot;:&quot;Inserire 9 caratteri&quot;,&quot;id&quot;:3}]},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-73&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;}},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-7-Text-2&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element2&quot;},{&quot;key&quot;:&quot;element_element_block_5_0_outputField_3_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3EInserire%209%20caratteri%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Status&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;ErrorType&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;2&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-7-Text-3&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element3&quot;},{&quot;key&quot;:&quot;element_element_block_5_0_block_4_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;0&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-9&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-8&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;&lt;=&quot;,&quot;value&quot;:&quot;2&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-36&quot;,&quot;field&quot;:&quot;TipologiaSocietaFatca&quot;,&quot;operator&quot;:&quot;&gt;=&quot;,&quot;value&quot;:&quot;7&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3EGIIN%20USA%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-7-Text-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_5_0_block_4_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_5_0_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element4block_element0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Label&quot;,&quot;fieldBinding&quot;:&quot;{Giin1}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747225123452-jwl9fr7sg&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749456321233&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;minLength&quot;:6,&quot;maxLength&quot;:6},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747225123452-jwl9fr7sg&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749456321233&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;}},&quot;elementLabel&quot;:&quot;Block-7-Text-6&quot;,&quot;key&quot;:&quot;element_element_element_block_5_0_block_4_0_baseInputElement_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_5_0_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element4block_element1&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;value&quot;:&quot;&quot;,&quot;placeholder&quot;:&quot;.&quot;,&quot;readOnly&quot;:true,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-7-Text-7&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_5_0_block_4_0_baseInputElement_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_5_0_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element4block_element2&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Label&quot;,&quot;fieldBinding&quot;:&quot;{Giin2}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747225123452-jwl9fr7sg&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749456343262&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;minLength&quot;:5,&quot;maxLength&quot;:5},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747225123452-jwl9fr7sg&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749456343262&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;}},&quot;elementLabel&quot;:&quot;Block-7-Text-8&quot;,&quot;key&quot;:&quot;element_element_element_block_5_0_block_4_0_baseInputElement_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_5_0_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element4block_element3&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;value&quot;:&quot;&quot;,&quot;placeholder&quot;:&quot;.&quot;,&quot;readOnly&quot;:true,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-7-Text-9&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_5_0_block_4_0_baseInputElement_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_5_0_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element4block_element4&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Label&quot;,&quot;fieldBinding&quot;:&quot;{Giin3}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747225123452-jwl9fr7sg&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749456357963&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;minLength&quot;:2,&quot;maxLength&quot;:2},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747225123452-jwl9fr7sg&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749456357963&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;}},&quot;elementLabel&quot;:&quot;Block-7-Text-10&quot;,&quot;key&quot;:&quot;element_element_element_block_5_0_block_4_0_baseInputElement_5_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_5_0_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element4block_element5&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;value&quot;:&quot;&quot;,&quot;placeholder&quot;:&quot;.&quot;,&quot;readOnly&quot;:true,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-7-Text-11&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_5_0_block_4_0_baseInputElement_6_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_5_0_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element4block_element6&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Label&quot;,&quot;fieldBinding&quot;:&quot;{Giin4}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;variant&quot;,&quot;value&quot;:&quot;label-hidden&quot;,&quot;id&quot;:0}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747225123452-jwl9fr7sg&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749456370655&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;minLength&quot;:3,&quot;maxLength&quot;:3},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1747225123452-jwl9fr7sg&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749456370655&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Giin&quot;,&quot;fieldValue&quot;:&quot;{Giin1}.{Giin2}.{Giin3}.{Giin4}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;}},&quot;elementLabel&quot;:&quot;Block-7-Text-12&quot;,&quot;key&quot;:&quot;element_element_element_block_5_0_block_4_0_baseInputElement_7_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_5_0_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element4block_element7&quot;}],&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-7-Block-4&quot;,&quot;datasourceKey&quot;:&quot;state0element5block_element4&quot;}],&quot;elementLabel&quot;:&quot;Block-7&quot;,&quot;uKey&quot;:&quot;1758035910765-769&quot;,&quot;datasourceKey&quot;:&quot;state0element5&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_6_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3EErrore%20nel%20caricamento%20dati%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_6_0&quot;,&quot;elementLabel&quot;:&quot;Block-7-Text-0&quot;,&quot;datasourceKey&quot;:&quot;state0element6block_element0&quot;,&quot;uKey&quot;:&quot;1758551928757-766&quot;}],&quot;elementLabel&quot;:&quot;Block-8&quot;,&quot;uKey&quot;:&quot;1758035910765-52&quot;,&quot;datasourceKey&quot;:&quot;state0element6&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_7_0_block_0_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_7_0_block_0_0_action_0_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Annulla&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1742897557688-3mr4r2936&quot;,&quot;label&quot;:&quot;Reset&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747652487219&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;Status&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;ErrorType&quot;,&quot;fieldValue&quot;:&quot;0&quot;}]},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1742897503631-spa7dwkvu&quot;,&quot;label&quot;:&quot;CloseModal&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742897691602&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;Custom&quot;,&quot;eventName&quot;:&quot;closemodal&quot;,&quot;composed&quot;:true,&quot;bubbles&quot;:true},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_element_block_9_0_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Block-0-Action-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element7block_element0block_element0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_7_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Block-0&quot;,&quot;datasourceKey&quot;:&quot;state0element7block_element0&quot;},{&quot;key&quot;:&quot;element_element_block_7_0_block_1_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_7_0_block_1_0_action_0_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Conferma&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1758556188270-dtraubv2p&quot;,&quot;label&quot;:&quot;ErrorOff&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758621193371&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;errorResidenzaEstera&quot;,&quot;fieldValue&quot;:&quot;false&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-703&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-711&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-710&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-738&quot;,&quot;field&quot;:&quot;record.Estero&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}],&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;actionIndex&quot;:0},{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758621211680&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestPost_Fatca\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ciu\&quot;:\&quot;{Ciu}\&quot;,\&quot;compagnia\&quot;:\&quot;{Compagnia}\&quot;,\&quot;cellaCensuaria\&quot;:\&quot;{CellaCensuaria}\&quot;,\&quot;indirizzoBreve\&quot;:\&quot;{IndirizzoBreve}\&quot;,\&quot;cap\&quot;:\&quot;{CAP}\&quot;,\&quot;codiceBelfioreComune\&quot;:\&quot;{CodiceBelfioreComune}\&quot;,\&quot;comune\&quot;:\&quot;{Comune}\&quot;,\&quot;flagSedeLegale\&quot;:\&quot;{FlagSedeLegale}\&quot;,\&quot;indirizzoCompleto\&quot;:\&quot;{IndirizzoCompleto}\&quot;,\&quot;numeroCivico\&quot;:\&quot;{NumeroCivico}\&quot;,\&quot;giin\&quot;:\&quot;{Giin}\&quot;,\&quot;provincia\&quot;:\&quot;{Provincia}\&quot;,\&quot;tin\&quot;:\&quot;{Tin}\&quot;,\&quot;tipoIndirizzo\&quot;:\&quot;{TipoIndirizzo}\&quot;,\&quot;tipologiaSocietaFatca\&quot;:\&quot;{TipologiaSocietaFatca}\&quot;,\&quot;stato\&quot;:\&quot;{Stato}\&quot;,\&quot;siglaProvincia\&quot;:\&quot;{SiglaProvincia}\&quot;,\&quot;residenzaFiscaleUsa\&quot;:\&quot;{ResidenzaFiscaleUsa}\&quot;,\&quot;residenzaFiscaleStatoEstero\&quot;:\&quot;{ResidenzaFiscaleStatoEstero}\&quot;,\&quot;ActionType\&quot;:\&quot;ModificaFatcaPG\&quot;,\&quot;RecordId\&quot;:\&quot;{RecordId}\&quot;,\&quot;UserId\&quot;:\&quot;{User.userId}\&quot;,\&quot;localitaEstera\&quot;:\&quot;{LocalitaEstera}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;Ciu\\\&quot;:\\\&quot;{Ciu}\\\&quot;,\\\&quot;Compagnia\\\&quot;:\\\&quot;{Compagnia}\\\&quot;,\\\&quot;CellaCensuaria\\\&quot;:\\\&quot;{CellaCensuaria}\\\&quot;,\\\&quot;IndirizzoBreve\\\&quot;:\\\&quot;{IndirizzoBreve}\\\&quot;,\\\&quot;CAP\\\&quot;:\\\&quot;{CAP}\\\&quot;,\\\&quot;CodiceBelfioreComune\\\&quot;:\\\&quot;{CodiceBelfioreComune}\\\&quot;,\\\&quot;Comune\\\&quot;:\\\&quot;{Comune}\\\&quot;,\\\&quot;FlagSedeLegale\\\&quot;:\\\&quot;{FlagSedeLegale}\\\&quot;,\\\&quot;IndirizzoCompleto\\\&quot;:\\\&quot;{IndirizzoCompleto}\\\&quot;,\\\&quot;NumeroCivico\\\&quot;:\\\&quot;{NumeroCivico}\\\&quot;,\\\&quot;Giin\\\&quot;:\\\&quot;{Giin}\\\&quot;,\\\&quot;Provincia\\\&quot;:\\\&quot;{Provincia}\\\&quot;,\\\&quot;Tin\\\&quot;:\\\&quot;{Tin}\\\&quot;,\\\&quot;TipoIndirizzo\\\&quot;:\\\&quot;{TipoIndirizzo}\\\&quot;,\\\&quot;TipologiaSocietaFatca\\\&quot;:\\\&quot;{TipologiaSocietaFatca}\\\&quot;,\\\&quot;Stato\\\&quot;:\\\&quot;{Stato}\\\&quot;,\\\&quot;SiglaProvincia\\\&quot;:\\\&quot;{SiglaProvincia}\\\&quot;,\\\&quot;ResidenzaFiscaleUsa\\\&quot;:\\\&quot;{ResidenzaFiscaleUsa}\\\&quot;,\\\&quot;ResidenzaFiscaleStatoEstero\\\&quot;:\\\&quot;{ResidenzaFiscaleStatoEstero}\\\&quot;,\\\&quot;RecordId\\\&quot;:\\\&quot;{RecordId}\\\&quot;,\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;,\\\&quot;LocalitaEstera\\\&quot;:\\\&quot;{LocalitaEstera}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;Ciu\&quot;,\&quot;val\&quot;:\&quot;7176310\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;Compagnia\&quot;,\&quot;val\&quot;:\&quot;unipolsai\&quot;,\&quot;id\&quot;:4},{\&quot;name\&quot;:\&quot;CellaCensuaria\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:6},{\&quot;name\&quot;:\&quot;IndirizzoBreve\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:8},{\&quot;name\&quot;:\&quot;CAP\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;CodiceBelfioreComune\&quot;,\&quot;val\&quot;:\&quot;G157\&quot;,\&quot;id\&quot;:12},{\&quot;name\&quot;:\&quot;Comune\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:14},{\&quot;name\&quot;:\&quot;FlagSedeLegale\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:16},{\&quot;name\&quot;:\&quot;IndirizzoCompleto\&quot;,\&quot;val\&quot;:\&quot;Via San Giovanni\&quot;,\&quot;id\&quot;:18},{\&quot;name\&quot;:\&quot;NumeroCivico\&quot;,\&quot;val\&quot;:\&quot;17\&quot;,\&quot;id\&quot;:20},{\&quot;name\&quot;:\&quot;Giin\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:22},{\&quot;name\&quot;:\&quot;Provincia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:24},{\&quot;name\&quot;:\&quot;Tin\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:26},{\&quot;name\&quot;:\&quot;TipoIndirizzo\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:28},{\&quot;name\&quot;:\&quot;TipologiaSocietaFatca\&quot;,\&quot;val\&quot;:\&quot;1\&quot;,\&quot;id\&quot;:30},{\&quot;name\&quot;:\&quot;Stato\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:32},{\&quot;name\&quot;:\&quot;SiglaProvincia\&quot;,\&quot;val\&quot;:\&quot;AN\&quot;,\&quot;id\&quot;:34},{\&quot;name\&quot;:\&quot;ResidenzaFiscaleUsa\&quot;,\&quot;val\&quot;:\&quot;false\&quot;,\&quot;id\&quot;:36},{\&quot;name\&quot;:\&quot;ResidenzaFiscaleStatoEstero\&quot;,\&quot;val\&quot;:\&quot;false\&quot;,\&quot;id\&quot;:38},{\&quot;name\&quot;:\&quot;RecordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:61},{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:63},{\&quot;name\&quot;:\&quot;LocalitaEstera\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:45}]}&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-group-8&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-35&quot;,&quot;field&quot;:&quot;record.Estero&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]},{&quot;id&quot;:&quot;state-new-group-64&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-63&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}],&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;key&quot;:&quot;1742771490462-u2g8lwkph&quot;,&quot;label&quot;:&quot;Send&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;1742897614245-r308ujzwr&quot;,&quot;label&quot;:&quot;Reload&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758621242070&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;eventName&quot;:&quot;refreshAll&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-328&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-327&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-353&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-352&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-413&quot;,&quot;field&quot;:&quot;record.Estero&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}],&quot;logicalOperator&quot;:&quot;||&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]},&quot;subType&quot;:&quot;PubSub&quot;,&quot;message&quot;:&quot;refresh&quot;},&quot;actionIndex&quot;:2},{&quot;key&quot;:&quot;1742897650655-wpfsil70m&quot;,&quot;label&quot;:&quot;CloseModal&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758621263618&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;Custom&quot;,&quot;eventName&quot;:&quot;closemodal&quot;,&quot;composed&quot;:true,&quot;bubbles&quot;:true,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-483&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-482&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-508&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-507&quot;,&quot;field&quot;:&quot;record.Estero&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-568&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}],&quot;logicalOperator&quot;:&quot;||&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;actionIndex&quot;:3},{&quot;key&quot;:&quot;1758551927379-fju1186tx&quot;,&quot;label&quot;:&quot;ErrorStatoEstero&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758621082104&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;errorResidenzaEstera&quot;,&quot;fieldValue&quot;:&quot;true&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-773&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-320&quot;,&quot;field&quot;:&quot;record.Estero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;actionIndex&quot;:4}],&quot;showSpinner&quot;:&quot;true&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;brand&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_element_block_7_0_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Block-0-clone-0-Action-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element7block_element1block_element0&quot;,&quot;parsedProperty&quot;:{&quot;label&quot;:&quot;Conferma&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:{&quot;success&quot;:true,&quot;Message&quot;:&quot;&quot;,&quot;Status&quot;:true,&quot;ErrorType&quot;:0},&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:{&quot;success&quot;:true,&quot;Message&quot;:&quot;&quot;,&quot;Status&quot;:true,&quot;ErrorType&quot;:0},&quot;actionList&quot;:[{&quot;key&quot;:&quot;1758556188270-dtraubv2p&quot;,&quot;label&quot;:&quot;ErrorOff&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758621193371&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;errorResidenzaEstera&quot;,&quot;fieldValue&quot;:&quot;false&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-703&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-711&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-710&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-738&quot;,&quot;field&quot;:&quot;record.Estero&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}],&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;actionIndex&quot;:0},{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758621211680&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestPost_Fatca\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ciu\&quot;:\&quot;{Ciu}\&quot;,\&quot;compagnia\&quot;:\&quot;{Compagnia}\&quot;,\&quot;cellaCensuaria\&quot;:\&quot;{CellaCensuaria}\&quot;,\&quot;indirizzoBreve\&quot;:\&quot;{IndirizzoBreve}\&quot;,\&quot;cap\&quot;:\&quot;{CAP}\&quot;,\&quot;codiceBelfioreComune\&quot;:\&quot;{CodiceBelfioreComune}\&quot;,\&quot;comune\&quot;:\&quot;{Comune}\&quot;,\&quot;flagSedeLegale\&quot;:\&quot;{FlagSedeLegale}\&quot;,\&quot;indirizzoCompleto\&quot;:\&quot;{IndirizzoCompleto}\&quot;,\&quot;numeroCivico\&quot;:\&quot;{NumeroCivico}\&quot;,\&quot;giin\&quot;:\&quot;{Giin}\&quot;,\&quot;provincia\&quot;:\&quot;{Provincia}\&quot;,\&quot;tin\&quot;:\&quot;{Tin}\&quot;,\&quot;tipoIndirizzo\&quot;:\&quot;{TipoIndirizzo}\&quot;,\&quot;tipologiaSocietaFatca\&quot;:\&quot;{TipologiaSocietaFatca}\&quot;,\&quot;stato\&quot;:\&quot;{Stato}\&quot;,\&quot;siglaProvincia\&quot;:\&quot;{SiglaProvincia}\&quot;,\&quot;residenzaFiscaleUsa\&quot;:\&quot;{ResidenzaFiscaleUsa}\&quot;,\&quot;residenzaFiscaleStatoEstero\&quot;:\&quot;{ResidenzaFiscaleStatoEstero}\&quot;,\&quot;ActionType\&quot;:\&quot;ModificaFatcaPG\&quot;,\&quot;RecordId\&quot;:\&quot;{RecordId}\&quot;,\&quot;UserId\&quot;:\&quot;{User.userId}\&quot;,\&quot;localitaEstera\&quot;:\&quot;{LocalitaEstera}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;Ciu\\\&quot;:\\\&quot;{Ciu}\\\&quot;,\\\&quot;Compagnia\\\&quot;:\\\&quot;{Compagnia}\\\&quot;,\\\&quot;CellaCensuaria\\\&quot;:\\\&quot;{CellaCensuaria}\\\&quot;,\\\&quot;IndirizzoBreve\\\&quot;:\\\&quot;{IndirizzoBreve}\\\&quot;,\\\&quot;CAP\\\&quot;:\\\&quot;{CAP}\\\&quot;,\\\&quot;CodiceBelfioreComune\\\&quot;:\\\&quot;{CodiceBelfioreComune}\\\&quot;,\\\&quot;Comune\\\&quot;:\\\&quot;{Comune}\\\&quot;,\\\&quot;FlagSedeLegale\\\&quot;:\\\&quot;{FlagSedeLegale}\\\&quot;,\\\&quot;IndirizzoCompleto\\\&quot;:\\\&quot;{IndirizzoCompleto}\\\&quot;,\\\&quot;NumeroCivico\\\&quot;:\\\&quot;{NumeroCivico}\\\&quot;,\\\&quot;Giin\\\&quot;:\\\&quot;{Giin}\\\&quot;,\\\&quot;Provincia\\\&quot;:\\\&quot;{Provincia}\\\&quot;,\\\&quot;Tin\\\&quot;:\\\&quot;{Tin}\\\&quot;,\\\&quot;TipoIndirizzo\\\&quot;:\\\&quot;{TipoIndirizzo}\\\&quot;,\\\&quot;TipologiaSocietaFatca\\\&quot;:\\\&quot;{TipologiaSocietaFatca}\\\&quot;,\\\&quot;Stato\\\&quot;:\\\&quot;{Stato}\\\&quot;,\\\&quot;SiglaProvincia\\\&quot;:\\\&quot;{SiglaProvincia}\\\&quot;,\\\&quot;ResidenzaFiscaleUsa\\\&quot;:\\\&quot;{ResidenzaFiscaleUsa}\\\&quot;,\\\&quot;ResidenzaFiscaleStatoEstero\\\&quot;:\\\&quot;{ResidenzaFiscaleStatoEstero}\\\&quot;,\\\&quot;RecordId\\\&quot;:\\\&quot;{RecordId}\\\&quot;,\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;,\\\&quot;LocalitaEstera\\\&quot;:\\\&quot;{LocalitaEstera}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;Ciu\&quot;,\&quot;val\&quot;:\&quot;7176310\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;Compagnia\&quot;,\&quot;val\&quot;:\&quot;unipolsai\&quot;,\&quot;id\&quot;:4},{\&quot;name\&quot;:\&quot;CellaCensuaria\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:6},{\&quot;name\&quot;:\&quot;IndirizzoBreve\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:8},{\&quot;name\&quot;:\&quot;CAP\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;CodiceBelfioreComune\&quot;,\&quot;val\&quot;:\&quot;G157\&quot;,\&quot;id\&quot;:12},{\&quot;name\&quot;:\&quot;Comune\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:14},{\&quot;name\&quot;:\&quot;FlagSedeLegale\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:16},{\&quot;name\&quot;:\&quot;IndirizzoCompleto\&quot;,\&quot;val\&quot;:\&quot;Via San Giovanni\&quot;,\&quot;id\&quot;:18},{\&quot;name\&quot;:\&quot;NumeroCivico\&quot;,\&quot;val\&quot;:\&quot;17\&quot;,\&quot;id\&quot;:20},{\&quot;name\&quot;:\&quot;Giin\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:22},{\&quot;name\&quot;:\&quot;Provincia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:24},{\&quot;name\&quot;:\&quot;Tin\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:26},{\&quot;name\&quot;:\&quot;TipoIndirizzo\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:28},{\&quot;name\&quot;:\&quot;TipologiaSocietaFatca\&quot;,\&quot;val\&quot;:\&quot;1\&quot;,\&quot;id\&quot;:30},{\&quot;name\&quot;:\&quot;Stato\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:32},{\&quot;name\&quot;:\&quot;SiglaProvincia\&quot;,\&quot;val\&quot;:\&quot;AN\&quot;,\&quot;id\&quot;:34},{\&quot;name\&quot;:\&quot;ResidenzaFiscaleUsa\&quot;,\&quot;val\&quot;:\&quot;false\&quot;,\&quot;id\&quot;:36},{\&quot;name\&quot;:\&quot;ResidenzaFiscaleStatoEstero\&quot;,\&quot;val\&quot;:\&quot;false\&quot;,\&quot;id\&quot;:38},{\&quot;name\&quot;:\&quot;RecordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:61},{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:63},{\&quot;name\&quot;:\&quot;LocalitaEstera\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:45}]}&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-group-8&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-35&quot;,&quot;field&quot;:&quot;record.Estero&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]},{&quot;id&quot;:&quot;state-new-group-64&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-63&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleUsa&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}],&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;key&quot;:&quot;1742771490462-u2g8lwkph&quot;,&quot;label&quot;:&quot;Send&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;1742897614245-r308ujzwr&quot;,&quot;label&quot;:&quot;Reload&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758621242070&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;eventName&quot;:&quot;refreshAll&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-328&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-327&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-353&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-352&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-413&quot;,&quot;field&quot;:&quot;record.Estero&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}],&quot;logicalOperator&quot;:&quot;||&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]},&quot;subType&quot;:&quot;PubSub&quot;,&quot;message&quot;:&quot;refresh&quot;},&quot;actionIndex&quot;:2},{&quot;key&quot;:&quot;1742897650655-wpfsil70m&quot;,&quot;label&quot;:&quot;CloseModal&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758621263618&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;Custom&quot;,&quot;eventName&quot;:&quot;closemodal&quot;,&quot;composed&quot;:true,&quot;bubbles&quot;:true,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-483&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-482&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-group-508&quot;,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-507&quot;,&quot;field&quot;:&quot;record.Estero&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-568&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}],&quot;logicalOperator&quot;:&quot;||&quot;}],&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;actionIndex&quot;:3},{&quot;key&quot;:&quot;1758551927379-fju1186tx&quot;,&quot;label&quot;:&quot;ErrorStatoEstero&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758621082104&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;errorResidenzaEstera&quot;,&quot;fieldValue&quot;:&quot;true&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-773&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-320&quot;,&quot;field&quot;:&quot;record.Estero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;actionIndex&quot;:4}],&quot;showSpinner&quot;:&quot;true&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;brand&quot;,&quot;flyoutDetails&quot;:{}}}],&quot;parentElementKey&quot;:&quot;element_block_7_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Block-0-clone-0&quot;,&quot;datasourceKey&quot;:&quot;state0element7block_element1&quot;}],&quot;elementLabel&quot;:&quot;Block-9&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1758035910765-482&quot;,&quot;datasourceKey&quot;:&quot;state0element7&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[],&quot;elementLabel&quot;:&quot;Block-10&quot;,&quot;uKey&quot;:&quot;1758035910765-909&quot;,&quot;datasourceKey&quot;:&quot;state0element8&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-646&quot;,&quot;field&quot;:&quot;errorResidenzaEstera&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-653&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3EErrore%20-%20Inserire%20almeno%20una%20Residenza%20Fiscale%20Estera%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-8-Text-0-clone-0&quot;,&quot;datasourceKey&quot;:&quot;state0element9block_element0&quot;,&quot;uKey&quot;:&quot;1758551948722-599&quot;,&quot;key&quot;:&quot;element_element_block_9_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_9_0&quot;}],&quot;elementLabel&quot;:&quot;Block-8-clone-0&quot;,&quot;uKey&quot;:&quot;1758552150757-198&quot;,&quot;datasourceKey&quot;:&quot;state0element9&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_10_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cstrong%3EResidenze%20Fiscali%20Estere%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_9_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element10block_element0&quot;},{&quot;key&quot;:&quot;element_element_block_10_0_action_1_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Inserisci&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747744967247&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;cardName&quot;:&quot;DA_NewResidenzaEstera&quot;,&quot;flyoutLwc&quot;:&quot;DA_NewResidenzaEstera&quot;,&quot;cardNode&quot;:&quot;{record}&quot;},&quot;key&quot;:&quot;1742483545155-lqf8gsfd7&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;flyoutChannel&quot;:&quot;&quot;,&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;ResidenzaFiscaleStatoEstero&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_9_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Action-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element10block_element1&quot;}],&quot;elementLabel&quot;:&quot;Block-11&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1758035910765-47&quot;,&quot;datasourceKey&quot;:&quot;state0element10&quot;},{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;DA_ModificaFatcaEstero&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;{record.Estero}&quot;,&quot;selectedState&quot;:&quot;Active&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;FlexCard-12&quot;,&quot;uKey&quot;:&quot;1758035910765-550&quot;,&quot;datasourceKey&quot;:&quot;state0element11&quot;}]}},&quot;childCards&quot;:[&quot;DA_ModificaFatcaEstero&quot;],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n\&quot;success\&quot; : true,\n\&quot;Message\&quot; : \&quot;\&quot;,\n\&quot;Status\&quot; : true,\n\&quot;ErrorType\&quot; : 0\n}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;title&quot;:&quot;FlexFatcaModifica&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfFlexFatcaModifica_PostInsert_1_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003F85BSAS&quot;,&quot;MasterLabel&quot;:&quot;cfFlexFatcaModifica_PostInsert_1_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;dynamicCanvasWidth&quot;:{&quot;type&quot;:&quot;tablet_l&quot;},&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;FlexFatcaModifica&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56,&quot;targetConfigs&quot;:&quot;&quot;},&quot;sessionVars&quot;:[],&quot;xmlJson&quot;:[]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;success&quot;:true,&quot;Message&quot;:&quot;&quot;,&quot;Status&quot;:true,&quot;ErrorType&quot;:0}</sampleDataSourceResponse>
    <stylingConfiguration>{}</stylingConfiguration>
    <versionNumber>4</versionNumber>
</OmniUiCard>
