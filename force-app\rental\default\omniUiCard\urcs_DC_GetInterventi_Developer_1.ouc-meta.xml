<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Developer</authorName>
    <clonedFromOmniUiCardKey>urcs_DC_GetSinistri/Developer/1.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;Remote\&quot;][\&quot;data\&quot;]&quot;,&quot;ipMethod&quot;:&quot;URCS_Interventi&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;02i9V000006yF7BQAU&quot;,&quot;id&quot;:1}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>urcs_DC_GetInterventi</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:false,&quot;issortavailable&quot;:true,&quot;cellLevelEdit&quot;:true,&quot;pagelimit&quot;:3,&quot;groupOrder&quot;:&quot;asc&quot;,&quot;searchDatatable&quot;:&quot;&quot;,&quot;columns&quot;:[{&quot;fieldName&quot;:&quot;TS_CREAZIONE__c&quot;,&quot;label&quot;:&quot;Data&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;datetime&quot;,&quot;format&quot;:&quot;DD/MM/YYYY, HH:mm&quot;},{&quot;fieldName&quot;:&quot;KQ_ID_INTERVENTO__c&quot;,&quot;label&quot;:&quot;Id Richiesta Intervento&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;ID_INTERVENTO__c&quot;,&quot;label&quot;:&quot;Id Intervento&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;SINISTRO_DATA_INVIO__c&quot;,&quot;label&quot;:&quot;Sinistro - Data Invio&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;datetime&quot;,&quot;format&quot;:&quot;DD/MM/YYYY, HH:mm&quot;},{&quot;fieldName&quot;:&quot;ID_CONTRATTO__c&quot;,&quot;label&quot;:&quot;Id Contratto&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;ID_AUTO__c&quot;,&quot;label&quot;:&quot;Id Auto&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;ID_DRIVER__c&quot;,&quot;label&quot;:&quot;Id Driver&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;,&quot;label&quot;:&quot;Denominazione Driver&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;ID_FORNITORE__c&quot;,&quot;label&quot;:&quot;Id Fornitore&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;DS_FORNITORE_DENOMINAZIONECOMPUTED__c&quot;,&quot;label&quot;:&quot;Denominazione Fornitore&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;TS_INIZIO_EFFETTIVO__c&quot;,&quot;label&quot;:&quot;Data Inizio Effettivo&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;datetime&quot;,&quot;format&quot;:&quot;DD/MM/YYYY, HH:mm&quot;},{&quot;fieldName&quot;:&quot;TS_USCITA_OFFICINA__c&quot;,&quot;label&quot;:&quot;Data Uscita Officina&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;datetime&quot;,&quot;format&quot;:&quot;DD/MM/YYYY, HH:mm&quot;},{&quot;fieldName&quot;:&quot;TS_CHIUSURA__c&quot;,&quot;label&quot;:&quot;Data Chiusura&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;datetime&quot;,&quot;format&quot;:&quot;DD/MM/YYYY, HH:mm&quot;},{&quot;fieldName&quot;:&quot;TS_PROGRAMMAZIONE__c&quot;,&quot;label&quot;:&quot;Data Presa in Carico&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;datetime&quot;,&quot;format&quot;:&quot;DD/MM/YYYY, HH:mm&quot;},{&quot;fieldName&quot;:&quot;TS_INIZIO_PREVISTO__c&quot;,&quot;label&quot;:&quot;Data di Approvazione&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;datetime&quot;,&quot;format&quot;:&quot;DD/MM/YYYY, HH:mm&quot;},{&quot;fieldName&quot;:&quot;TS_FINE_EFFETTIVA__c&quot;,&quot;label&quot;:&quot;Data Esecuzione Intervento&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;datetime&quot;,&quot;format&quot;:&quot;DD/MM/YYYY, HH:mm&quot;},{&quot;fieldName&quot;:&quot;TS_FINE_PREVISTA__c&quot;,&quot;label&quot;:&quot;Data di Rifiuto&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;datetime&quot;,&quot;format&quot;:&quot;DD/MM/YYYY, HH:mm&quot;},{&quot;fieldName&quot;:&quot;DS_OPZIONE_EROGAZIONE__c&quot;,&quot;label&quot;:&quot;Data di Richiesta&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;FL_PROGRAMMATO__c&quot;,&quot;label&quot;:&quot;Codice Intervento Fornitore&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;}],&quot;records&quot;:&quot;{records}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;datasourceKey&quot;:&quot;state0element0&quot;,&quot;uKey&quot;:&quot;1752238851843-708&quot;,&quot;elementLabel&quot;:&quot;Data Table-0&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[],&quot;blankCardState&quot;:false}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;Remote\&quot;][\&quot;data\&quot;]&quot;,&quot;ipMethod&quot;:&quot;URCS_Interventi&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;02i9V000006yF7BQAU&quot;,&quot;id&quot;:1}]},&quot;title&quot;:&quot;urcs_DC_GetInterventi&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX0FwcFBhZ2UiPgogICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIi8+CiAgICAgICAgICAgICAgICA8L3RhcmdldENvbmZpZz4KICAgICAgICAgICAgICAgIDx0YXJnZXRDb25maWcgdGFyZ2V0cz0ibGlnaHRuaW5nX19SZWNvcmRQYWdlIj4KICAgICAgICAgICAgICAgICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgICAgICAgICAgIDwvdGFyZ2V0Q29uZmlnPg==&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;apiVersion&quot;:61,&quot;description&quot;:&quot;&quot;,&quot;masterLabel&quot;:&quot;urcs_DC_GetInterventi&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}}]}],&quot;isRepeatable&quot;:false}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;RemoteStatus&quot;:true,&quot;Set ValuesStatus&quot;:true,&quot;Remote&quot;:{&quot;error&quot;:&quot;OK&quot;,&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;data&quot;:[{&quot;TS_CHIUSURA__c&quot;:&quot;2017-09-01T00:00:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2017-09-01T12:00:00.000+0000&quot;,&quot;TS_INIZIO_EFFETTIVO__c&quot;:&quot;2017-09-01T08:00:00.000+0000&quot;,&quot;TS_USCITA_OFFICINA__c&quot;:&quot;2017-09-01T16:00:00.000+0000&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;Goi Enedino&quot;,&quot;DS_FORNITORE_DENOMINAZIONECOMPUTED__c&quot;:&quot;Officina Test&quot;,&quot;ID_AUTO__c&quot;:405287,&quot;ID_CONTRATTO__c&quot;:1095593,&quot;ID_DRIVER__c&quot;:12345,&quot;ID_FORNITORE__c&quot;:67890,&quot;ID_INTERVENTO__c&quot;:987654,&quot;KQ_ID_INTERVENTO__c&quot;:&quot;REQ-123456&quot;,&quot;SINISTRO_DATA_INVIO__c&quot;:&quot;2017-08-30T10:00:00.000+0000&quot;,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_INTERVENTI__dlm/qDI000004066584GAA&quot;,&quot;type&quot;:&quot;UR_INTERVENTI__dlm&quot;}}]},&quot;Set Values&quot;:{&quot;methodExecute&quot;:&quot;init&quot;,&quot;recordId&quot;:&quot;02i9V000006yF7BQAU&quot;},&quot;options&quot;:{&quot;forceQueueable&quot;:false,&quot;mockHttpResponse&quot;:null,&quot;vlcApexResponse&quot;:true,&quot;useFuture&quot;:false,&quot;isTestProcedure&quot;:false,&quot;resetCache&quot;:false,&quot;integrationProcedureKey&quot;:null,&quot;vlcIPData&quot;:null,&quot;OmniAnalyticsTrackingDebug&quot;:false,&quot;ignoreCache&quot;:false,&quot;shouldCommit&quot;:false,&quot;vlcTestSuiteUniqueKey&quot;:null,&quot;vlcTestUniqueKey&quot;:null,&quot;vlcCacheKey&quot;:null,&quot;continuationStepResult&quot;:null,&quot;vlcFilesMap&quot;:null,&quot;ParentInteractionToken&quot;:null,&quot;useQueueable&quot;:false,&quot;disableMetadataCache&quot;:false,&quot;isDebug&quot;:false,&quot;queueableChainable&quot;:false,&quot;useContinuation&quot;:false,&quot;chainable&quot;:false,&quot;ignoreMetadataPermissions&quot;:false,&quot;useHttpCalloutMock&quot;:false,&quot;useQueueableApexRemoting&quot;:false},&quot;response&quot;:{},&quot;ResponseStatus&quot;:true,&quot;recordId&quot;:&quot;02i9V000006yF7BQAU&quot;}</sampleDataSourceResponse>
    <stylingConfiguration>{}</stylingConfiguration>
    <versionNumber>1</versionNumber>
</OmniUiCard>
