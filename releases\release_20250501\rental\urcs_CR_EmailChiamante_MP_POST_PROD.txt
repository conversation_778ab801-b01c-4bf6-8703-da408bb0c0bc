------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
This manual procedure aims to change email templates
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------
 
1. Login to Salesforce
2. Open Setup - Search for Organization-Wide Addresses -
3. Create a new Organization-Wide Addresses with following values:
    Display Name: <EMAIL>
    Email Address: <EMAIL>
    Purpose: User Selection
    Select option:  Allow Only Selected Profiles to Use the From Address
    Profiles: Unipol Rental CS Standard User
    Save

3.1 Create a new Organization-Wide Addresses with following values:
    Display Name: <EMAIL> 
    Email Address: <EMAIL>
    Purpose: Default No-Reply Address
    Save

3.2 Open Setup - Search for Permission Set: Unipol Rental CS Manage Case
   On find settings search for: Organization-Wide Email Address Access
   Edit - add on Enabled Organization-Wide Email Addresses followiong address:
   <EMAIL>
   Save

3.3 Repeat step previously step for following permission set: 
   Unipol Rental CS Admin
   Unipol Rental CS Admin Light
   Unipol Rental CS Mulesoft Integration

4. Create Enhanced Letterhead
   - Search for "Enhanced Letterheads" in App Launcher
   - Serch for ur_letterhead
   - In Footer section, click Source button
   - In HTML body delete the existing code, paste the following code: 
   
   <p>&nbsp;</p> <div style="height: 3px; background-color: #0066cc; margin: 20px 0; width: 280px;">&nbsp;</div> <img alt="UnipolRental_Logo" src="https://unipol--devrental2.sandbox.file.force.com/file-asset-public/UnipolRental_Logo?oid=ID_ORG" style="max-width: 470.567px; width: 276.979px; height: 50.9896px;" title="UnipolRental_Logo" /><br /> <br /> <br /> <span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;"><strong><a href="https://www.unipolrental.it/">www.unipolrental.it</a></strong></span> <div style="height: 3px; background-color: #0066cc; margin: 20px 0; width: 280px;">&nbsp;</div> <p>&nbsp;</p> <div id="" style="left: 20px; top: 96.7708px;">&nbsp;</div> <div id="" style="left: 20px; top: 96.7708px;">&nbsp;</div>

   - Replace ID_ORG with the OID retrieved in step 1 and replace "unipol--devrental2" with the target organization name
   
   - Click Save


5. Create Email Template Folder
   - Search for "Email Templates" in App Launcher
   - Click New Folder
   - Set Name: urcs_EmailTemplates
   - Set Folder Unique Name: urcs_EmailTemplates
   - Click Save
   - Click share action on urcs_EmailTemplates Folder
   - Add Public groupt SOC_V with Access: Manage
   - Click share and Done

6. Create Case Creation Notification Email Template
   - Click New Email Template
   - Set Email Template Name: urcs_CaseNotificaCreazione
   - Set Related Entity Type: Case
   - Set Folder: urcs_EmailTemplates
   - Set Subject: UnipolRental - Conferma apertura richiesta {{{Case.CaseNumber}}}
   - Set Enhanced Letterhead: ur_letterhead
   - In HTML Value section, click Source
   - Paste the following HTML code:

<html style="overflow-y: hidden;">
<head>
	<title></title>
</head>
<body style="height: auto; min-height: auto;">
<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Gentile {{{Case.NomeChiamante__c}}},</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">le confermiamo di aver ricevuto la richiesta <strong>{{{Case.CaseNumber}}}</strong>, aperta in data <strong>{{{Case.CreatedDate}}}</strong>.</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Di seguito il riepilogo:<br />
<strong>Categoria</strong>: {{{Case.Categoria__c}}}<br />
<strong>Sotto-categoria</strong>: {{{Case.SottoCategoria__c}}}<br />
<strong>Richiesta</strong>: {{{Case.Subject}}}<br />
<strong>Targa</strong>: {{{Case.TargaVeicoloFormula__c}}}<br />
<strong>Contratto</strong>: {{{Case.ContractNameFormula__c}}}</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Il nostro team sta analizzando la sua richiesta e la contatter&agrave; al pi&ugrave; presto per fornirle maggiori dettagli o aggiornamenti.</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">La ringraziamo per aver scelto UnipolRental.<br />
Buona giornata,<br />
Servizio clienti UnipolRental</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;"><em>Questa &egrave; una mail automatica inviata da un indirizzo no-reply. La invitiamo a non rispondere.</em></span></p>
</body>
</html>

7.Save


8. Create Case Closure Notification Email Template
   - Click New Email Template
   - Set Email Template Name: urcs_CaseNotificaChiusura
   - Set Related Entity Type: Case
   - Set Folder: urcs_EmailTemplates
   - Set Subject: UnipolRental - Conferma chiusura richiesta {{{Case.CaseNumber}}}
   - Set Enhanced Letterhead: ur_letterhead
   - In HTML Value section, click Source
   - Paste the following HTML code:

<html style="overflow-y: hidden;">
<head>
	<title></title>
</head>
<body style="height: auto; min-height: auto;">
<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Gentile {{{Case.NomeChiamante__c}}},</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Le confermiamo che la sua richiesta <strong>{{{Case.CaseNumber}}}</strong>, aperta in data <strong>{{{Case.CreatedDate}}}</strong>, &egrave; stata gestita e risolta con successo.</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Di seguito il riepilogo:<br />
<strong>Categoria</strong>: {{{Case.Categoria__c}}}<br />
<strong>Sotto-categoria</strong>: {{{Case.SottoCategoria__c}}}<br />
<strong>Richiesta</strong>: {{{Case.Subject}}}<br />
<strong>Targa</strong>: {{{Case.TargaVeicoloFormula__c}}}<br />
<strong>Contratto</strong>: {{{Case.ContractNameFormula__c}}}<br />
<br />
La ringraziamo per aver scelto UnipolRental.<br />
Buona giornata,<br />
Servizio clienti UnipolRental</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;"><em>Questa &egrave; una mail automatica inviata da un indirizzo no-reply. La invitiamo a non rispondere.</em></span></p>
</body>
</html>

9.Save