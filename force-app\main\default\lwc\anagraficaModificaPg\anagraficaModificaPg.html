<template>
    <c-loader lwc:ref="loader"></c-loader>
    <div class="slds-col slds-border_bottom slds-p-around_xx-small slds-size_12-of-12" style="border-bottom: 2px solid rgb(204, 204, 204);">
        <div class="slds-grid slds-wrap">
            <div class="slds-col slds-size_12-of-12" style="text-align: center">
                <span style="font-size: 14pt">Modifica dettaglio anagrafica</span>
            </div>
        </div>
    </div>
    <c-elenco-errori show-error={showError} err-list={errList} title={titleError}></c-elenco-errori>
    <div if:true={initDone}>
        <div class="slds-col slds-p-around_xx-small slds-size_12-of-12">
            <div class="slds-col slds-p-around_x-small slds-size_12-of-12" style="background-color: rgb(243, 243, 243);">
                <span style="font-size: 10pt; font-weight: bold">Dati anagrafici</span>
            </div>
        </div>
        <c-anagrafica-pg
            lwc:ref="anagrafica"
            set-init="1"
            anagrafica-api={data.anagrafica}
            onanagrafica={handleAnagrafica}>
        </c-anagrafica-pg>
        <div class="slds-col slds-p-around_x-small slds-size_12-of-12" style="background-color: rgb(243, 243, 243);">
            <span style="font-size: 10pt; font-weight: bold">Sede legale</span>
        </div>
        <c-address
            lwc:ref="indirizzoSele"
            set-init="1"
            indirizzo={data.indirizzo.indirizzoCompleto}
            numero-civico={data.indirizzo.numeroCivico}
            stato={data.indirizzo.stato}
            provincia={data.indirizzo.abbreviazioneProvincia}
            comune={data.indirizzo.codiceBelfioreComune}
            localita={data.indirizzo.localita}
            cap={data.indirizzo.cap}
            presso={data.indirizzo.presso}
            edit={data.indirizzo.edit}
            skip-prima-normalizzazione={data.indirizzo.skipPrimaNormalizzazione}
            onaddress={handleIndirizzo}>
        </c-address>
        <!--
        indirizzo={data.indirizzoDomicilio.indirizzoCompleto}
                numero-civico={data.indirizzoDomicilio.numeroCivico}
                stato={data.indirizzoDomicilio.stato}
                provincia={data.indirizzoDomicilio.abbreviazioneProvincia}
                comune={data.indirizzoDomicilio.codiceBelfioreComune}
                localita={data.indirizzoDomicilio.localita}
                cap={data.indirizzoDomicilio.cap}
                presso={data.indirizzoDomicilio.presso}
                edit="false"
                onaddress={handleIndirizzoDomicilio}>
        -->
        <div class="slds-col slds-p-around_x-small slds-size_12-of-12" style="background-color: rgb(243, 243, 243);">
            <span style="font-size: 10pt; font-weight: bold">Tipo persona giuridica</span>
        </div>
        <c-modifica-anagrafica-persona-giuridica-cmp
            lwc:ref="personaGiuridica"
            set-init="1"
            professione={data.professione}
            onpersonagiuridica={handlePersonaGiuridica}
            >
        </c-modifica-anagrafica-persona-giuridica-cmp>
        <div class="slds-col slds-p-around_xx-small slds-size_12-of-12">
            <div class="slds-col slds-p-around_x-small slds-size_12-of-12" style="background-color: rgb(243, 243, 243);">
                <span style="font-size: 10pt; font-weight: bold">Propaga su altre compagnie</span>
            </div>
            <c-modifica-anagrafica-propagazione-compagnia-cmp
                lwc:ref="propagazione"
                account-detail-record-id={recordId}
                >
            </c-modifica-anagrafica-propagazione-compagnia-cmp>
        </div>
        <div class="slds-col slds-border_top slds-p-around_xx-small slds-size_12-of-12" style="background-color: rgb(243, 243, 243); border-top: 2px solid rgb(204, 204, 204);">
            <div class="slds-col slds-p-around_xx-small slds-size_12-of-12" style="text-align: center">
                <lightning-button 
                    label="Annulla" 
                    variant="neutral"
                    class="slds-m-right_small"
                    onclick={handleAnnulla}>
                </lightning-button>
                <lightning-button 
                    label="Conferma" 
                    variant="brand" 
                    class="slds-m-left_small"
                    onclick={handleConferma}>
                </lightning-button>
            </div>
        </div>
    </div>
</template>