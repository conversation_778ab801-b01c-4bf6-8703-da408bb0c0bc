<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;ContextId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;userId&quot; : null,
  &quot;userName&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userTimeZone&quot; : null,
  &quot;vlcPersistentComponent&quot; : { },
  &quot;vlcTimeTracking&quot; : {
    &quot;GetPolicies&quot; : null,
    &quot;GetUserDetails&quot; : null
  }
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>GetInsurancePoliciesDetailsRow</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:IP:NameInsured.ExternalId__c &quot;_&quot; var:IP:FolderId__c CONCAT</formulaConverted>
        <formulaExpression>CONCAT(IP:NameInsured.ExternalId__c,&quot;_&quot;,IP:FolderId__c)</formulaExpression>
        <formulaResultPath>folderUnicaFinal</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>GetInsurancePoliciesDetailsRowCustom1817</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetailsRow</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsRowCustom0jI9O000000szg1UAAItem11</globalKey>
        <inputFieldName>IP:RecordType.DeveloperName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetailsRow</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recordType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>f6909bba-b955-436d-ae2e-d4b3a8fa8f6c</globalKey>
        <inputFieldName>IP:SourceSystemIdentifier</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetailsRow</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>SourceSystemIdentifier</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>policyId</filterValue>
        <globalKey>GetInsurancePoliciesDetailsRowCustom0jI9O000000szg1UAAItem0</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetailsRow</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>IP</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsRowCustom0jI9O000000szg1UAAItem3</globalKey>
        <inputFieldName>IP:AgencyCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetailsRow</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>agencyCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsRowCustom0jI9O000000szg1UAAItem5</globalKey>
        <inputFieldName>IP:ReferencePolicyNumber</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetailsRow</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policyNumber</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsRowCustom0jI9O000000szg1UAAItem4</globalKey>
        <inputFieldName>IP:NameInsured.ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetailsRow</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>cf</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsRowCustom0jI9O000000szg1UAAItem7</globalKey>
        <inputFieldName>IP:Product__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetailsRow</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>product</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsRowCustom0jI9O000000szg1UAAItem6</globalKey>
        <inputFieldName>IP:PolicyType</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetailsRow</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policyType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsRowCustom0jI9O000000szg1UAAItem8</globalKey>
        <inputFieldName>IP:FolderId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetailsRow</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>folderUnica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsRowCustom2982</globalKey>
        <inputFieldName>IP:ServiceProviderIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetailsRow</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>serviceProviderIdentifier</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;policyId&quot; : &quot;0YT9X000000XxcPWAS&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>GetInsurancePoliciesDetailsRow_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
