import { LightningElement, api, track} from 'lwc';
import { getFocusedTabInfo, openSubtab } from "lightning/platformWorkspaceApi";
export default class cC_quoteListDetail extends LightningElement {
    @track data = [];
    @track error;
    @track expandedDetailData = [];
    @track documentUrl;
    @track showPreview = false;
    @track quoteIdSeleceted;
    @api caseId;

    @api
    set customerData(value) {
      let obj = {};
      try {
        if (typeof value === 'string') {
          // handle url-encoded JSON strings
          obj = JSON.parse(decodeURIComponent(value));
        } else if (value && typeof value === 'object') {
          obj = value;
        }
      } catch (e) {
        console.warn('Failed to parse customerData:', e);
      }
      this._customerData = obj || {};
      console.log('customerData set:', JSON.stringify(this._customerData));
      this.prepareData();
    }
    get customerData() {
      return this._customerData;
    }

    columns = [
        {
            type: 'button-icon',
            initialWidth: 50,
            typeAttributes: {
                iconName: { fieldName: 'expandoIcon' },
                variant: 'bare',
                alternativeText: 'Espandi dettagli',
                name: 'toggle_details',
            },
        },
        { label: 'Id Preventivo', fieldName: 'Id', type: 'text' },
        { label: 'Prodotto', fieldName: 'ProductOfInterest__c', type: 'text' },          
        { label: 'Ambito', fieldName: 'AreaOfNeed__c', type: 'text' },
        { label: 'Stato', fieldName: 'QuoteStatus', type: 'text' },        
        { label: 'Premio', fieldName: 'Amount__c', type: 'currency' },
        { label: 'Step Digitale', fieldName: 'StageName__c', type: 'text' },
        { label: 'Data Creazione', fieldName: 'CreatedDate', type: 'date' },
        { label: 'Data Scadenza', fieldName: 'QuoteExpirationDate', type: 'date' }, 
        {
            type: 'action',
            typeAttributes: {
                rowActions: [
                    { label: 'Visualizza PDF', name: 'pdf' },
                    { label: 'Aggiungi al carrello', name: 'cart' }
                ]
            }
        }
    ];


    dettaglioColumns = [
        { label: 'Prodotto', fieldName: 'ProductOfInterest__c', type: 'text' },
        { label: 'Descrizione', fieldName: 'Description__c', type: 'text' },
        { label: 'Frazionamento', fieldName: 'Fractionation__c', type: 'text' },
        { label: 'Convenzioni', fieldName: 'Conventions__c', type: 'text' },
        { label: 'Assicurato', fieldName: 'Assicurato__c', type: 'text' }
    ];


    prepareData() {
        try {
            const coverages = this.customerData?.opportunityCoverages ?? [];
            const quotes = [
                ...(this.customerData?.quote ?? []),
                ...((this.customerData?.opportunitiesData ?? []).flatMap(od => od.quotes ?? []))
            ];
            const quoteById = new Map(quotes.map(q => [q.Id, q]));

            this.data = coverages.map(cov => {
            const q = cov?.Quote__c ? quoteById.get(cov.Quote__c) : null;
            if (!q) return null;

            return {
                Id: cov.Id,
                // QuoteId: cov.ProductOfInterest__c,
                QuoteStatus: q.Status,
                ProductOfInterest__c : cov.ProductOfInterest__c,
                QuoteExpirationDate: q.ExpirationDate,
                Amount__c: cov.Amount__c,
                AreaOfNeed__c: q.AreasOfNeed__c,
                StageName__c: q.StageName__c,
                CreatedDate: cov.CreatedDate,
                DocumentURL__c: q.DocumentURL__c,
                isExpanded: false,
                expandoIcon: 'utility:chevronright',
                dettaglioRecord: [{
                    ProductOfInterest__c: cov.ProductOfInterest__c,
                    Description__c: cov.Description__c,
                    Fractionation__c: cov.Fractionation__c,
                    Conventions__c: cov.Conventions__c,
                    Assicurato__c: cov.Assicurato__c
                }]
            };
        }).filter(Boolean);

            this.error = undefined;
        } catch (err) {
            console.error('Errore prepareData: ', err);
            this.error = err.message;
            this.data = [];
        }
    }

    handleRowAction(event) {
        const actionName = event.detail.action.name;
        const row = event.detail.row;

        if (actionName === 'toggle_details') {
            const rowIndex = this.data.findIndex(item => item.Id === row.Id);
            
            let updatedData = JSON.parse(JSON.stringify(this.data)); 
            updatedData[rowIndex].isExpanded = !updatedData[rowIndex].isExpanded;
            updatedData[rowIndex].expandoIcon = updatedData[rowIndex].isExpanded ? 'utility:chevrondown' : 'utility:chevronright';

            this.data = updatedData;

            if (this.data[rowIndex].isExpanded) {
                this.expandedDetailData = this.data[rowIndex].dettaglioRecord;
            } else {
                this.expandedDetailData = [];
            }
        } else if (actionName === 'pdf') {
            console.log('Visualizza PDF per ' + row.Id);
            console.log('Visualizza PDF per ' + JSON.stringify(row));
            console.log('url riga : ', row.DocumentURL__c);
            this.documentUrl = row.DocumentURL__c;            
            console.log('url : ', this.documentUrl);

            if (this.documentUrl) {
                this.showPreview = true;
            } else {
                console.error('URL del documento non trovato per il preventivo ' + row.Id);
            }
        } else if (actionName === 'cart') {
            console.log('Aggiungi al carrello ' + row.Id);
            console.log('Aggiungi al carrello ' + JSON.stringify(row));
            console.log('recordId: ', this.caseId);
            this.quoteIdSeleceted = row.Id;
            console.log('quoteIdSeleceted: ', this.quoteIdSeleceted);
            this.openDisambiguation();

        }
    }

    closePDFPreview() {
        this.showPreview = false;
        this.documentUrl = null;
    }

    connectedCallback() {
        console.log('customerData on init:', JSON.stringify(this.customerData));
        
    }

      async openDisambiguation() {
        try {
          this.loading = true;
          const focused = await getFocusedTabInfo();
          const parentId = focused.isSubtab ? focused.parentTabId : focused.tabId;
    
          const pageRef = {
            type: 'standard__component',
            attributes: { componentName: 'c__ccDisambiguationRootWrapper' },
            state: { c__recordId: this.caseId, c__quoteId: this.quoteIdSeleceted ? this.quoteIdSeleceted : '' }
          };
    
          await openSubtab(parentId, {
            recordId: this.caseId,
            pageReference: pageRef,
            focus: true
          });
        } catch (e) {
          // eslint-disable-next-line no-console
          console.error('Errore apertura subtab:', e);
        } finally {
          this.loading = false;
        }
      }

}