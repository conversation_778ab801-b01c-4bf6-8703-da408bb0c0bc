<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;LeadManagement_RetrieveProductConfiguration&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;data.cfProductId\&quot;:\&quot;{data.cfProductId}\&quot;,\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;data.cfProductId&quot;,&quot;val&quot;:&quot;0069X00000NCNZwQAP&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0069X00000NCNZwQAP&quot;,&quot;id&quot;:3}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>ST_ProductContainer</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;ST_ProductHeader&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;{record}&quot;,&quot;selectedState&quot;:&quot;Active&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;FlexCard-0&quot;},{&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;targetId&quot;:&quot;{recordId}&quot;,&quot;productChannel&quot;:&quot;{channel}&quot;,&quot;quoteStored&quot;:&quot;false&quot;,&quot;customlwcname&quot;:&quot;quoteAccordionDisplay&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Custom LWC-1&quot;}]}},&quot;childCards&quot;:[&quot;ST_ProductHeader&quot;],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;LeadManagement_RetrieveProductConfiguration&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;data.cfProductId\&quot;:\&quot;{data.cfProductId}\&quot;,\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;data.cfProductId&quot;,&quot;val&quot;:&quot;0069X00000NCNZwQAP&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0069X00000NCNZwQAP&quot;,&quot;id&quot;:3}]},&quot;title&quot;:&quot;ST_ProductContainer&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfST_ProductContainer_11_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003HEBJSA4&quot;,&quot;MasterLabel&quot;:&quot;cfST_ProductContainer_11_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlObject&quot;:{&quot;isExplicitImport&quot;:false,&quot;targetConfigs&quot;:&quot;CiAgICAgICAgPHRhcmdldENvbmZpZyB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX1JlY29yZFBhZ2UiPjxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPjxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIi8+PHByb3BlcnR5IG5hbWU9ImNmUHJvZHVjdElkIiB0eXBlPSJTdHJpbmciIGxhYmVsPSJQcm9kdWN0SWQiIHJlcXVpcmVkPSJ0cnVlIi8+PC90YXJnZXRDb25maWc+PHRhcmdldENvbmZpZyB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX0FwcFBhZ2UiPjxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPjxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIi8+PHByb3BlcnR5IG5hbWU9ImNmUHJvZHVjdElkIiB0eXBlPSJTdHJpbmciIGxhYmVsPSJQcm9kdWN0SWQiIHJlcXVpcmVkPSJ0cnVlIi8+PC90YXJnZXRDb25maWc+PHRhcmdldENvbmZpZyB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX0hvbWVQYWdlIj48cHJvcGVydHkgbmFtZT0iY2ZQcm9kdWN0SWQiIHR5cGU9IlN0cmluZyIgbGFiZWw9IlByb2R1Y3RJZCIgcmVxdWlyZWQ9InRydWUiLz48L3RhcmdldENvbmZpZz4KICAgICAgICA=&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;apiVersion&quot;:56,&quot;masterLabel&quot;:&quot;ST_ProductContainer&quot;,&quot;description&quot;:&quot;&quot;,&quot;runtimeNamespace&quot;:&quot;&quot;,&quot;isExposed&quot;:true},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfProductId&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;ProductId&quot;,&quot;required&quot;:&quot;true&quot;}}]},{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfProductId&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;ProductId&quot;,&quot;required&quot;:&quot;true&quot;}}]},{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__HomePage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfProductId&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;ProductId&quot;,&quot;required&quot;:&quot;true&quot;}}]}],&quot;isRepeatable&quot;:true,&quot;osSupport&quot;:true,&quot;sessionVars&quot;:[{&quot;name&quot;:&quot;ProductId&quot;,&quot;isApi&quot;:true,&quot;val&quot;:&quot;&quot;}],&quot;events&quot;:[{&quot;eventname&quot;:&quot;refreshView&quot;,&quot;channelname&quot;:&quot;ST_ProductContainer&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;event&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1749020150539-ry9eud0bd&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749020150625&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;refreshView&quot;,&quot;eventLabel&quot;:&quot;custom event&quot;}]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;inactiveQuoteCount&quot;:0,&quot;noteCount&quot;:8,&quot;notes&quot;:[{&quot;noteId&quot;:&quot;0029O00000IThWxQAL&quot;,&quot;ownerName&quot;:&quot;Maria Calvino&quot;,&quot;title&quot;:&quot;Riassegnazione in Agenzia&quot;,&quot;ownerId&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;body&quot;:&quot;Test riassegnazione gruppo&quot;,&quot;id&quot;:&quot;0029O00000IThWxQAL&quot;,&quot;createdDate&quot;:&quot;2025-06-13T09:28:03.000Z&quot;},{&quot;noteId&quot;:&quot;0029O00000I45V9QAJ&quot;,&quot;ownerName&quot;:&quot;Domenico Cambareri&quot;,&quot;title&quot;:&quot;Chiusura linea di prodotto&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;body&quot;:&quot;test closeproduct&quot;,&quot;id&quot;:&quot;0029O00000I45V9QAJ&quot;,&quot;createdDate&quot;:&quot;2025-06-03T12:55:54.000Z&quot;},{&quot;noteId&quot;:&quot;0029O00000I41vvQAB&quot;,&quot;ownerName&quot;:&quot;Domenico Cambareri&quot;,&quot;title&quot;:&quot;Chiusura linea di prodotto&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;body&quot;:&quot;crossfingersssssssssssssssss&quot;,&quot;id&quot;:&quot;0029O00000I41vvQAB&quot;,&quot;createdDate&quot;:&quot;2025-06-03T12:49:20.000Z&quot;},{&quot;noteId&quot;:&quot;0029O00000I44MYQAZ&quot;,&quot;ownerName&quot;:&quot;Domenico Cambareri&quot;,&quot;title&quot;:&quot;Chiusura trattativa&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;body&quot;:&quot;2° TEST CHIUSURA TRATTATIVA&quot;,&quot;id&quot;:&quot;0029O00000I44MYQAZ&quot;,&quot;createdDate&quot;:&quot;2025-06-03T12:21:21.000Z&quot;},{&quot;noteId&quot;:&quot;0029O00000I3xwXQAR&quot;,&quot;ownerName&quot;:&quot;Domenico Cambareri&quot;,&quot;title&quot;:&quot;Chiusura linea di prodotto&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;body&quot;:&quot;Motivo: Acquisto da altra compagnia&quot;,&quot;id&quot;:&quot;0029O00000I3xwXQAR&quot;,&quot;createdDate&quot;:&quot;2025-06-03T10:57:17.000Z&quot;},{&quot;noteId&quot;:&quot;0029O00000I3rS5QAJ&quot;,&quot;ownerName&quot;:&quot;Domenico Cambareri&quot;,&quot;title&quot;:&quot;Chiusura trattativa&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;body&quot;:&quot;eravamo 4 amici a chiudere le note&quot;,&quot;id&quot;:&quot;0029O00000I3rS5QAJ&quot;,&quot;createdDate&quot;:&quot;2025-06-03T10:40:08.000Z&quot;},{&quot;noteId&quot;:&quot;0029O00000I3a4jQAB&quot;,&quot;ownerName&quot;:&quot;Domenico Cambareri&quot;,&quot;title&quot;:&quot;Chiusura linea di prodotto&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;body&quot;:&quot;Motivo: Prezzo troppo alto&quot;,&quot;id&quot;:&quot;0029O00000I3a4jQAB&quot;,&quot;createdDate&quot;:&quot;2025-06-03T10:37:47.000Z&quot;},{&quot;noteId&quot;:&quot;0029O00000I3nUvQAJ&quot;,&quot;ownerName&quot;:&quot;Domenico Cambareri&quot;,&quot;title&quot;:&quot;Chiusura linea di prodotto&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;body&quot;:&quot;Motivo: Non adatto alle esigenze&quot;,&quot;id&quot;:&quot;0029O00000I3nUvQAJ&quot;,&quot;createdDate&quot;:&quot;2025-06-03T10:04:25.000Z&quot;}],&quot;wasReassigned&quot;:&quot;No&quot;,&quot;engagementPoint&quot;:&quot;Digitale Unica&quot;,&quot;channel&quot;:&quot;Preventivatore digitale Unica&quot;,&quot;rawNotes&quot;:[{&quot;createdDate&quot;:&quot;2025-06-13T09:28:03.000Z&quot;,&quot;id&quot;:&quot;0029O00000IThWxQAL&quot;,&quot;body&quot;:&quot;Test riassegnazione gruppo&quot;,&quot;ownerId&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;title&quot;:&quot;Riassegnazione in Agenzia&quot;},{&quot;createdDate&quot;:&quot;2025-06-03T12:55:54.000Z&quot;,&quot;id&quot;:&quot;0029O00000I45V9QAJ&quot;,&quot;body&quot;:&quot;test closeproduct&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;title&quot;:&quot;Chiusura linea di prodotto&quot;},{&quot;createdDate&quot;:&quot;2025-06-03T12:49:20.000Z&quot;,&quot;id&quot;:&quot;0029O00000I41vvQAB&quot;,&quot;body&quot;:&quot;crossfingersssssssssssssssss&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;title&quot;:&quot;Chiusura linea di prodotto&quot;},{&quot;createdDate&quot;:&quot;2025-06-03T12:21:21.000Z&quot;,&quot;id&quot;:&quot;0029O00000I44MYQAZ&quot;,&quot;body&quot;:&quot;2° TEST CHIUSURA TRATTATIVA&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;title&quot;:&quot;Chiusura trattativa&quot;},{&quot;createdDate&quot;:&quot;2025-06-03T10:57:17.000Z&quot;,&quot;id&quot;:&quot;0029O00000I3xwXQAR&quot;,&quot;body&quot;:&quot;Motivo: Acquisto da altra compagnia&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;title&quot;:&quot;Chiusura linea di prodotto&quot;},{&quot;createdDate&quot;:&quot;2025-06-03T10:40:08.000Z&quot;,&quot;id&quot;:&quot;0029O00000I3rS5QAJ&quot;,&quot;body&quot;:&quot;eravamo 4 amici a chiudere le note&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;title&quot;:&quot;Chiusura trattativa&quot;},{&quot;createdDate&quot;:&quot;2025-06-03T10:37:47.000Z&quot;,&quot;id&quot;:&quot;0029O00000I3a4jQAB&quot;,&quot;body&quot;:&quot;Motivo: Prezzo troppo alto&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;title&quot;:&quot;Chiusura linea di prodotto&quot;},{&quot;createdDate&quot;:&quot;2025-06-03T10:04:25.000Z&quot;,&quot;id&quot;:&quot;0029O00000I3nUvQAJ&quot;,&quot;body&quot;:&quot;Motivo: Non adatto alle esigenze&quot;,&quot;ownerId&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;title&quot;:&quot;Chiusura linea di prodotto&quot;}],&quot;noteUsers&quot;:[{&quot;id&quot;:&quot;0059X00000J1Nm2QAF&quot;,&quot;userName&quot;:&quot;Domenico Cambareri&quot;},{&quot;id&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;userName&quot;:&quot;Maria Calvino&quot;}],&quot;closureType&quot;:&quot;Scadenza&quot;,&quot;canaleDiVendita&quot;:&quot;Agenzia&quot;,&quot;quotesAllowed&quot;:&quot;false&quot;,&quot;saleChannel&quot;:&quot;-&quot;,&quot;closureSubType&quot;:&quot;Non adatto alle esigenze&quot;,&quot;callMeBackAllowed&quot;:&quot;false&quot;,&quot;id&quot;:&quot;0069X00000NCNZwQAP&quot;,&quot;status&quot;:&quot;Chiuso&quot;}</sampleDataSourceResponse>
    <versionNumber>11</versionNumber>
</OmniUiCard>
