<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;ApexRemote&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;remoteClass&quot;:&quot;uniUtils&quot;,&quot;remoteMethod&quot;:&quot;populatePicklistCompany2&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;accountId&quot;:&quot;{Parent.recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;Parent.recordId\&quot;:\&quot;{Parent.recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;Parent.recordId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:1}]},&quot;state0element0block_element0_1&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDoc_OmniscriptHelper&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;actionType&quot;:&quot;setDocType&quot;,&quot;codCompagnia&quot;:&quot;{society}&quot;,&quot;docTypes&quot;:&quot;{Parent.docTypesSOC1}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;codCompagnia\&quot;:\&quot;{codCompagnia}\&quot;,\&quot;Parent.docTypesSOC1\&quot;:\&quot;{Parent.docTypesSOC1}\&quot;,\&quot;society\&quot;:\&quot;{society}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;codCompagnia&quot;,&quot;val&quot;:&quot;SOC_1&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;Parent.docTypesSOC1&quot;,&quot;val&quot;:&quot;PAS&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;society&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3}]},&quot;state0element0block_element0_2&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDoc_OmniscriptHelper&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;accountId&quot;:&quot;{Parent.recordId}&quot;,&quot;codCompagnia&quot;:&quot;{society}&quot;,&quot;docTypes&quot;:&quot;{Parent.docTypesSOC4}&quot;,&quot;actionType&quot;:&quot;setDocType&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;Parent.recordId\&quot;:\&quot;{Parent.recordId}\&quot;,\&quot;society\&quot;:\&quot;{society}\&quot;,\&quot;Parent.docTypesSOC4\&quot;:\&quot;{Parent.docTypesSOC4}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;Parent.recordId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;society&quot;,&quot;val&quot;:&quot;SOC_4&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;Parent.docTypesSOC4&quot;,&quot;val&quot;:&quot;PAS&quot;,&quot;id&quot;:5}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniDoc_SocietyPicklistND</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Società&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{society}&quot;,&quot;required&quot;:true,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{options}&quot;,&quot;id&quot;:0}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1755704556821-exenpe0l2&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1755705203767&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;elementId&quot;:&quot;data&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;soc&quot;:&quot;{society}&quot;}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1758352396272-d7ta0tx8u&quot;,&quot;label&quot;:&quot;SetTypePLSOC1&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758359828041&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;UniDoc_OmniscriptHelper\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;actionType\&quot;:\&quot;setDocType\&quot;,\&quot;codCompagnia\&quot;:\&quot;{society}\&quot;,\&quot;docTypes\&quot;:\&quot;{Parent.docTypesSOC1}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;codCompagnia\\\&quot;:\\\&quot;{codCompagnia}\\\&quot;,\\\&quot;Parent.docTypesSOC1\\\&quot;:\\\&quot;{Parent.docTypesSOC1}\\\&quot;,\\\&quot;society\\\&quot;:\\\&quot;{society}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:2},{\&quot;name\&quot;:\&quot;codCompagnia\&quot;,\&quot;val\&quot;:\&quot;SOC_1\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;Parent.docTypesSOC1\&quot;,\&quot;val\&quot;:\&quot;PAS\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;society\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:3}]}&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-5&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;1758357602469-vwwuo11hw&quot;,&quot;label&quot;:&quot;SetTypePLSOC2&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758358103766&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;UniDoc_OmniscriptHelper\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;accountId\&quot;:\&quot;{Parent.recordId}\&quot;,\&quot;codCompagnia\&quot;:\&quot;{society}\&quot;,\&quot;docTypes\&quot;:\&quot;{Parent.docTypesSOC4}\&quot;,\&quot;actionType\&quot;:\&quot;setDocType\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;Parent.recordId\\\&quot;:\\\&quot;{Parent.recordId}\\\&quot;,\\\&quot;society\\\&quot;:\\\&quot;{society}\\\&quot;,\\\&quot;Parent.docTypesSOC4\\\&quot;:\\\&quot;{Parent.docTypesSOC4}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:2},{\&quot;name\&quot;:\&quot;Parent.recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;society\&quot;,\&quot;val\&quot;:\&quot;SOC_4\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;Parent.docTypesSOC4\&quot;,\&quot;val\&quot;:\&quot;PAS\&quot;,\&quot;id\&quot;:5}]}&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-12&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;value&quot;:&quot;&quot;},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1755704556821-exenpe0l2&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1755705203767&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;elementId&quot;:&quot;data&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;soc&quot;:&quot;{society}&quot;}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1758352396272-d7ta0tx8u&quot;,&quot;label&quot;:&quot;SetTypePLSOC1&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758359828041&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;UniDoc_OmniscriptHelper\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;actionType\&quot;:\&quot;setDocType\&quot;,\&quot;codCompagnia\&quot;:\&quot;{society}\&quot;,\&quot;docTypes\&quot;:\&quot;{Parent.docTypesSOC1}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;codCompagnia\\\&quot;:\\\&quot;{codCompagnia}\\\&quot;,\\\&quot;Parent.docTypesSOC1\\\&quot;:\\\&quot;{Parent.docTypesSOC1}\\\&quot;,\\\&quot;society\\\&quot;:\\\&quot;{society}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:2},{\&quot;name\&quot;:\&quot;codCompagnia\&quot;,\&quot;val\&quot;:\&quot;SOC_1\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;Parent.docTypesSOC1\&quot;,\&quot;val\&quot;:\&quot;PAS\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;society\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:3}]}&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-5&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1,&quot;datasourceKey&quot;:&quot;state0element0block_element0_1&quot;},{&quot;key&quot;:&quot;1758357602469-vwwuo11hw&quot;,&quot;label&quot;:&quot;SetTypePLSOC2&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758358103766&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;UniDoc_OmniscriptHelper\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;accountId\&quot;:\&quot;{Parent.recordId}\&quot;,\&quot;codCompagnia\&quot;:\&quot;{society}\&quot;,\&quot;docTypes\&quot;:\&quot;{Parent.docTypesSOC4}\&quot;,\&quot;actionType\&quot;:\&quot;setDocType\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;Parent.recordId\\\&quot;:\\\&quot;{Parent.recordId}\\\&quot;,\\\&quot;society\\\&quot;:\\\&quot;{society}\\\&quot;,\\\&quot;Parent.docTypesSOC4\\\&quot;:\\\&quot;{Parent.docTypesSOC4}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:2},{\&quot;name\&quot;:\&quot;Parent.recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;society\&quot;,\&quot;val\&quot;:\&quot;SOC_4\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;Parent.docTypesSOC4\&quot;,\&quot;val\&quot;:\&quot;PAS\&quot;,\&quot;id\&quot;:5}]}&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-12&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:2,&quot;datasourceKey&quot;:&quot;state0element0block_element0_2&quot;}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;right:xx-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_xx-small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Select-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;right:xx-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_xx-small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element0&quot;,&quot;uKey&quot;:&quot;1758269285918-716&quot;,&quot;parsedProperty&quot;:{&quot;record&quot;:{&quot;options&quot;:[{&quot;label&quot;:&quot;UniSalute&quot;,&quot;value&quot;:&quot;SOC_4&quot;},{&quot;label&quot;:&quot;Unipol&quot;,&quot;value&quot;:&quot;SOC_1&quot;}],&quot;error&quot;:&quot;OK&quot;},&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Società&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{society}&quot;,&quot;required&quot;:true,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{options}&quot;,&quot;id&quot;:0}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1755704556821-exenpe0l2&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1755705203767&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;elementId&quot;:&quot;data&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;soc&quot;:&quot;{society}&quot;}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1758352396272-d7ta0tx8u&quot;,&quot;label&quot;:&quot;SetTypePLSOC1&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758359828041&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;UniDoc_OmniscriptHelper\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;actionType\&quot;:\&quot;setDocType\&quot;,\&quot;codCompagnia\&quot;:\&quot;{society}\&quot;,\&quot;docTypes\&quot;:\&quot;{Parent.docTypesSOC1}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;codCompagnia\\\&quot;:\\\&quot;{codCompagnia}\\\&quot;,\\\&quot;Parent.docTypesSOC1\\\&quot;:\\\&quot;{Parent.docTypesSOC1}\\\&quot;,\\\&quot;society\\\&quot;:\\\&quot;{society}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:2},{\&quot;name\&quot;:\&quot;codCompagnia\&quot;,\&quot;val\&quot;:\&quot;SOC_1\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;Parent.docTypesSOC1\&quot;,\&quot;val\&quot;:\&quot;PAS\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;society\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:3}]}&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-5&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;1758357602469-vwwuo11hw&quot;,&quot;label&quot;:&quot;SetTypePLSOC2&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758358103766&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;UniDoc_OmniscriptHelper\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;accountId\&quot;:\&quot;{Parent.recordId}\&quot;,\&quot;codCompagnia\&quot;:\&quot;{society}\&quot;,\&quot;docTypes\&quot;:\&quot;{Parent.docTypesSOC4}\&quot;,\&quot;actionType\&quot;:\&quot;setDocType\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;Parent.recordId\\\&quot;:\\\&quot;{Parent.recordId}\\\&quot;,\\\&quot;society\\\&quot;:\\\&quot;{society}\\\&quot;,\\\&quot;Parent.docTypesSOC4\\\&quot;:\\\&quot;{Parent.docTypesSOC4}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:2},{\&quot;name\&quot;:\&quot;Parent.recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;society\&quot;,\&quot;val\&quot;:\&quot;SOC_4\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;Parent.docTypesSOC4\&quot;,\&quot;val\&quot;:\&quot;PAS\&quot;,\&quot;id\&quot;:5}]}&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-12&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;value&quot;:&quot;&quot;},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1755704556821-exenpe0l2&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1755705203767&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;elementId&quot;:&quot;data&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;soc&quot;:&quot;{society}&quot;}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1758352396272-d7ta0tx8u&quot;,&quot;label&quot;:&quot;SetTypePLSOC1&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758359828041&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;UniDoc_OmniscriptHelper\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;actionType\&quot;:\&quot;setDocType\&quot;,\&quot;codCompagnia\&quot;:\&quot;{society}\&quot;,\&quot;docTypes\&quot;:\&quot;{Parent.docTypesSOC1}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;codCompagnia\\\&quot;:\\\&quot;{codCompagnia}\\\&quot;,\\\&quot;Parent.docTypesSOC1\\\&quot;:\\\&quot;{Parent.docTypesSOC1}\\\&quot;,\\\&quot;society\\\&quot;:\\\&quot;{society}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:2},{\&quot;name\&quot;:\&quot;codCompagnia\&quot;,\&quot;val\&quot;:\&quot;SOC_1\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;Parent.docTypesSOC1\&quot;,\&quot;val\&quot;:\&quot;PAS\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;society\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:3}]}&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-5&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;1758357602469-vwwuo11hw&quot;,&quot;label&quot;:&quot;SetTypePLSOC2&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758358103766&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;UniDoc_OmniscriptHelper\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;accountId\&quot;:\&quot;{Parent.recordId}\&quot;,\&quot;codCompagnia\&quot;:\&quot;{society}\&quot;,\&quot;docTypes\&quot;:\&quot;{Parent.docTypesSOC4}\&quot;,\&quot;actionType\&quot;:\&quot;setDocType\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;Parent.recordId\\\&quot;:\\\&quot;{Parent.recordId}\\\&quot;,\\\&quot;society\\\&quot;:\\\&quot;{society}\\\&quot;,\\\&quot;Parent.docTypesSOC4\\\&quot;:\\\&quot;{Parent.docTypesSOC4}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:2},{\&quot;name\&quot;:\&quot;Parent.recordId\&quot;,\&quot;val\&quot;:\&quot;0019X000015e2ZLQAY\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;society\&quot;,\&quot;val\&quot;:\&quot;SOC_4\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;Parent.docTypesSOC4\&quot;,\&quot;val\&quot;:\&quot;PAS\&quot;,\&quot;id\&quot;:5}]}&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-12&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;}}},{&quot;key&quot;:&quot;element_element_block_0_0_baseInputElement_1_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Tipologia&quot;,&quot;fieldBinding&quot;:&quot;{tipologia}&quot;,&quot;customProperties&quot;:[{&quot;id&quot;:0,&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{list.options}&quot;}],&quot;required&quot;:true,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1758359983476-04pi52fkl&quot;,&quot;label&quot;:&quot;updateOS&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758360001596&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;elementId&quot;:&quot;data&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;tipologia&quot;:&quot;{tipologia}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-6&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1758359983476-04pi52fkl&quot;,&quot;label&quot;:&quot;updateOS&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758360001596&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;elementId&quot;:&quot;data&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;tipologia&quot;:&quot;{tipologia}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-left_x-small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element1&quot;,&quot;uKey&quot;:&quot;1758269335984-895&quot;,&quot;elementLabel&quot;:&quot;Block-0-Select-1&quot;,&quot;parsedProperty&quot;:{&quot;record&quot;:{&quot;options&quot;:[{&quot;label&quot;:&quot;UniSalute&quot;,&quot;value&quot;:&quot;SOC_4&quot;},{&quot;label&quot;:&quot;Unipol&quot;,&quot;value&quot;:&quot;SOC_1&quot;}],&quot;error&quot;:&quot;OK&quot;},&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Tipologia&quot;,&quot;fieldBinding&quot;:&quot;{tipologia}&quot;,&quot;customProperties&quot;:[{&quot;id&quot;:0,&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{list.options}&quot;}],&quot;required&quot;:true,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1758359983476-04pi52fkl&quot;,&quot;label&quot;:&quot;updateOS&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758360001596&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;elementId&quot;:&quot;data&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;tipologia&quot;:&quot;{tipologia}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_1&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-6&quot;,&quot;field&quot;:&quot;society&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;SOC_4&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1758359983476-04pi52fkl&quot;,&quot;label&quot;:&quot;updateOS&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1758360001596&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;elementId&quot;:&quot;data&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;tipologia&quot;:&quot;{tipologia}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-left_x-small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;*************-492&quot;,&quot;datasourceKey&quot;:&quot;state0element0&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;ApexRemote&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;remoteClass&quot;:&quot;uniUtils&quot;,&quot;remoteMethod&quot;:&quot;populatePicklistCompany2&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;accountId&quot;:&quot;{Parent.recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;Parent.recordId\&quot;:\&quot;{Parent.recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;Parent.recordId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:1}]},&quot;title&quot;:&quot;UniDoc_SocietyPicklistND&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;UniDoc_SocietyPicklist&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56}}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;options&quot;:[{&quot;label&quot;:&quot;UniSalute&quot;,&quot;value&quot;:&quot;SOC_4&quot;},{&quot;label&quot;:&quot;Unipol&quot;,&quot;value&quot;:&quot;SOC_1&quot;}],&quot;error&quot;:&quot;OK&quot;}</sampleDataSourceResponse>
    <stylingConfiguration>{}</stylingConfiguration>
    <versionNumber>2</versionNumber>
</OmniUiCard>
