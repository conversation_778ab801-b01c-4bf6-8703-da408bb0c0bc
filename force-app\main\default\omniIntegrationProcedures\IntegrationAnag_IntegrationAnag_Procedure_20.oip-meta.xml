<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{&quot;actionToCall&quot;:&quot;statiCF&quot;,&quot;cf&quot;:&quot;****************&quot;,&quot;statoBelfiore&quot;:&quot;Z404&quot;,&quot;statoDescrizione&quot;:&quot;ITALIA&quot;,&quot;provincia&quot;:&quot;EE&quot;,&quot;provinciaDescrizione&quot;:&quot;null&quot;,&quot;comuneBelfiore&quot;:&quot;null&quot;,&quot;comuneDescrizione&quot;:&quot;null&quot;}</customJavaScript>
    <description>LORENZO SCRUFARI: Piccole Modifiche Dati di Base</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>IntegrationAnag</name>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformAction14</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;respHTTP:contatti&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;respHTTP:contatti&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UtilObjToList&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPCreateAnag</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;POST&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;%HTTPCreateAnagInfo:statusCode% &gt; 300&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;respHTTP&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;bodyReq&quot;,
  &quot;restPath&quot; : &quot;/api/v1/creazione-anagrafica&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : {
      &quot;Content-type&quot; : &quot;application/json&quot;
    },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : { },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>RemoteAction1</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;call&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;HTTPCreateAnagInfo:statusCode = 201&quot;,
  &quot;remoteClass&quot; : &quot;AnagraficaManagement&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;respBody&quot; : &quot;=SERIALIZE(%respHTTP%) &quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>Remote Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetValues1</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;Status&quot; : &quot;%HTTPCreateAnagInfo:status%&quot;,
    &quot;StatusCode&quot; : &quot;%HTTPCreateAnagInfo:statusCode%&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>transformAnagCreateRequest</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : true,
  &quot;responseJSONNode&quot; : &quot;bodyReq&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PF&apos;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;bodyAnagOS&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;anagCreateRequestTranform&quot;,
  &quot;sendJSONNode&quot; : &quot;bodyAnag&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>transformAnagCreateRequestPG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : true,
  &quot;responseJSONNode&quot; : &quot;bodyReq&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PG&apos;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;bodyAnagOS&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;anagCreateRequestTransformPG&quot;,
  &quot;sendJSONNode&quot; : &quot;bodyAnag&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>AnagCreationConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;createAnag&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>13.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPActionCF</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : false,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/reverse&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;fiscalCode&quot; : &quot;%cf%&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPActionItaly</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : false,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction3&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%HTTPAction:datiNascita:provincia% != \&quot;EE\&quot; &amp;&amp; ISNOTBLANK(%HTTPAction:datiNascita:provincia%)&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/comuni&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;descrizione&quot; : &quot;%HTTPAction:datiNascita:comune%&quot;,
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : false,
      &quot;limiteOccorrenze&quot; : 121,
      &quot;siglaProvincia&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPActionNotItaly</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : false,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction2&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%HTTPAction:datiNascita:provincia% == \&quot;EE\&quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/stati&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceABI&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;%HTTPAction:datiNascita:comune%&quot;,
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : true,
      &quot;filtroGeografico&quot; : &quot;DISATTIVO&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetFieldValuesEE</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(HTTPAction:datiNascita) &amp;&amp; HTTPAction:datiNascita:provincia == \&quot;EE\&quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;CFBreakDown:comuneDescrizione&quot; : &quot;=HTTPAction3:descrizione&quot;,
    &quot;CFBreakDown:provincia&quot; : &quot;=HTTPAction:datiNascita:provincia&quot;,
    &quot;FormStep:DatiDiBaseBlock:dataNascitaInput&quot; : &quot;=CONCAT(TOSTRING(%HTTPAction:datiNascita:anno%),\r\n \&quot;-\&quot;,\r\n IF(%HTTPAction:datiNascita:mese% &lt; 10, CONCAT(\&quot;0\&quot;,TOSTRING(%HTTPAction:datiNascita:mese%)), %HTTPAction:datiNascita:mese%),\r\n \&quot;-\&quot;,\r\n IF(%HTTPAction:datiNascita:giorno% &lt; 10, CONCAT(\&quot;0\&quot;,TOSTRING(%HTTPAction:datiNascita:giorno%)), %HTTPAction:datiNascita:giorno%))&quot;,
    &quot;FormStep:DatiDiBaseBlock:cfInputForm&quot; : &quot;=%cf%&quot;,
    &quot;CFBreakDown:cf&quot; : &quot;=%cf%&quot;,
    &quot;FormStep:DatiDiBaseBlock:datiAnagFC:Anag:nazioneNascita&quot; : &quot;=IF(ISBLANK(HTTPAction2), \&quot;Z000\&quot;, HTTPAction2:codiceBelfiore)&quot;,
    &quot;CFBreakDown:comuneBelfiore&quot; : &quot;=HTTPAction3:codiceBelfiore&quot;,
    &quot;FormStep:DatiDiBaseBlock:sessoInput&quot; : &quot;=HTTPAction:sesso&quot;,
    &quot;CFBreakDown:statoDescrizione&quot; : &quot;=IF(ISBLANK(HTTPAction2), \&quot;ITALIA\&quot;, HTTPAction2:descrizione)&quot;,
    &quot;CFBreakDown:statoBelfiore&quot; : &quot;=IF(ISBLANK(HTTPAction2), \&quot;Z000\&quot;, HTTPAction2:codiceBelfiore)&quot;,
    &quot;CFBreakDown:provinciaDescrizione&quot; : &quot;=HTTPAction3:descrizioneProvincia&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>8.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetFieldValuesEEApex</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(HTTPAction:datiNascita) &amp;&amp; HTTPAction:datiNascita:provincia == \&quot;EE\&quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;CFBreakDown:comuneDescrizione&quot; : &quot;&quot;,
    &quot;CFBreakDown:provincia&quot; : &quot;=HTTPAction:datiNascita:provincia&quot;,
    &quot;FormStep:DatiDiBaseBlock:dataNascitaInput&quot; : &quot;=CONCAT(TOSTRING(%HTTPAction:datiNascita:anno%),\r\n \&quot;-\&quot;,\r\n IF(%HTTPAction:datiNascita:mese% &lt; 10, CONCAT(\&quot;0\&quot;,TOSTRING(%HTTPAction:datiNascita:mese%)), %HTTPAction:datiNascita:mese%),\r\n \&quot;-\&quot;,\r\n IF(%HTTPAction:datiNascita:giorno% &lt; 10, CONCAT(\&quot;0\&quot;,TOSTRING(%HTTPAction:datiNascita:giorno%)), %HTTPAction:datiNascita:giorno%))&quot;,
    &quot;FormStep:DatiDiBaseBlock:cfInputForm&quot; : &quot;=%cf%&quot;,
    &quot;CFBreakDown:cf&quot; : &quot;=%cf%&quot;,
    &quot;FormStep:DatiDiBaseBlock:datiAnagFC:Anag:nazioneNascita&quot; : &quot;=SmontaCF:stato&quot;,
    &quot;CFBreakDown:comuneBelfiore&quot; : &quot;&quot;,
    &quot;FormStep:DatiDiBaseBlock:sessoInput&quot; : &quot;=HTTPAction:sesso&quot;,
    &quot;CFBreakDown:statoDescrizione&quot; : &quot;=HTTPAction:datiNascita:comune&quot;,
    &quot;CFBreakDown:statoBelfiore&quot; : &quot;=SmontaCF:stato&quot;,
    &quot;CFBreakDown:provinciaDescrizione&quot; : &quot;&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>9.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetFieldValuesIT</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(HTTPAction:datiNascita) &amp;&amp; HTTPAction:datiNascita:provincia != \&quot;EE\&quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;FormStep:DatiDiBaseBlock:datiAnagFC:Anag:provinciaNascita&quot; : &quot;=HTTPAction:datiNascita:provincia&quot;,
    &quot;CFBreakDown:cf&quot; : &quot;=%cf%&quot;,
    &quot;FormStep:DatiDiBaseBlock:datiAnagFC:Anag:nazioneNascita&quot; : &quot;=IF(ISBLANK(HTTPAction2), \&quot;Z000\&quot;, HTTPAction2:codiceBelfiore)&quot;,
    &quot;CFBreakDown:comuneBelfiore&quot; : &quot;=HTTPAction3:codiceBelfiore&quot;,
    &quot;FormStep:DatiDiBaseBlock:sessoInput&quot; : &quot;=HTTPAction:sesso&quot;,
    &quot;CFBreakDown:statoBelfiore&quot; : &quot;=IF(ISBLANK(HTTPAction2), \&quot;Z000\&quot;, HTTPAction2:codiceBelfiore)&quot;,
    &quot;CFBreakDown:comuneDescrizione&quot; : &quot;=HTTPAction3:descrizione&quot;,
    &quot;CFBreakDown:provincia&quot; : &quot;=HTTPAction:datiNascita:provincia&quot;,
    &quot;FormStep:DatiDiBaseBlock:dataNascitaInput&quot; : &quot;=CONCAT(TOSTRING(%HTTPAction:datiNascita:anno%),\r\n \&quot;-\&quot;,\r\n IF(%HTTPAction:datiNascita:mese% &lt; 10, CONCAT(\&quot;0\&quot;,TOSTRING(%HTTPAction:datiNascita:mese%)), %HTTPAction:datiNascita:mese%),\r\n \&quot;-\&quot;,\r\n IF(%HTTPAction:datiNascita:giorno% &lt; 10, CONCAT(\&quot;0\&quot;,TOSTRING(%HTTPAction:datiNascita:giorno%)), %HTTPAction:datiNascita:giorno%))&quot;,
    &quot;FormStep:DatiDiBaseBlock:cfInputForm&quot; : &quot;=%cf%&quot;,
    &quot;CFBreakDown:statoDescrizione&quot; : &quot;=IF(ISBLANK(HTTPAction2), \&quot;ITALIA\&quot;, HTTPAction2:descrizione)&quot;,
    &quot;FormStep:DatiDiBaseBlock:datiAnagFC:Anag:comuneNascita&quot; : &quot;=HTTPAction3:codiceBelfiore&quot;,
    &quot;CFBreakDown:provinciaDescrizione&quot; : &quot;=HTTPAction3:descrizioneProvincia&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetFieldValuesITApex</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(HTTPAction:datiNascita) &amp;&amp; HTTPAction:datiNascita:provincia != \&quot;EE\&quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;FormStep:DatiDiBaseBlock:datiAnagFC:Anag:provinciaNascita&quot; : &quot;=SmontaCF:provincia&quot;,
    &quot;CFBreakDown:cf&quot; : &quot;=%cf%&quot;,
    &quot;FormStep:DatiDiBaseBlock:datiAnagFC:Anag:nazioneNascita&quot; : &quot;=%SmontaCF:stato%&quot;,
    &quot;CFBreakDown:comuneBelfiore&quot; : &quot;=SmontaCF:comune&quot;,
    &quot;FormStep:DatiDiBaseBlock:sessoInput&quot; : &quot;=HTTPAction:sesso&quot;,
    &quot;CFBreakDown:statoBelfiore&quot; : &quot;=%SmontaCF:stato%&quot;,
    &quot;CFBreakDown:comuneDescrizione&quot; : &quot;=HTTPAction:datiNascita:comune&quot;,
    &quot;CFBreakDown:provincia&quot; : &quot;=SmontaCF:provincia&quot;,
    &quot;FormStep:DatiDiBaseBlock:dataNascitaInput&quot; : &quot;=CONCAT(TOSTRING(%HTTPAction:datiNascita:anno%),\r\n \&quot;-\&quot;,\r\n IF(%HTTPAction:datiNascita:mese% &lt; 10, CONCAT(\&quot;0\&quot;,TOSTRING(%HTTPAction:datiNascita:mese%)), %HTTPAction:datiNascita:mese%),\r\n \&quot;-\&quot;,\r\n IF(%HTTPAction:datiNascita:giorno% &lt; 10, CONCAT(\&quot;0\&quot;,TOSTRING(%HTTPAction:datiNascita:giorno%)), %HTTPAction:datiNascita:giorno%))&quot;,
    &quot;FormStep:DatiDiBaseBlock:cfInputForm&quot; : &quot;=%cf%&quot;,
    &quot;CFBreakDown:statoDescrizione&quot; : &quot;=\&quot;ITALIA\&quot;&quot;,
    &quot;FormStep:DatiDiBaseBlock:datiAnagFC:Anag:comuneNascita&quot; : &quot;=SmontaCF:comune&quot;,
    &quot;CFBreakDown:provinciaDescrizione&quot; : &quot;&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>7.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetFieldValuesKO</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISBLANK(HTTPAction:datiNascita)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;CFBreakDown:comuneDescrizione&quot; : &quot;=HTTPAction3:descrizione&quot;,
    &quot;CFBreakDown:provincia&quot; : &quot;=HTTPAction:datiNascita:provincia&quot;,
    &quot;CFBreakDown:cf&quot; : &quot;=%cf%&quot;,
    &quot;CFBreakDown:comuneBelfiore&quot; : &quot;=HTTPAction3:codiceBelfiore&quot;,
    &quot;CFBreakDown:statoDescrizione&quot; : &quot;=IF(ISBLANK(HTTPAction2), \&quot;ITALIA\&quot;, HTTPAction2:descrizione)&quot;,
    &quot;CFBreakDown:statoBelfiore&quot; : &quot;=IF(ISBLANK(HTTPAction2), \&quot;Z000\&quot;, HTTPAction2:codiceBelfiore)&quot;,
    &quot;CFBreakDown:provinciaDescrizione&quot; : &quot;=HTTPAction3:descrizioneProvincia&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SmontaCF</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;getStatoComuneCF&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;CF&quot; : &quot;=%cf%&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Remote Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>BreakDownCFConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;cfBreakDown&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ComuniDecisionMatrix</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;remoteOptions&quot; : {
    &quot;matrixName&quot; : &quot;Comuni&quot;
  },
  &quot;defaultMatrixResult&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;matrix Input Parameters&quot; : [ {
    &quot;value&quot; : &quot;Provincia&quot;,
    &quot;name&quot; : &quot;siglaProvincia&quot;
  } ],
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;Comuni&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Matrix Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ComuniList</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;comuniList&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;comuniList&quot; : &quot;=DESERIALIZE(ComuniDecisionMatrix)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformAction10</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:optionsComuni&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;comuni&quot;
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformAction25</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:optionsComuni&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;ComuniList&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;comuni&quot;
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetComuni</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;codProv&quot;,
    &quot;element&quot; : &quot;siglaProvincia&quot;
  } ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:optionsComuni&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetComuni&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPActionGetComuni</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/comuni&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : true,
      &quot;limiteOccorrenze&quot; : 121,
      &quot;siglaProvincia&quot; : &quot;%siglaProvincia%&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ComuniConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;comuni&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformActionSingleToList3</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;DataMapperTransformDinLpr&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;TransformSingleToJsonArray&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformDinLpr</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;TransformProfessione&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformMultipleProf2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;DataMapperTransformActionSingleToList3&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;TransformProfessioniPG&quot;,
  &quot;sendJSONNode&quot; : &quot;optionsPG&quot;
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPActionRicercaDinLpr</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/ricercaProfessioni&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;impiego&quot; : &quot;L&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SortMercatiPreferenziali</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;DataMapperTransformMultipleProf2:mercatiPreferenziali&quot; ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ &quot;label&quot; ],
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response:optionsPG:mercatiPreferenziali&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;mergeFields&quot; : [ ]
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>List Merge Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SortProfessioni</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;DataMapperTransformMultipleProf2:professioni&quot; ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ &quot;label&quot; ],
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response:optionsPG:professioni&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;mergeFields&quot; : [ ]
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>List Merge Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>DinLprConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;DinLpr&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformAction12</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:societa&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;societa&quot;
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>FiltraEOrdinaLista</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;ListaFiltrataEOrdinata&quot;,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;ListaFiltrataEOrdinata&quot; : &quot;=SORTBY(LIST(FILTER(LIST(HTTPActionGrezza), &apos;flagAttivo == true&apos;)), &apos;descrizione&apos;)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPAction10</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPActionGrezza&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/tipiFormaSocietaria&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : { },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>FormeSocietarieConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;societari&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>12.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformCodPG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;UnsortedPGList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;TransformJsonToDescCode&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPActionCodPG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/codiciPersonaGiuridica&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : { },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>PGOptionsList</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;tipoPG&quot; : &quot;=FILTER(LIST(%SortedPgList%), &apos;value != \&quot;EPU\&quot;&apos;)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetVariablesForAF_TipologiaPersonaGiuridica</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;mercREQ&quot; : &quot;=false&quot;,
    &quot;mercRO&quot; : &quot;=true&quot;,
    &quot;profREQ&quot; : &quot;=false&quot;,
    &quot;profLabel&quot; : &quot;=\&quot;Professione\&quot;&quot;,
    &quot;profRO&quot; : &quot;=true&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SortList</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;UnsortedPGList&quot; ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ &quot;label&quot; ],
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;SortedPgList&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;mergeFields&quot; : [ ]
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>List Merge Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetPersoneGiuridicheConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;pg&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformCodProf</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;TransformJsonToDescCode&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPActionCodProf</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/codiciProfessione&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : { },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SortProfByLabel</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;DataMapperTransformCodProf&quot; ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ &quot;DataMapperTransformCodProf:label&quot; ],
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;mergeFields&quot; : [ ]
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>List Merge Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetProfessioniConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;prof&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPActionNormIndirizzi</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:indirizzoNormalizzato&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/indirizziNormalizzati&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceBelfioreComune&quot; : &quot;%codBelCom%&quot;,
      &quot;comune&quot; : &quot;&quot;,
      &quot;indirizzo&quot; : &quot;%indirizzo%&quot;,
      &quot;civico&quot; : &quot;%civico%&quot;,
      &quot;siglaProvincia&quot; : &quot;%siglaProvincia%&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>IndirizziNormConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;indirizzi&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseAction1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;response&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>14.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformActionSingleToList2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;DataMapperTransformRicPG&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;TransformSingleToJsonArray&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformMultipleProf</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;DataMapperTransformActionSingleToList2&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;TransformProfessioniPG&quot;,
  &quot;sendJSONNode&quot; : &quot;optionsPG&quot;
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformRicPG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;TransformProfessione&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPActionRicercaPG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/ricercaProfessioni&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;personaGiuridica&quot; : &quot;%codProf%&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SortMercatiPreferenzialiPG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;DataMapperTransformMultipleProf:mercatiPreferenziali&quot; ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ &quot;label&quot; ],
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response:optionsPG:mercatiPreferenziali&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;mergeFields&quot; : [ ]
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>List Merge Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SortProfessioniPG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;DataMapperTransformMultipleProf:professioni&quot; ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ &quot;label&quot; ],
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response:optionsPG:professioni&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;mergeFields&quot; : [ ]
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>List Merge Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RicPGConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;ricPg&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformActionSIngleToList</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:optionsProf&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;DataMapperTransformrRicProf&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;TransformSingleToJsonArray&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformrRicProf</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;TransformProfessione&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPActionRicercaProf</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/ricercaProfessioni&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceProfessione&quot; : &quot;%codProf%&quot;
    },
    &quot;timeout&quot; : 120000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RicProfessioniConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;ricProf&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>DataMapperTransformAction28</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Stati&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;stati&quot;
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>DataMapperTransformAction29</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Prov&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction1&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;prov&quot;
}</propertySetConfig>
                <sequenceNumber>6.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetProvince2</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetProvince&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>5.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetStati2</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetStati&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPAction24</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/stati&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceABI&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : true,
      &quot;filtroGeografico&quot; : &quot;DISATTIVO&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPAction25</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction1&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/province&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceUnipolRegione&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;sigla&quot; : &quot;&quot;,
      &quot;codiceISTATRegione&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
                <sequenceNumber>4.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetVariablesForAFDatiAnagrafici2</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;options:comRO&quot; : &quot;=true&quot;,
    &quot;options:provRO&quot; : &quot;=true&quot;,
    &quot;options:comREQ&quot; : &quot;=false&quot;,
    &quot;options:isNormVisible&quot; : &quot;=true&quot;,
    &quot;codStato&quot; : &quot;=%statoBelfiore%&quot;,
    &quot;options:Prov&quot; : &quot;=GetProvince2:options&quot;,
    &quot;options:Stati&quot; : &quot;=GetStati2:options&quot;,
    &quot;options:provREQ&quot; : &quot;=false&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>7.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Estero</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;provincia == \&quot;EE\&quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>DataMapperTransformAction19</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Prov&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction1&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;prov&quot;
}</propertySetConfig>
                <sequenceNumber>4.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>DataMapperTransformActionStatiCF</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Stati&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;stati&quot;
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPAction16</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction1&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/province&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceUnipolRegione&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;sigla&quot; : &quot;&quot;,
      &quot;codiceISTATRegione&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPActionGetStatiCF</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/stati&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceABI&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : true,
      &quot;filtroGeografico&quot; : &quot;DISATTIVO&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetVariablesForAFDatiAnagrafici</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;options:comRO&quot; : &quot;=true&quot;,
    &quot;options:provRO&quot; : &quot;=true&quot;,
    &quot;options:comREQ&quot; : &quot;=false&quot;,
    &quot;options:isNormVisible&quot; : &quot;=true&quot;,
    &quot;codStato&quot; : &quot;=%statoBelfiore%&quot;,
    &quot;options:provREQ&quot; : &quot;=false&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>5.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>EsteroOLD</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;provincia == \&quot;EE\&quot;&quot;,
  &quot;elementValueMap&quot; : { },
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : false,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>DataMapperTransformAction22</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Stati&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;stati&quot;
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>DataMapperTransformAction23</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Prov&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction1&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;prov&quot;
}</propertySetConfig>
                <sequenceNumber>6.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>DataMapperTransformAction24</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:optionsComuni&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction2&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;comuni&quot;
}</propertySetConfig>
                <sequenceNumber>9.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetComuni3</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;codProv&quot;,
    &quot;element&quot; : &quot;provincia&quot;
  } ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetComuni&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>8.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetProvince3</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetProvince&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>5.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetStati3</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetStati&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPAction19</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/stati&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceABI&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : true,
      &quot;filtroGeografico&quot; : &quot;DISATTIVO&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPAction20</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction1&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/province&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceUnipolRegione&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;sigla&quot; : &quot;&quot;,
      &quot;codiceISTATRegione&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
                <sequenceNumber>4.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPAction21</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction2&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/comuni&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : true,
      &quot;limiteOccorrenze&quot; : 121,
      &quot;siglaProvincia&quot; : &quot;%provincia%&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
                <sequenceNumber>7.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetValues9</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;options:comRO&quot; : &quot;=false&quot;,
    &quot;comNascita&quot; : &quot;=%comuneBelfiore%&quot;,
    &quot;options:provRO&quot; : &quot;=false&quot;,
    &quot;options:comREQ&quot; : &quot;=true&quot;,
    &quot;provNascita&quot; : &quot;=%provincia%&quot;,
    &quot;options:isNormVisible&quot; : &quot;=true&quot;,
    &quot;codStato&quot; : &quot;=%statoBelfiore%&quot;,
    &quot;options:Prov&quot; : &quot;=GetProvince3:options&quot;,
    &quot;options:Stati&quot; : &quot;=GetStati3:options&quot;,
    &quot;optionsComuni&quot; : &quot;=GetComuni3:options&quot;,
    &quot;options:provREQ&quot; : &quot;=true&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>10.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ITALIA</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;provincia != \&quot;EE\&quot; &amp;&amp; provincia != $Vlocity.NULL &amp;&amp; provincia != \&quot;\&quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetProvince4</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetProvince&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetStati4</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetStati&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetValues17</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;provREQ&quot; : &quot;=true&quot;,
    &quot;Stati&quot; : &quot;=GetStati4:options&quot;,
    &quot;comREQ&quot; : &quot;=true&quot;,
    &quot;comRO&quot; : &quot;=true&quot;,
    &quot;isNormVisible&quot; : &quot;=true&quot;,
    &quot;provRO&quot; : &quot;=true&quot;,
    &quot;Prov&quot; : &quot;=GetProvince4:options&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SmontaCFKO</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;provincia != \&quot;EE\&quot; &amp;&amp; (provincia = $Vlocity.NULL || provincia = \&quot;\&quot;)&quot;,
  &quot;elementValueMap&quot; : { },
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>DataMapperTransformAction20</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Stati&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;stati&quot;
}</propertySetConfig>
                <sequenceNumber>4.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>DataMapperTransformAction21</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Prov&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction1&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;prov&quot;
}</propertySetConfig>
                <sequenceNumber>7.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetProvince5</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetProvince&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>6.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetProvinceASDASD</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetProvince&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetStati4ASDASD</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetStati&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPAction17</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/stati&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceABI&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : true,
      &quot;filtroGeografico&quot; : &quot;DISATTIVO&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPAction18</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction1&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/province&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceUnipolRegione&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;sigla&quot; : &quot;&quot;,
      &quot;codiceISTATRegione&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
                <sequenceNumber>5.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetValues8</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;provREQ&quot; : &quot;=true&quot;,
    &quot;Stati&quot; : &quot;=GetStati4:options&quot;,
    &quot;comREQ&quot; : &quot;=true&quot;,
    &quot;comRO&quot; : &quot;=true&quot;,
    &quot;isNormVisible&quot; : &quot;=true&quot;,
    &quot;provRO&quot; : &quot;=true&quot;,
    &quot;Prov&quot; : &quot;=GetProvince4:options&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>8.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SmontaCFKOOOOOLD</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : false,
  &quot;isIfElseBlock&quot; : true
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>StatiCFConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;statiCF&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformAction26</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Stati&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;stati&quot;
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformAction27</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Prov&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction1&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;prov&quot;
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetProvince</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetProvince&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetStati</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UnipolGetStati&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPAction22</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/stati&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceABI&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : true,
      &quot;filtroGeografico&quot; : &quot;DISATTIVO&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPAction23</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction1&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/province&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceUnipolRegione&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;sigla&quot; : &quot;&quot;,
      &quot;codiceISTATRegione&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetVariablesForAFIndirizzi2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;provREQ&quot; : &quot;=true&quot;,
    &quot;Stati&quot; : &quot;=GetStati:options&quot;,
    &quot;comREQ&quot; : &quot;=true&quot;,
    &quot;comRO&quot; : &quot;=true&quot;,
    &quot;isNormVisible&quot; : &quot;=true&quot;,
    &quot;provRO&quot; : &quot;=true&quot;,
    &quot;Prov&quot; : &quot;=GetProvince:options&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>7.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>StatiConditionalBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;stati&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformAction11</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Prov&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction1&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;prov&quot;
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DataMapperTransformActionStati</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;response:options:Stati&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AFTransformStati&quot;,
  &quot;sendJSONNode&quot; : &quot;stati&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPAction8</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction1&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/province&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceUnipolRegione&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;sigla&quot; : &quot;&quot;,
      &quot;codiceISTATRegione&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPActionGetStati</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : true,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/stati&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceABI&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : true,
      &quot;filtroGeografico&quot; : &quot;DISATTIVO&quot;
    },
    &quot;timeout&quot; : 25000,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetVariablesForAFIndirizzi</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response:options&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;provREQ&quot; : &quot;=true&quot;,
    &quot;comREQ&quot; : &quot;=true&quot;,
    &quot;comRO&quot; : &quot;=true&quot;,
    &quot;isNormVisible&quot; : &quot;=true&quot;,
    &quot;provRO&quot; : &quot;=true&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>StatiConditionalBlockOLD</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%actionToCall% = &apos;stati&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : false,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessKey>IntegrationAnag_IntegrationAnag</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : [ ],
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : [ ],
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : [ ],
  &quot;transientValues&quot; : {
    &quot;activateOrDeactivateConsent&quot; : false
  }
}</propertySetConfig>
    <subType>IntegrationAnag</subType>
    <type>IntegrationAnag</type>
    <uniqueName>IntegrationAnag_IntegrationAnag_Procedure_20</uniqueName>
    <versionNumber>20.0</versionNumber>
</OmniIntegrationProcedure>
