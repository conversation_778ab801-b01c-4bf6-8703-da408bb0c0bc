<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CaseNumber</fieldItem>
                <identifier>RecordCaseNumberField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-60ddcaeb-a787-4228-80f8-5f546edb6814</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Priority</fieldItem>
                <identifier>RecordPriorityField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Source__c</fieldItem>
                <identifier>RecordSource_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.StartDate__c</fieldItem>
                <identifier>RecordStartDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DueDate__c</fieldItem>
                <identifier>RecordDueDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Assignee__c</fieldItem>
                <identifier>RecordAssignee_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ClosedDate</fieldItem>
                <identifier>RecordClosedDateField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TECH_getInTouch__c</fieldItem>
                <identifier>RecordTECH_getInTouch_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.TECH_getInTouch__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Status}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Closed</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-a219fc21-9d49-42de-8de8-699af43d140a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Case.Invio_OTP_con_SMS</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Closed</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.TECH_ShowButtonSendSMS__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Case.Certifica_Modifica</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.TECH_ShowButtonSendSMS__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Closed</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Case.Download_Documenti</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.TECH_ShowButtonDownloadDoc__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Closed</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Case.Integra_Documenti</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.TECH_ShowButtonMissingDocument__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Closed</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Case.Presa_appuntamento</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Closed</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Case.Riassegna</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Closed</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Case.Ripianifica</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Closed</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.TECH_IsPlannable__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Case.Chiudi</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Closed</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.TECH_ShowCloseManual__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>5</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>primaryField</name>
                    <value>Facet-60ddcaeb-a787-4228-80f8-5f546edb6814</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>secondaryFields</name>
                    <value>Facet-a219fc21-9d49-42de-8de8-699af43d140a</value>
                </componentInstanceProperties>
                <componentName>record_flexipage:dynamicHighlights</componentName>
                <identifier>record_flexipage_dynamicHighlights</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>hideUpdateButton</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>variant</name>
                    <value>linear</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_pathassistant:pathAssistant</componentName>
                <identifier>runtime_sales_pathassistant_pathAssistant</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Area__c</fieldItem>
                <identifier>RecordArea_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Activity__c</fieldItem>
                <identifier>RecordActivity_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Detail__c</fieldItem>
                <identifier>RecordDetail_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AreaOfNeed__c</fieldItem>
                <identifier>RecordAreaOfNeed_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-eb79ab3e-0f04-4cf4-bde7-167984ee499d</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Requestfromclient__c</fieldItem>
                <identifier>RecordRequestfromclient_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CCEngaged__c</fieldItem>
                <identifier>RecordCCEngaged_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Outcome__c</fieldItem>
                <identifier>RecordOutcome_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SubOutcome__c</fieldItem>
                <identifier>RecordSubOutcome_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-0f7b8581-f715-4ae1-b168-c5797a80e1d0</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-eb79ab3e-0f04-4cf4-bde7-167984ee499d</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-0f7b8581-f715-4ae1-b168-c5797a80e1d0</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-19113829-9604-4335-9023-3be3a5cd3863</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CommercialAreasOfNeed__c</fieldItem>
                <identifier>RecordCommercialAreasOfNeed_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.TECH_Hide_CommercialAreasOfNeed__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrPosizioniPU__c</fieldItem>
                <identifier>RecordDrPosizioniPU_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.DrPosizioniPU__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.DrPosizioniPU__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>0</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrDataRiattAntic__c</fieldItem>
                <identifier>RecordDrDataRiattAntic_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.TECH_Hide_DrDataRiattAntic__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrNrEvento__c</fieldItem>
                <identifier>RecordDrNrEvento_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.DrNrEvento__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrRiattPolizza__c</fieldItem>
                <identifier>RecordDrRiattPolizza_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.TECH_Hide_DrRiattPolizza__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrMomentoMc__c</fieldItem>
                <identifier>RecordDrMomentoMc_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.DrMomentoMc__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrProdotto__c</fieldItem>
                <identifier>RecordDrProdotto_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.DrProdotto__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrIdVoucher__c</fieldItem>
                <identifier>RecordDrIdVoucher_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.DrIdVoucher__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-a81cd8ce-1a8b-4124-a60b-10d324402650</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrStepRichiestaPU__c</fieldItem>
                <identifier>RecordDrStepRichiestaPU_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.DrStepRichiestaPU__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrNumeroRicontatto__c</fieldItem>
                <identifier>RecordDrNumeroRicontatto_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.DrNumeroRicontatto__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrFasciaOrarioRicontatto__c</fieldItem>
                <identifier>RecordDrFasciaOrarioRicontatto_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.DrFasciaOrarioRicontatto__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrDataSospRiattPolizza__c</fieldItem>
                <identifier>RecordDrDataSospRiattPolizza_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.TECH_Hide_DrDataSospRiattPolizza__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrLabelMotivazione__c</fieldItem>
                <identifier>RecordDrLabelMotivazione_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.DrLabelMotivazione__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrNrOfferta__c</fieldItem>
                <identifier>RecordDrNrOfferta_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.DrNrOfferta__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DrDataProrogaSospensione__c</fieldItem>
                <identifier>RecordDrDataProrogaSospensione_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.TECH_Hide_DrDataProrogaSospensione__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-b2908ab8-7729-4bd4-bba0-0d2e4a53553f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a81cd8ce-1a8b-4124-a60b-10d324402650</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b2908ab8-7729-4bd4-bba0-0d2e4a53553f</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-e840e172-1358-4c1a-ac6d-61ced34e39a0</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-19113829-9604-4335-9023-3be3a5cd3863</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Dettagli</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>incidentFieldApiName</name>
                    <value>ClaimNumber__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>incidentLabel</name>
                    <value>Sinistro</value>
                </componentInstanceProperties>
                <componentName>caseCorrelatoALookupWorkaround</componentName>
                <identifier>c_caseCorrelatoALookupWorkaround</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-e840e172-1358-4c1a-ac6d-61ced34e39a0</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Informazioni Aggiuntive</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>dataTableNoteButton</componentName>
                <identifier>c_dataTableNoteButton</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>Dati_Soggetto_Case</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>timelineConfigs</name>
                    <valueList>
                        <valueListItems>
                            <value>Storico_attivita_case</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentName>industries_common:timeline</componentName>
                <identifier>industries_common_timeline</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Case.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>AttachedContentDocuments</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer2</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Case Record Page</masterLabel>
    <parentFlexiPage>sfa__Case_rec_L</parentFlexiPage>
    <sobjectType>Case</sobjectType>
    <template>
        <name>flexipage:recordHomeTemplateDesktop</name>
        <properties>
            <name>saveOptions</name>
            <value>[{&quot;name&quot;:&quot;UseDefaultAssignmentRule&quot;,&quot;value&quot;:&quot;SHOW_CHECKBOX_WITH_DEFAULT_OFF&quot;},{&quot;name&quot;:&quot;triggerOtherEmail&quot;,&quot;value&quot;:&quot;SHOW_CHECKBOX_WITH_DEFAULT_OFF&quot;}]</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
