<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>DA_Dati_Anagrafici/Unipolsai/1.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n    \&quot;formaSoc\&quot;: {\n      \&quot;id\&quot;: 37,\n      \&quot;codice\&quot;: \&quot;1\&quot;,\n      \&quot;descrizione\&quot;: \&quot;S.P.A.\&quot;,\n      \&quot;flagAttivo\&quot;: true,\n      \&quot;FormaSocietaria\&quot;: \&quot;\&quot;\n    },\n    \&quot;TipoPersGiuridica\&quot;: \&quot;Grandi Clienti\&quot;,\n    \&quot;isCorp\&quot;: \&quot;No\&quot;,\n    \&quot;TipoPersonaGiuridica\&quot;: \&quot;-\&quot;,\n    \&quot;showProfPG\&quot;: true,\n    \&quot;modifica\&quot;: {\n      \&quot;indirizzi\&quot;: [\n        {\n          \&quot;flagPreview\&quot;: false,\n          \&quot;longitudine\&quot;: 15.65492,\n          \&quot;presso\&quot;: \&quot;\&quot;,\n          \&quot;abbreviazioneProvincia\&quot;: \&quot;RC\&quot;,\n          \&quot;dug\&quot;: \&quot;VIA\&quot;,\n          \&quot;latitudine\&quot;: 38.12727,\n          \&quot;indirizzoCompleto\&quot;: \&quot;VIA ITALIA 30\&quot;,\n          \&quot;codiceBelfioreStato\&quot;: \&quot;\&quot;,\n          \&quot;codiceIstatAnag1\&quot;: \&quot;018080063\&quot;,\n          \&quot;cap\&quot;: \&quot;89122\&quot;,\n          \&quot;indirizzoBreve\&quot;: \&quot;\&quot;,\n          \&quot;stato\&quot;: \&quot;\&quot;,\n          \&quot;tipoNormalizzato\&quot;: \&quot;S\&quot;,\n          \&quot;numeroCivico\&quot;: \&quot;30/30\&quot;,\n          \&quot;codiceUnsdM49\&quot;: \&quot;\&quot;,\n          \&quot;codiceIstatComune\&quot;: \&quot;080063\&quot;,\n          \&quot;idIndirizzo\&quot;: \&quot;\&quot;,\n          \&quot;cabStato\&quot;: \&quot;\&quot;,\n          \&quot;dus\&quot;: \&quot;ITALIA\&quot;,\n          \&quot;tipoIndirizzo\&quot;: \&quot;SELE\&quot;,\n          \&quot;localita\&quot;: \&quot;REGGIO DI CALABRIA\&quot;,\n          \&quot;codiceBelfioreComune\&quot;: \&quot;H224\&quot;,\n          \&quot;cabComune\&quot;: \&quot;\&quot;\n        },\n        {\n          \&quot;flagPreview\&quot;: false,\n          \&quot;longitudine\&quot;: 0,\n          \&quot;presso\&quot;: \&quot;\&quot;,\n          \&quot;dug\&quot;: \&quot;\&quot;,\n          \&quot;abbreviazioneProvincia\&quot;: \&quot;\&quot;,\n          \&quot;latitudine\&quot;: 0,\n          \&quot;codiceBelfioreStato\&quot;: \&quot;\&quot;,\n          \&quot;indirizzoCompleto\&quot;: \&quot;\&quot;,\n          \&quot;codiceIstatAnag1\&quot;: \&quot;\&quot;,\n          \&quot;cap\&quot;: \&quot;\&quot;,\n          \&quot;indirizzoBreve\&quot;: \&quot;\&quot;,\n          \&quot;stato\&quot;: \&quot;\&quot;,\n          \&quot;tipoNormalizzato\&quot;: \&quot;N\&quot;,\n          \&quot;numeroCivico\&quot;: \&quot;\&quot;,\n          \&quot;codiceUnsdM49\&quot;: \&quot;\&quot;,\n          \&quot;codiceIstatComune\&quot;: \&quot;\&quot;,\n          \&quot;idIndirizzo\&quot;: \&quot;\&quot;,\n          \&quot;cabStato\&quot;: \&quot;\&quot;,\n          \&quot;dus\&quot;: \&quot;\&quot;,\n          \&quot;tipoIndirizzo\&quot;: \&quot;DOMI\&quot;,\n          \&quot;codiceBelfioreComune\&quot;: \&quot;\&quot;,\n          \&quot;localita\&quot;: \&quot;\&quot;,\n          \&quot;cabComune\&quot;: \&quot;\&quot;\n        }\n      ],\n      \&quot;anagrafica\&quot;: {\n        \&quot;comuneNascitaBelfiore\&quot;: \&quot;\&quot;,\n        \&quot;sesso\&quot;: \&quot;-\&quot;,\n        \&quot;comuneNascita\&quot;: \&quot;\&quot;,\n        \&quot;tipoAnagrafica\&quot;: \&quot;D\&quot;,\n        \&quot;tipoSoggetto\&quot;: \&quot;\&quot;,\n        \&quot;compagnia\&quot;: \&quot;UNIPOL\&quot;,\n        \&quot;statoCodiceFiscalePartitaIva\&quot;: \&quot;-\&quot;,\n        \&quot;statoAnagrafica\&quot;: \&quot;A\&quot;,\n        \&quot;ciu\&quot;: \&quot;7177709\&quot;,\n        \&quot;codiceFiscale\&quot;: \&quot;\&quot;,\n        \&quot;statoCodiceFiscale\&quot;: \&quot;-\&quot;,\n        \&quot;nome\&quot;: \&quot;PROVACOMP2\&quot;,\n        \&quot;cognome\&quot;: \&quot;-\&quot;,\n        \&quot;dataNascita\&quot;: null,\n        \&quot;dataDecesso\&quot;: null,\n        \&quot;nazioneNascita\&quot;: \&quot;\&quot;,\n        \&quot;agenzia\&quot;: \&quot;\&quot;,\n        \&quot;tipoAnagraficaRichiesto\&quot;: \&quot;\&quot;\n      },\n      \&quot;DisableCompany\&quot;: true,\n      \&quot;privacy\&quot;: {\n        \&quot;datiAdesione\&quot;: {\n          \&quot;applicazioneFine\&quot;: \&quot;\&quot;,\n          \&quot;applicazioneInizio\&quot;: \&quot;PUPTF\&quot;,\n          \&quot;tipoPrivacy\&quot;: \&quot;\&quot;,\n          \&quot;dataInizioEffetto\&quot;: \&quot;\&quot;\n        }\n      },\n      \&quot;extra\&quot;: {\n        \&quot;id\&quot;: \&quot;a1i9O000005UhB8QAK\&quot;\n      },\n      \&quot;professione\&quot;: {\n        \&quot;specializzazione\&quot;: {\n          \&quot;codice\&quot;: \&quot;\&quot;\n        },\n        \&quot;settore\&quot;: {\n          \&quot;codice\&quot;: \&quot;\&quot;\n        },\n        \&quot;personaGiuridica\&quot;: {\n          \&quot;codice\&quot;: \&quot;GCL\&quot;\n        },\n        \&quot;impiego\&quot;: {\n          \&quot;codice\&quot;: \&quot;\&quot;\n        },\n        \&quot;mercatoPreferenziale\&quot;: {\n          \&quot;codice\&quot;: \&quot;\&quot;\n        },\n        \&quot;professione\&quot;: {\n          \&quot;codice\&quot;: \&quot;882\&quot;\n        }\n      },\n      \&quot;datiAgenzia\&quot;: {\n        \&quot;flagAutorizzazioneCauzione\&quot;: false,\n        \&quot;codiceCanale\&quot;: \&quot;\&quot;,\n        \&quot;dataClienteTop\&quot;: null,\n        \&quot;dataInizioEffetto\&quot;: null,\n        \&quot;compagnia\&quot;: \&quot;\&quot;,\n        \&quot;dataFineEffetto\&quot;: null,\n        \&quot;flagAdesioneFEA\&quot;: false,\n        \&quot;flagProprietaContattiFea\&quot;: false,\n        \&quot;dataCessazioneCliente\&quot;: null,\n        \&quot;statoSoggetto\&quot;: \&quot;\&quot;,\n        \&quot;codiceSubAgenzia\&quot;: \&quot;\&quot;,\n        \&quot;codiceAgenziaPrevalente\&quot;: \&quot;\&quot;,\n        \&quot;flagClienteTop\&quot;: false\n      },\n      \&quot;Propaga\&quot;: false\n    },\n    \&quot;Originator\&quot;: \&quot;-\&quot;,\n    \&quot;SedeLegale\&quot;: \&quot;VIA ITALIA 30, RC, REGGIO DI CALABRIA, 89122\&quot;,\n    \&quot;labelProfPG\&quot;: \&quot;Tipologia Grande Cliente\&quot;,\n    \&quot;ReferenteAziendale\&quot;: \&quot;-\&quot;,\n    \&quot;ClienteInPerimetro\&quot;: \&quot;No\&quot;,\n    \&quot;IsCorporate\&quot;: \&quot;--\&quot;,\n    \&quot;FormaSoocietaria\&quot;: \&quot;1\&quot;,\n    \&quot;StatoPartitaIva\&quot;: \&quot;-\&quot;,\n    \&quot;GestoreAnagrafica\&quot;: \&quot;-\&quot;,\n    \&quot;DataCostituzione\&quot;: \&quot;-\&quot;,\n    \&quot;PartitaIva\&quot;: \&quot;72782820053\&quot;,\n    \&quot;StatoSoggettoPerAzienda\&quot;: \&quot;-\&quot;,\n    \&quot;RagioneSociale\&quot;: \&quot;PROVACOMP2\&quot;,\n    \&quot;STatoPartitaIVA\&quot;: null,\n    \&quot;professioneDescrizione\&quot;: \&quot;Ente Pubb. - Ist.Credito E Altre Istituz.Cr\&quot;,\n    \&quot;Professione\&quot;: \&quot;Ente Pubb. - Ist.Credito E Altre Istituz.Cr\&quot;\n  }&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]}}</dataSourceConfig>
    <description>Lorenzo Scrufari: Professioni PG</description>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>BSN_DA_Dati_Anagrafici</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;bottom:x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;customClass&quot;:&quot;&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_block_0_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;11&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3EDati%20Anagrafici%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_11-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;11&quot;}},&quot;elementLabel&quot;:&quot;Block-0-Block-0-Text-1&quot;,&quot;key&quot;:&quot;element_element_element_block_0_0_block_0_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_0_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Block-0&quot;},{&quot;key&quot;:&quot;element_element_block_0_0_block_1_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Modifica&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1737392422016&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;close_modal&quot;,&quot;cardName&quot;:&quot;DA_Dati_Anagrafici_Modifica&quot;,&quot;flyoutLwc&quot;:&quot;DA_Dati_Anagrafici_Modifica&quot;,&quot;cardNode&quot;:&quot;{record.modifica}&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;setPropagation&quot;:&quot;true&quot;}},&quot;key&quot;:&quot;1737128172910-nnovmazcj&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;hideActionIcon&quot;:true,&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}},&quot;flyoutChannel&quot;:&quot;close_modal&quot;,&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Action-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_1_0_action_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_1_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Block-0-clone-0&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Ragione Sociale&quot;,&quot;fieldName&quot;:&quot;RagioneSociale&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;,&quot;customClass&quot;:&quot;&quot;,&quot;maxHeight&quot;:&quot;&quot;},&quot;elementLabel&quot;:&quot;Block-6-Field-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;,&quot;customClass&quot;:&quot;&quot;,&quot;maxHeight&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Sede Legale&quot;,&quot;fieldName&quot;:&quot;SedeLegale&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-6-Field-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Forma Societaria&quot;,&quot;fieldName&quot;:&quot;formaSoc.descrizione&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-6-Field-6&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Stato Soggetto per Agenzia&quot;,&quot;fieldName&quot;:&quot;StatoSoggettoPerAzienda&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Field-7-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Cliente di Perimetro Patto 3.0&quot;,&quot;fieldName&quot;:&quot;ClienteInPerimetro&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Field-8-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Gestore Anagrafica&quot;,&quot;fieldName&quot;:&quot;GestoreAnagrafica&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;1&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;2&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-2-Field-12-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_5_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Corporate&quot;,&quot;fieldName&quot;:&quot;isCorp&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-2-Field-6&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_6_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;P. IVA&quot;,&quot;fieldName&quot;:&quot;PartitaIva&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-3-Field-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Stato P.IVA&quot;,&quot;fieldName&quot;:&quot;StatoPartitaIva&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-3-Field-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Tipo Persona Giuridica&quot;,&quot;fieldName&quot;:&quot;TipoPersGiuridica&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-3-Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_outputField_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;key&quot;:&quot;element_element_block_2_0_outputField_3_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;{labelProfPG}&quot;,&quot;fieldName&quot;:&quot;professioneDescrizione&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;showProfPG&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-9&quot;,&quot;field&quot;:&quot;nascondiProfessione&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Field-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Origination&quot;,&quot;fieldName&quot;:&quot;Originator&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-17&quot;,&quot;field&quot;:&quot;1&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;2&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-3-Field-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_outputField_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Anzianità di Relazione (anni)&quot;,&quot;fieldName&quot;:&quot;AnzianitaRelazione&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-3-Field-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_outputField_5_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Referente Aziendale&quot;,&quot;fieldName&quot;:&quot;ReferenteAziendale&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;elementLabel&quot;:&quot;Block-3-Field-6&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;5&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;5&quot;,&quot;small&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_5-of-12 slds-small-size_5-of-12 slds-size_5-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_outputField_6_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;}],&quot;elementLabel&quot;:&quot;Block-3&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[],&quot;blankCardState&quot;:false}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n    \&quot;formaSoc\&quot;: {\n      \&quot;id\&quot;: 37,\n      \&quot;codice\&quot;: \&quot;1\&quot;,\n      \&quot;descrizione\&quot;: \&quot;S.P.A.\&quot;,\n      \&quot;flagAttivo\&quot;: true,\n      \&quot;FormaSocietaria\&quot;: \&quot;\&quot;\n    },\n    \&quot;TipoPersGiuridica\&quot;: \&quot;Grandi Clienti\&quot;,\n    \&quot;isCorp\&quot;: \&quot;No\&quot;,\n    \&quot;TipoPersonaGiuridica\&quot;: \&quot;-\&quot;,\n    \&quot;showProfPG\&quot;: true,\n    \&quot;modifica\&quot;: {\n      \&quot;indirizzi\&quot;: [\n        {\n          \&quot;flagPreview\&quot;: false,\n          \&quot;longitudine\&quot;: 15.65492,\n          \&quot;presso\&quot;: \&quot;\&quot;,\n          \&quot;abbreviazioneProvincia\&quot;: \&quot;RC\&quot;,\n          \&quot;dug\&quot;: \&quot;VIA\&quot;,\n          \&quot;latitudine\&quot;: 38.12727,\n          \&quot;indirizzoCompleto\&quot;: \&quot;VIA ITALIA 30\&quot;,\n          \&quot;codiceBelfioreStato\&quot;: \&quot;\&quot;,\n          \&quot;codiceIstatAnag1\&quot;: \&quot;018080063\&quot;,\n          \&quot;cap\&quot;: \&quot;89122\&quot;,\n          \&quot;indirizzoBreve\&quot;: \&quot;\&quot;,\n          \&quot;stato\&quot;: \&quot;\&quot;,\n          \&quot;tipoNormalizzato\&quot;: \&quot;S\&quot;,\n          \&quot;numeroCivico\&quot;: \&quot;30/30\&quot;,\n          \&quot;codiceUnsdM49\&quot;: \&quot;\&quot;,\n          \&quot;codiceIstatComune\&quot;: \&quot;080063\&quot;,\n          \&quot;idIndirizzo\&quot;: \&quot;\&quot;,\n          \&quot;cabStato\&quot;: \&quot;\&quot;,\n          \&quot;dus\&quot;: \&quot;ITALIA\&quot;,\n          \&quot;tipoIndirizzo\&quot;: \&quot;SELE\&quot;,\n          \&quot;localita\&quot;: \&quot;REGGIO DI CALABRIA\&quot;,\n          \&quot;codiceBelfioreComune\&quot;: \&quot;H224\&quot;,\n          \&quot;cabComune\&quot;: \&quot;\&quot;\n        },\n        {\n          \&quot;flagPreview\&quot;: false,\n          \&quot;longitudine\&quot;: 0,\n          \&quot;presso\&quot;: \&quot;\&quot;,\n          \&quot;dug\&quot;: \&quot;\&quot;,\n          \&quot;abbreviazioneProvincia\&quot;: \&quot;\&quot;,\n          \&quot;latitudine\&quot;: 0,\n          \&quot;codiceBelfioreStato\&quot;: \&quot;\&quot;,\n          \&quot;indirizzoCompleto\&quot;: \&quot;\&quot;,\n          \&quot;codiceIstatAnag1\&quot;: \&quot;\&quot;,\n          \&quot;cap\&quot;: \&quot;\&quot;,\n          \&quot;indirizzoBreve\&quot;: \&quot;\&quot;,\n          \&quot;stato\&quot;: \&quot;\&quot;,\n          \&quot;tipoNormalizzato\&quot;: \&quot;N\&quot;,\n          \&quot;numeroCivico\&quot;: \&quot;\&quot;,\n          \&quot;codiceUnsdM49\&quot;: \&quot;\&quot;,\n          \&quot;codiceIstatComune\&quot;: \&quot;\&quot;,\n          \&quot;idIndirizzo\&quot;: \&quot;\&quot;,\n          \&quot;cabStato\&quot;: \&quot;\&quot;,\n          \&quot;dus\&quot;: \&quot;\&quot;,\n          \&quot;tipoIndirizzo\&quot;: \&quot;DOMI\&quot;,\n          \&quot;codiceBelfioreComune\&quot;: \&quot;\&quot;,\n          \&quot;localita\&quot;: \&quot;\&quot;,\n          \&quot;cabComune\&quot;: \&quot;\&quot;\n        }\n      ],\n      \&quot;anagrafica\&quot;: {\n        \&quot;comuneNascitaBelfiore\&quot;: \&quot;\&quot;,\n        \&quot;sesso\&quot;: \&quot;-\&quot;,\n        \&quot;comuneNascita\&quot;: \&quot;\&quot;,\n        \&quot;tipoAnagrafica\&quot;: \&quot;D\&quot;,\n        \&quot;tipoSoggetto\&quot;: \&quot;\&quot;,\n        \&quot;compagnia\&quot;: \&quot;UNIPOL\&quot;,\n        \&quot;statoCodiceFiscalePartitaIva\&quot;: \&quot;-\&quot;,\n        \&quot;statoAnagrafica\&quot;: \&quot;A\&quot;,\n        \&quot;ciu\&quot;: \&quot;7177709\&quot;,\n        \&quot;codiceFiscale\&quot;: \&quot;\&quot;,\n        \&quot;statoCodiceFiscale\&quot;: \&quot;-\&quot;,\n        \&quot;nome\&quot;: \&quot;PROVACOMP2\&quot;,\n        \&quot;cognome\&quot;: \&quot;-\&quot;,\n        \&quot;dataNascita\&quot;: null,\n        \&quot;dataDecesso\&quot;: null,\n        \&quot;nazioneNascita\&quot;: \&quot;\&quot;,\n        \&quot;agenzia\&quot;: \&quot;\&quot;,\n        \&quot;tipoAnagraficaRichiesto\&quot;: \&quot;\&quot;\n      },\n      \&quot;DisableCompany\&quot;: true,\n      \&quot;privacy\&quot;: {\n        \&quot;datiAdesione\&quot;: {\n          \&quot;applicazioneFine\&quot;: \&quot;\&quot;,\n          \&quot;applicazioneInizio\&quot;: \&quot;PUPTF\&quot;,\n          \&quot;tipoPrivacy\&quot;: \&quot;\&quot;,\n          \&quot;dataInizioEffetto\&quot;: \&quot;\&quot;\n        }\n      },\n      \&quot;extra\&quot;: {\n        \&quot;id\&quot;: \&quot;a1i9O000005UhB8QAK\&quot;\n      },\n      \&quot;professione\&quot;: {\n        \&quot;specializzazione\&quot;: {\n          \&quot;codice\&quot;: \&quot;\&quot;\n        },\n        \&quot;settore\&quot;: {\n          \&quot;codice\&quot;: \&quot;\&quot;\n        },\n        \&quot;personaGiuridica\&quot;: {\n          \&quot;codice\&quot;: \&quot;GCL\&quot;\n        },\n        \&quot;impiego\&quot;: {\n          \&quot;codice\&quot;: \&quot;\&quot;\n        },\n        \&quot;mercatoPreferenziale\&quot;: {\n          \&quot;codice\&quot;: \&quot;\&quot;\n        },\n        \&quot;professione\&quot;: {\n          \&quot;codice\&quot;: \&quot;882\&quot;\n        }\n      },\n      \&quot;datiAgenzia\&quot;: {\n        \&quot;flagAutorizzazioneCauzione\&quot;: false,\n        \&quot;codiceCanale\&quot;: \&quot;\&quot;,\n        \&quot;dataClienteTop\&quot;: null,\n        \&quot;dataInizioEffetto\&quot;: null,\n        \&quot;compagnia\&quot;: \&quot;\&quot;,\n        \&quot;dataFineEffetto\&quot;: null,\n        \&quot;flagAdesioneFEA\&quot;: false,\n        \&quot;flagProprietaContattiFea\&quot;: false,\n        \&quot;dataCessazioneCliente\&quot;: null,\n        \&quot;statoSoggetto\&quot;: \&quot;\&quot;,\n        \&quot;codiceSubAgenzia\&quot;: \&quot;\&quot;,\n        \&quot;codiceAgenziaPrevalente\&quot;: \&quot;\&quot;,\n        \&quot;flagClienteTop\&quot;: false\n      },\n      \&quot;Propaga\&quot;: false\n    },\n    \&quot;Originator\&quot;: \&quot;-\&quot;,\n    \&quot;SedeLegale\&quot;: \&quot;VIA ITALIA 30, RC, REGGIO DI CALABRIA, 89122\&quot;,\n    \&quot;labelProfPG\&quot;: \&quot;Tipologia Grande Cliente\&quot;,\n    \&quot;ReferenteAziendale\&quot;: \&quot;-\&quot;,\n    \&quot;ClienteInPerimetro\&quot;: \&quot;No\&quot;,\n    \&quot;IsCorporate\&quot;: \&quot;--\&quot;,\n    \&quot;FormaSoocietaria\&quot;: \&quot;1\&quot;,\n    \&quot;StatoPartitaIva\&quot;: \&quot;-\&quot;,\n    \&quot;GestoreAnagrafica\&quot;: \&quot;-\&quot;,\n    \&quot;DataCostituzione\&quot;: \&quot;-\&quot;,\n    \&quot;PartitaIva\&quot;: \&quot;72782820053\&quot;,\n    \&quot;StatoSoggettoPerAzienda\&quot;: \&quot;-\&quot;,\n    \&quot;RagioneSociale\&quot;: \&quot;PROVACOMP2\&quot;,\n    \&quot;STatoPartitaIVA\&quot;: null,\n    \&quot;professioneDescrizione\&quot;: \&quot;Ente Pubb. - Ist.Credito E Altre Istituz.Cr\&quot;,\n    \&quot;Professione\&quot;: \&quot;Ente Pubb. - Ist.Credito E Altre Istituz.Cr\&quot;\n  }&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;title&quot;:&quot;BSN_DA_Dati_Anagrafici&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfBSN_DA_Dati_Anagrafici_11_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O0000047asTSAQ&quot;,&quot;MasterLabel&quot;:&quot;cfBSN_DA_Dati_Anagrafici_11_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;globalCSS&quot;:false,&quot;xmlObject&quot;:{&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX1JlY29yZFBhZ2UiPgogICAgICAgICAgICAgICAgICAgICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgICAgICAgICAgICAgICAgICAgPHByb3BlcnR5IG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciLz4KICAgICAgICAgICAgICAgICAgICAgIDwvdGFyZ2V0Q29uZmlnPjx0YXJnZXRDb25maWcgdGFyZ2V0cz0ibGlnaHRuaW5nX19BcHBQYWdlIj4KICAgICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICAgICAgICAgICAgICAgICAgPHByb3BlcnR5IG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciLz4KICAgICAgICAgICAgICAgICAgICA8L3RhcmdldENvbmZpZz4=&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;masterLabel&quot;:&quot;BSN_DA_Dati_Anagrafici&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]}],&quot;events&quot;:[{&quot;eventname&quot;:&quot;closeModalANagMod&quot;,&quot;channelname&quot;:&quot;DA_Dati_Anagrafici&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1737369726490-wn2hbg56f&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1737369954718&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;close_modal&quot;,&quot;message&quot;:&quot;close&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;DA_Dati_Anagrafici:closeModalANagMod&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}],&quot;sessionVars&quot;:[]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;formaSoc&quot;:{&quot;id&quot;:37,&quot;codice&quot;:&quot;1&quot;,&quot;descrizione&quot;:&quot;S.P.A.&quot;,&quot;flagAttivo&quot;:true,&quot;FormaSocietaria&quot;:&quot;&quot;},&quot;TipoPersGiuridica&quot;:&quot;Grandi Clienti&quot;,&quot;isCorp&quot;:&quot;No&quot;,&quot;TipoPersonaGiuridica&quot;:&quot;-&quot;,&quot;showProfPG&quot;:true,&quot;modifica&quot;:{&quot;indirizzi&quot;:[{&quot;flagPreview&quot;:false,&quot;longitudine&quot;:15.65492,&quot;presso&quot;:&quot;&quot;,&quot;abbreviazioneProvincia&quot;:&quot;RC&quot;,&quot;dug&quot;:&quot;VIA&quot;,&quot;latitudine&quot;:38.12727,&quot;indirizzoCompleto&quot;:&quot;VIA ITALIA 30&quot;,&quot;codiceBelfioreStato&quot;:&quot;&quot;,&quot;codiceIstatAnag1&quot;:&quot;018080063&quot;,&quot;cap&quot;:&quot;89122&quot;,&quot;indirizzoBreve&quot;:&quot;&quot;,&quot;stato&quot;:&quot;&quot;,&quot;tipoNormalizzato&quot;:&quot;S&quot;,&quot;numeroCivico&quot;:&quot;30/30&quot;,&quot;codiceUnsdM49&quot;:&quot;&quot;,&quot;codiceIstatComune&quot;:&quot;080063&quot;,&quot;idIndirizzo&quot;:&quot;&quot;,&quot;cabStato&quot;:&quot;&quot;,&quot;dus&quot;:&quot;ITALIA&quot;,&quot;tipoIndirizzo&quot;:&quot;SELE&quot;,&quot;localita&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;cabComune&quot;:&quot;&quot;},{&quot;flagPreview&quot;:false,&quot;longitudine&quot;:0,&quot;presso&quot;:&quot;&quot;,&quot;dug&quot;:&quot;&quot;,&quot;abbreviazioneProvincia&quot;:&quot;&quot;,&quot;latitudine&quot;:0,&quot;codiceBelfioreStato&quot;:&quot;&quot;,&quot;indirizzoCompleto&quot;:&quot;&quot;,&quot;codiceIstatAnag1&quot;:&quot;&quot;,&quot;cap&quot;:&quot;&quot;,&quot;indirizzoBreve&quot;:&quot;&quot;,&quot;stato&quot;:&quot;&quot;,&quot;tipoNormalizzato&quot;:&quot;N&quot;,&quot;numeroCivico&quot;:&quot;&quot;,&quot;codiceUnsdM49&quot;:&quot;&quot;,&quot;codiceIstatComune&quot;:&quot;&quot;,&quot;idIndirizzo&quot;:&quot;&quot;,&quot;cabStato&quot;:&quot;&quot;,&quot;dus&quot;:&quot;&quot;,&quot;tipoIndirizzo&quot;:&quot;DOMI&quot;,&quot;codiceBelfioreComune&quot;:&quot;&quot;,&quot;localita&quot;:&quot;&quot;,&quot;cabComune&quot;:&quot;&quot;}],&quot;anagrafica&quot;:{&quot;comuneNascitaBelfiore&quot;:&quot;&quot;,&quot;sesso&quot;:&quot;-&quot;,&quot;comuneNascita&quot;:&quot;&quot;,&quot;tipoAnagrafica&quot;:&quot;D&quot;,&quot;tipoSoggetto&quot;:&quot;&quot;,&quot;compagnia&quot;:&quot;UNIPOL&quot;,&quot;statoCodiceFiscalePartitaIva&quot;:&quot;-&quot;,&quot;statoAnagrafica&quot;:&quot;A&quot;,&quot;ciu&quot;:&quot;7177709&quot;,&quot;codiceFiscale&quot;:&quot;&quot;,&quot;statoCodiceFiscale&quot;:&quot;-&quot;,&quot;nome&quot;:&quot;PROVACOMP2&quot;,&quot;cognome&quot;:&quot;-&quot;,&quot;dataNascita&quot;:null,&quot;dataDecesso&quot;:null,&quot;nazioneNascita&quot;:&quot;&quot;,&quot;agenzia&quot;:&quot;&quot;,&quot;tipoAnagraficaRichiesto&quot;:&quot;&quot;},&quot;DisableCompany&quot;:true,&quot;privacy&quot;:{&quot;datiAdesione&quot;:{&quot;applicazioneFine&quot;:&quot;&quot;,&quot;applicazioneInizio&quot;:&quot;PUPTF&quot;,&quot;tipoPrivacy&quot;:&quot;&quot;,&quot;dataInizioEffetto&quot;:&quot;&quot;}},&quot;extra&quot;:{&quot;id&quot;:&quot;a1i9O000005UhB8QAK&quot;},&quot;professione&quot;:{&quot;specializzazione&quot;:{&quot;codice&quot;:&quot;&quot;},&quot;settore&quot;:{&quot;codice&quot;:&quot;&quot;},&quot;personaGiuridica&quot;:{&quot;codice&quot;:&quot;GCL&quot;},&quot;impiego&quot;:{&quot;codice&quot;:&quot;&quot;},&quot;mercatoPreferenziale&quot;:{&quot;codice&quot;:&quot;&quot;},&quot;professione&quot;:{&quot;codice&quot;:&quot;882&quot;}},&quot;datiAgenzia&quot;:{&quot;flagAutorizzazioneCauzione&quot;:false,&quot;codiceCanale&quot;:&quot;&quot;,&quot;dataClienteTop&quot;:null,&quot;dataInizioEffetto&quot;:null,&quot;compagnia&quot;:&quot;&quot;,&quot;dataFineEffetto&quot;:null,&quot;flagAdesioneFEA&quot;:false,&quot;flagProprietaContattiFea&quot;:false,&quot;dataCessazioneCliente&quot;:null,&quot;statoSoggetto&quot;:&quot;&quot;,&quot;codiceSubAgenzia&quot;:&quot;&quot;,&quot;codiceAgenziaPrevalente&quot;:&quot;&quot;,&quot;flagClienteTop&quot;:false},&quot;Propaga&quot;:false},&quot;Originator&quot;:&quot;-&quot;,&quot;SedeLegale&quot;:&quot;VIA ITALIA 30, RC, REGGIO DI CALABRIA, 89122&quot;,&quot;labelProfPG&quot;:&quot;Tipologia Grande Cliente&quot;,&quot;ReferenteAziendale&quot;:&quot;-&quot;,&quot;ClienteInPerimetro&quot;:&quot;No&quot;,&quot;IsCorporate&quot;:&quot;--&quot;,&quot;FormaSoocietaria&quot;:&quot;1&quot;,&quot;StatoPartitaIva&quot;:&quot;-&quot;,&quot;GestoreAnagrafica&quot;:&quot;-&quot;,&quot;DataCostituzione&quot;:&quot;-&quot;,&quot;PartitaIva&quot;:&quot;72782820053&quot;,&quot;StatoSoggettoPerAzienda&quot;:&quot;-&quot;,&quot;RagioneSociale&quot;:&quot;PROVACOMP2&quot;,&quot;STatoPartitaIVA&quot;:null,&quot;professioneDescrizione&quot;:&quot;Ente Pubb. - Ist.Credito E Altre Istituz.Cr&quot;,&quot;Professione&quot;:&quot;Ente Pubb. - Ist.Credito E Altre Istituz.Cr&quot;}</sampleDataSourceResponse>
    <stylingConfiguration>{&quot;customStyles&quot;:&quot;&quot;}</stylingConfiguration>
    <versionNumber>11</versionNumber>
</OmniUiCard>
