/**
 * @File Name         : urcs_DC_GetInterventi.cls
 * @Description       : DataCloud class per recuperare gli interventi di un veicolo
 * <AUTHOR> VE
 * @Group             :
 * @Last Modified On  : 25-09-2025
 * @Last Modified By  : VE
 * @cicd_tests urcs_DC_GetInterventi
**/
global with sharing class urcs_DC_GetInterventi implements System.Callable {

    public Object call(String action, Map<String,Object> args){
        Map<String,Object> input = (Map<String,Object>) args.get('input');
        Map<String,Object> output = (Map<String,Object>) args.get('output');
        Map<String,Object> options = (Map<String,Object>) args.get('options');
        invokeMethod(input, output, options);
        return null;
    }
        
    @AuraEnabled
    global static Map<String,Object> invokeMethod(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        
        system.debug('input '+ JSON.serialize(input));
        system.debug('output '+ JSON.serialize(output));
        system.debug('options '+ JSON.serialize(options));
        Map<String,Object> setValueMap = (Map<String,Object>) input.get('Set Values');
        String method = (String) setValueMap.get('methodExecute');
        try{
            if(method.equalsIgnoreCase('init')){
                output = getInterventi(input,output,options);
            }
        }catch(Exception e){
            output.put('error',e.getMessage() + ' ' + e.getStackTraceString());
        }
        return output;
    }
    
    public static Map<String,Object> getInterventi(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        try{
            String idVeicolo =  null != (String)input.get('recordId') ? (String)input.get('recordId') : null;
            system.debug('idVeicolo '+ idVeicolo);
            if(idVeicolo != null){
                
                List<Asset> asset = [SELECT id,ExternalId__c FROM Asset WHERE id=: idVeicolo];
                String externalId = asset.get(0).ExternalId__c.replace('UR_V_','');
                Integer idAuto = Integer.valueOf(externalId);
                system.debug('idAuto '+ idAuto);
                
                String queryString = 'SELECT Id, ' +
                                        'DataSource__c, ' +
                                        'DataSourceObject__c, ' +
                                        'InternalOrganization__c, ' +
                                        'ID_INTERVENTO__c, ' +
                                        'ID_CONTRATTO__c, ' +
                                        'ID_AUTO__c, ' +
                                        'ID_FORNITORE__c, ' +
                                        'DS_FORNITORE_DENOMINAZIONECOMPUTED__c, ' +
                                        'ID_DRIVER__c, ' +
                                        'DS_DRIVER_DENOMINAZIONECOMPUTED__c, ' +
                                        'SINISTRO_DATA_INVIO__c, ' +
                                        'TS_FINE_EFFETTIVA__c, ' +
                                        'DS_OPZIONE_EROGAZIONE__c, ' +
                                        'FL_PROGRAMMATO__c, ' +
                                        'TS_PROGRAMMAZIONE__c, ' +
                                        'TS_INIZIO_PREVISTO__c, ' +
                                        'TS_FINE_PREVISTA__c, ' +
                                        'TS_INIZIO_EFFETTIVO__c, ' +
                                        'TS_USCITA_OFFICINA__c, ' +
                                        'TS_CREAZIONE__c, ' +
                                        'TS_CHIUSURA__c, ' +
                                        'KQ_ID_INTERVENTO__c ' +
                                        'FROM UR_INTERVENTI__dlm ' + 
                                        'WHERE ID_AUTO__c =: idAuto ';
                List<sObject> listInterventi = Database.query(queryString);
    
                output.put('data',listInterventi);

            }
            
        } catch (Exception e){

            output.put('data',null);
            System.debug('error in urcs_DC_GetInterventi method getInterventi: '+ json.serializePretty(e));
        }

        return output;
    }

}
