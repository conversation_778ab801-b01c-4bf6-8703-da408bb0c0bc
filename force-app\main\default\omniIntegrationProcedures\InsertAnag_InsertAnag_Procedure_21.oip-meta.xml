<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{&quot;bodyAnagOS&quot;:{&quot;CFBreakDown&quot;:{&quot;provinciaDescrizione&quot;:&quot;&quot;,&quot;statoDescrizione&quot;:&quot;ITALIA&quot;,&quot;provincia&quot;:&quot;SI&quot;,&quot;comuneBelfiore&quot;:&quot;F605&quot;,&quot;comuneDescrizione&quot;:&quot;MONTERONI D&apos;ARBIA&quot;,&quot;cf&quot;:&quot;****************&quot;,&quot;statoBelfiore&quot;:&quot;Z000&quot;},&quot;target&quot;:&quot;c:anagFormFormItalian&quot;,&quot;tabIcon&quot;:&quot;custom:custom18&quot;,&quot;tabLabel&quot;:&quot;Nuova Anagrafica&quot;,&quot;uid&quot;:&quot;1758119788489&quot;,&quot;userProfile&quot;:&quot;System Administrator&quot;,&quot;timeStamp&quot;:&quot;2025-09-17T14:36:29.309Z&quot;,&quot;userTimeZoneName&quot;:&quot;Europe/Rome&quot;,&quot;userTimeZone&quot;:&quot;120&quot;,&quot;userCurrencyCode&quot;:&quot;EUR&quot;,&quot;userName&quot;:&quot;<EMAIL>.dev6&quot;,&quot;userId&quot;:&quot;0059X00000PrV0GQAV&quot;,&quot;omniProcessId&quot;:&quot;0jN9V000004UxQ9UAK&quot;,&quot;localTimeZoneName&quot;:&quot;Europe/Rome&quot;,&quot;RedirectToExisting&quot;:null,&quot;FormStep&quot;:{&quot;DatiDiBaseBlock&quot;:{&quot;dataNascitaInput&quot;:&quot;1972-04-26&quot;,&quot;datiAnagFC&quot;:{&quot;Anag&quot;:{&quot;comuneNascita&quot;:&quot;F605&quot;,&quot;provinciaNascita&quot;:&quot;SI&quot;,&quot;nazioneNascita&quot;:&quot;Z000&quot;}},&quot;sessoInput&quot;:&quot;M&quot;,&quot;cfInputForm&quot;:&quot;****************&quot;,&quot;nomeInput&quot;:&quot;VLN&quot;,&quot;cognomeInput&quot;:&quot;ZCC&quot;},&quot;Block1&quot;:null,&quot;IndirizziBlock&quot;:{&quot;addressFCRes&quot;:{&quot;AnagNoNorm&quot;:{&quot;indirizzo&quot;:&quot;VIA ITALIA&quot;,&quot;civico&quot;:&quot;30&quot;,&quot;stato&quot;:&quot;Z000&quot;},&quot;Anag&quot;:{&quot;indirizzo&quot;:{&quot;cellaCensuaria&quot;:&quot;S0800630001494&quot;,&quot;accuratezza&quot;:80,&quot;longitudine&quot;:15.65492,&quot;latitudine&quot;:38.12727,&quot;codiceTerritoriale&quot;:&quot;89122&quot;,&quot;indirizzoBreveLegacy&quot;:&quot;VIA ITALIA 30&quot;,&quot;civico&quot;:&quot;30 &quot;,&quot;nomeOdonimoAbbreviato&quot;:&quot;ITALIA&quot;,&quot;nomeOdonimo&quot;:&quot;ITALIA&quot;,&quot;tipoOdonimoAbbreviato&quot;:&quot;V.&quot;,&quot;tipoOdonimo&quot;:&quot;VIA&quot;,&quot;descrizioneLocalitaAbbreviata&quot;:&quot;REGGIO CALABRIA&quot;,&quot;descrizioneLocalita&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;descrizioneComune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;codiceCABComune&quot;:&quot;16300-6&quot;,&quot;codiceISTATComune&quot;:&quot;018080063&quot;,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;descrizioneProvincia&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;codiceISTATProvincia&quot;:&quot;018080&quot;,&quot;siglaProvincia&quot;:&quot;RC&quot;,&quot;descrizioneRegione&quot;:&quot;CALABRIA&quot;,&quot;codiceUnipolRegione&quot;:&quot;18&quot;,&quot;codiceISTATRegione&quot;:&quot;018&quot;}}}},&quot;seleBlock&quot;:null,&quot;domBlock&quot;:null,&quot;TipoPGBlock&quot;:null,&quot;ProfessioniBlock&quot;:{},&quot;recapitiBlock&quot;:{&quot;CellPERInput&quot;:&quot;+39333444455&quot;},&quot;RecapitiPGBlock&quot;:null,&quot;privacyBlock&quot;:{&quot;consPrivacyCheck&quot;:false,&quot;optOutCheck&quot;:false},&quot;DomicilioEqualResSelect&quot;:&quot;Si&quot;,&quot;GestoreAnagraficaBlock&quot;:{&quot;GestAnagFC&quot;:{&quot;Anag&quot;:{&quot;SubagenziaInput&quot;:&quot;00001&quot;}}}},&quot;OmniScriptFmtData&quot;:{&quot;PreFormStep:CompanySelect&quot;:&quot;Unipol&quot;,&quot;PreFormStep:TipologiaSoggSelect&quot;:&quot;Persona fisica&quot;,&quot;FormStep:DatiDiBaseBlock|1:dataNascitaInput&quot;:&quot;01-04-1972&quot;,&quot;FormStep:DatiDiBaseBlock|1:sessoInput&quot;:&quot;Maschio&quot;,&quot;FormStep:DomicilioEqualResSelect&quot;:&quot;SI&quot;},&quot;PreFormStep&quot;:{&quot;cfInput&quot;:&quot;****************&quot;,&quot;pIvaInput&quot;:null,&quot;CompanySelect&quot;:&quot;unipolsai&quot;,&quot;TipologiaSoggSelect&quot;:&quot;PF&quot;},&quot;AccId&quot;:&quot;&quot;,&quot;Account&quot;:{},&quot;compagnia&quot;:&quot;SOC_1&quot;,&quot;AgencyCode&quot;:&quot;01853&quot;,&quot;UserId&quot;:&quot;101853AQ&quot;,&quot;UserName&quot;:&quot;Lorenzo Scrufari&quot;},&quot;actionToCall&quot;:&quot;createAnag&quot;,&quot;omniScriptId&quot;:&quot;0jN9V000004UxQ9UAK&quot;,&quot;elementName&quot;:&quot;IntegrationProcedureAction1&quot;}</customJavaScript>
    <description>LORENZO SCRUFARI: FIX INDIRIZZO BREVE</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>IntegrationInsertAnag</name>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ApexCreateAccStructure</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;call&quot;,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;respBody&quot; : &quot;=SERIALIZE(%SetApexInput:respBody%)&quot;,
    &quot;tipoSoggetto&quot; : &quot;=bodyReq:anagrafica:tipoSoggetto&quot;,
    &quot;isProfessione&quot; : &quot;=(((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PF&apos;) &amp;&amp; (ISNOTBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:professione%))) || ((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PG&apos;) &amp;&amp; (ISNOTBLANK(%bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:tipoPGCode%))))&quot;
  },
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;remoteResp&quot;,
  &quot;executionConditionalFormula&quot; : &quot;respHTTP:statusCode = 201&quot;,
  &quot;remoteClass&quot; : &quot;AnagraficaManagement&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;restOptions&quot; : { }
}</propertySetConfig>
        <sequenceNumber>15.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckDatiAnagraficiResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;elementValueMap&quot; : { },
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PF&apos;) &amp;&amp; (ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:nomeInput%) || ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:cfInputForm%) || ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:cognomeInput%) || ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:dataNascitaInput%) || ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:sessoInput%) || ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:datiAnagFC:Anag:nazioneNascita%))) || \r\n((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PG&apos;) &amp;&amp; (ISBLANK(%bodyAnagOS:FormStep:Block1:pivaInput%) || ISBLANK(%bodyAnagOS:FormStep:Block1:ragSocInput%) || ISBLANK(%bodyAnagOS:FormStep:Block1:formaSocFC:Anag:formaSoc%)))&quot;,
  &quot;additionalOutput&quot; : {
    &quot;debug&quot; : &quot;=%DebugValori%&quot;,
    &quot;errore&quot; : &quot;=\&quot;Verificare i dati anagrafici\&quot;&quot;,
    &quot;errorLabel&quot; : &quot;=\&quot;Errore di Validazione\&quot;&quot;,
    &quot;isSuccess&quot; : &quot;=false&quot;
  },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckProfessioneTPGResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;show&quot; : null,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;isActive&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PF&apos;) &amp;&amp; (ISBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:professione%))) || \r\n((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PG&apos;) &amp;&amp; (ISBLANK(%bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:tipoPGCode%)))&quot;,
  &quot;additionalOutput&quot; : {
    &quot;debug&quot; : &quot;=%DebugValori%&quot;,
    &quot;error&quot; : &quot;=IF((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PF&apos;),\&quot;Inserire Professione\&quot;,\&quot;Inserire Tipologia Persona Giuridica\&quot;)&quot;
  },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckRecapitiResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;elementValueMap&quot; : { },
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;((ISBLANK(%bodyAnagOS:FormStep:recapitiBlock:CellPERInput%) || (%bodyAnagOS:FormStep:recapitiBlock:CellPERInput% == \&quot;+39\&quot;))  &amp;&amp; ISBLANK(%bodyAnagOS:FormStep:recapitiBlock:EmailPERInput%) &amp;&amp; (%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PF&apos;)) ||\r\n((ISBLANK(%bodyAnagOS:FormStep:RecapitiPGBlock:cellPGInput%) || (%bodyAnagOS:FormStep:RecapitiPGBlock:cellPGInput% == \&quot;+39\&quot;)) &amp;&amp; ISBLANK(%bodyAnagOS:FormStep:RecapitiPGBlock:emailPGInput%) &amp;&amp; ISBLANK(%bodyAnagOS:FormStep:RecapitiPGBlock:PECPGInput%) &amp;&amp;(%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PG&apos;))&quot;,
  &quot;additionalOutput&quot; : {
    &quot;debug&quot; : &quot;=%DebugValori%&quot;,
    &quot;errore&quot; : &quot;=\&quot;Valorizzare almeno un recapito\&quot;&quot;,
    &quot;errorLabel&quot; : &quot;=\&quot;Errore di Validazione\&quot;&quot;,
    &quot;isSuccess&quot; : &quot;=false&quot;
  },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CellPF</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:recapitiBlock:CellPERInput%) &amp;&amp; (%bodyAnagOS:FormStep:recapitiBlock:CellPERInput% != \&quot;+39\&quot;)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;referente&quot; : &quot;=&quot;,
    &quot;flagPreferito&quot; : &quot;=$Vlocity.TRUE&quot;,
    &quot;tipologiaContatto&quot; : &quot;=\&quot;PER\&quot;&quot;,
    &quot;tipoContatto&quot; : &quot;=\&quot;CELL\&quot;&quot;,
    &quot;dataFineEffetto&quot; : &quot;=&quot;,
    &quot;dataInizioEffetto&quot; : &quot;=&quot;,
    &quot;mostraDatiTracciatura&quot; : &quot;=$Vlocity.TRUE&quot;,
    &quot;contatto&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:recapitiBlock:CellPERInput%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CreateContactNodePF</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;CellPF&quot;, &quot;MailPF&quot; ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ ],
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ContactNode&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;mergeFields&quot; : [ ]
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>List Merge Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>MailPF</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:recapitiBlock:EmailPERInput%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;referente&quot; : &quot;=&quot;,
    &quot;flagPreferito&quot; : &quot;=$Vlocity.TRUE&quot;,
    &quot;tipologiaContatto&quot; : &quot;=\&quot;PER\&quot;&quot;,
    &quot;tipoContatto&quot; : &quot;=\&quot;MAIL\&quot;&quot;,
    &quot;dataFineEffetto&quot; : &quot;=&quot;,
    &quot;dataInizioEffetto&quot; : &quot;=&quot;,
    &quot;mostraDatiTracciatura&quot; : &quot;=$Vlocity.TRUE&quot;,
    &quot;contatto&quot; : &quot;=%bodyAnagOS:FormStep:recapitiBlock:EmailPERInput%&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ContattiPF</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PF&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CellPG</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:RecapitiPGBlock:cellPGInput%) &amp;&amp; (%bodyAnagOS:FormStep:RecapitiPGBlock:cellPGInput% != \&quot;+39\&quot;)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;referente&quot; : &quot;=&quot;,
    &quot;flagPreferito&quot; : &quot;=$Vlocity.TRUE&quot;,
    &quot;tipologiaContatto&quot; : &quot;=\&quot;PER\&quot;&quot;,
    &quot;tipoContatto&quot; : &quot;=\&quot;CELL\&quot;&quot;,
    &quot;dataFineEffetto&quot; : &quot;=&quot;,
    &quot;dataInizioEffetto&quot; : &quot;=&quot;,
    &quot;mostraDatiTracciatura&quot; : &quot;=$Vlocity.TRUE&quot;,
    &quot;contatto&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:RecapitiPGBlock:cellPGInput%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CreateContactNodePG</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;CellPG&quot;, &quot;MailPG&quot;, &quot;PecPG&quot; ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ ],
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ContactNode&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;mergeFields&quot; : [ ]
}</propertySetConfig>
                <sequenceNumber>4.0</sequenceNumber>
                <type>List Merge Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>MailPG</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:RecapitiPGBlock:emailPGInput%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;referente&quot; : &quot;=&quot;,
    &quot;flagPreferito&quot; : &quot;=$Vlocity.TRUE&quot;,
    &quot;tipologiaContatto&quot; : &quot;=\&quot;PER\&quot;&quot;,
    &quot;tipoContatto&quot; : &quot;=\&quot;MAIL\&quot;&quot;,
    &quot;dataFineEffetto&quot; : &quot;=&quot;,
    &quot;dataInizioEffetto&quot; : &quot;=&quot;,
    &quot;mostraDatiTracciatura&quot; : &quot;=$Vlocity.TRUE&quot;,
    &quot;contatto&quot; : &quot;=%bodyAnagOS:FormStep:RecapitiPGBlock:emailPGInput%&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>PecPG</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:RecapitiPGBlock:PECPGInput%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;referente&quot; : &quot;=&quot;,
    &quot;flagPreferito&quot; : &quot;=$Vlocity.TRUE&quot;,
    &quot;tipologiaContatto&quot; : &quot;=\&quot;PER\&quot;&quot;,
    &quot;tipoContatto&quot; : &quot;=\&quot;PEC\&quot;&quot;,
    &quot;dataFineEffetto&quot; : &quot;=&quot;,
    &quot;dataInizioEffetto&quot; : &quot;=&quot;,
    &quot;mostraDatiTracciatura&quot; : &quot;=$Vlocity.TRUE&quot;,
    &quot;contatto&quot; : &quot;=%bodyAnagOS:FormStep:RecapitiPGBlock:PECPGInput%&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ContattiPG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PG&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Contatti</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>DebugValori</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;Nazione&quot; : &quot;=ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:datiAnagFC:Anag:nazioneNascita%)&quot;,
    &quot;Piva&quot; : &quot;=ISBLANK(%bodyAnagOS:FormStep:Block1:pivaInput%)&quot;,
    &quot;TipoSoggetto&quot; : &quot;=%bodyAnagOS:PreFormStep:TipologiaSoggSelect%&quot;,
    &quot;FormaSocietaria&quot; : &quot;=ISBLANK(%bodyAnagOS:FormStep:Block1:formaSocFC:Anag:formaSoc%)&quot;,
    &quot;CF&quot; : &quot;=ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:cfInputForm%&quot;,
    &quot;RagioneSociale&quot; : &quot;=ISBLANK(%bodyAnagOS:FormStep:Block1:ragSocInput%)&quot;,
    &quot;Nome&quot; : &quot;=ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:nomeInput%&quot;,
    &quot;Sesso&quot; : &quot;=ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:sessoInput%)&quot;,
    &quot;TipoPersonaGiuridicaa&quot; : &quot;=ISBLANK(%bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:tipoPGCode%)&quot;,
    &quot;Cognome&quot; : &quot;=ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:cognomeInput%&quot;,
    &quot;DataNascita&quot; : &quot;=ISBLANK(%bodyAnagOS:FormStep:DatiDiBaseBlock:dataNascitaInput%&quot;,
    &quot;Professionee&quot; : &quot;=ISBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:professione%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>DeserializedApexResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;DeserializedResult&quot;,
  &quot;responseJSONNode&quot; : &quot;DR&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;DeserializedResult&quot; : &quot;= DESERIALIZE(%remoteResp:result%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>16.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>TransformPGBodyOptOut2</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyReq&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PG&apos;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;anagrafica:indirizzo&quot; : &quot;=AddressNode&quot;,
    &quot;contatti&quot; : &quot;=LIST(ContactNode)&quot;,
    &quot;compagnia&quot; : &quot;=%bodyAnagOS:PreFormStep:CompanySelect%&quot;,
    &quot;userId&quot; : &quot;=%bodyAnagOS:UserId%&quot;,
    &quot;username&quot; : &quot;=%bodyAnagOS:UserName%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;bodyAnagOS&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;anagCreateRequestTranPGOptOut&quot;,
  &quot;sendJSONNode&quot; : &quot;bodyAnag&quot;
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>TransfromPFBodyOptOut2</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyReq&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PF&apos;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;anagrafica:indirizzo&quot; : &quot;=AddressNode&quot;,
    &quot;contatti&quot; : &quot;=LIST(ContactNode)&quot;,
    &quot;altriIndirizzi&quot; : &quot;=IF(ISNOTBLANK(LIST(OtherAddressNode)),LIST(OtherAddressNode),$Vlocity.NULL)&quot;,
    &quot;compagnia&quot; : &quot;=%bodyAnagOS:PreFormStep:CompanySelect%&quot;,
    &quot;userId&quot; : &quot;=%bodyAnagOS:UserId%&quot;,
    &quot;username&quot; : &quot;=%bodyAnagOS:UserName%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;bodyAnagOS&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;anagCreateRequestTranOptOut&quot;,
  &quot;sendJSONNode&quot; : &quot;bodyAnag&quot;
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>CheckOptOut2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>TransformPGBody2</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyReq&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PG&apos;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;anagrafica:indirizzo&quot; : &quot;=AddressNode&quot;,
    &quot;contatti&quot; : &quot;=LIST(ContactNode)&quot;,
    &quot;compagnia&quot; : &quot;=%bodyAnagOS:PreFormStep:CompanySelect%&quot;,
    &quot;userId&quot; : &quot;=%bodyAnagOS:UserId%&quot;,
    &quot;username&quot; : &quot;=%bodyAnagOS:UserName%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;bodyAnagOS&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;anagCreateRequestTransformPG&quot;,
  &quot;sendJSONNode&quot; : &quot;bodyAnag&quot;
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>TransfromPFBody2</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyReq&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PF&apos;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;anagrafica:indirizzo&quot; : &quot;=AddressNode&quot;,
    &quot;contatti&quot; : &quot;=LIST(ContactNode)&quot;,
    &quot;altriIndirizzi&quot; : &quot;=IF(ISNOTBLANK(OtherAddressNode),LIST(OtherAddressNode),\&quot;\&quot;)&quot;,
    &quot;compagnia&quot; : &quot;=%bodyAnagOS:PreFormStep:CompanySelect%&quot;,
    &quot;userId&quot; : &quot;=%bodyAnagOS:UserId%&quot;,
    &quot;username&quot; : &quot;=%bodyAnagOS:UserName%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;bodyAnagOS&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;anagCreateRequestTranform&quot;,
  &quot;sendJSONNode&quot; : &quot;bodyAnag&quot;
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ElseBlock2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:FormStep:privacyBlock:optOutCheck% != $Vlocity.TRUE&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : false,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>dmProfNo</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PF&apos;) &amp;&amp; (ISBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:professione%))) || \r\n((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PG&apos;) &amp;&amp; (ISBLANK(%bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:tipoPGCode%)))&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>TransformPGBodyOptOut</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyReq&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PG&apos;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;professione&quot; : &quot;=professione&quot;,
    &quot;anagrafica:indirizzo&quot; : &quot;=AddressNode&quot;,
    &quot;contatti&quot; : &quot;=LIST(ContactNode)&quot;,
    &quot;compagnia&quot; : &quot;=%bodyAnagOS:PreFormStep:CompanySelect%&quot;,
    &quot;userId&quot; : &quot;=%bodyAnagOS:UserId%&quot;,
    &quot;username&quot; : &quot;=%bodyAnagOS:UserName%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;bodyAnagOS&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;anagCreateRequestTranPGOptOut&quot;,
  &quot;sendJSONNode&quot; : &quot;bodyAnag&quot;
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>TransfromPFBodyOptOut</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyReq&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PF&apos;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;professione&quot; : &quot;=professione&quot;,
    &quot;anagrafica:indirizzo&quot; : &quot;=AddressNode&quot;,
    &quot;contatti&quot; : &quot;=LIST(ContactNode)&quot;,
    &quot;altriIndirizzi&quot; : &quot;=IF(ISNOTBLANK(LIST(OtherAddressNode)),LIST(OtherAddressNode),$Vlocity.NULL)&quot;,
    &quot;compagnia&quot; : &quot;=%bodyAnagOS:PreFormStep:CompanySelect%&quot;,
    &quot;userId&quot; : &quot;=%bodyAnagOS:UserId%&quot;,
    &quot;username&quot; : &quot;=%bodyAnagOS:UserName%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;bodyAnagOS&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;anagCreateRequestTranOptOut&quot;,
  &quot;sendJSONNode&quot; : &quot;bodyAnag&quot;
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>CheckOptOut</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>TransformPGBody</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyReq&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PG&apos;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;professione&quot; : &quot;=professione&quot;,
    &quot;anagrafica:indirizzo&quot; : &quot;=AddressNode&quot;,
    &quot;contatti&quot; : &quot;=LIST(ContactNode)&quot;,
    &quot;compagnia&quot; : &quot;=%bodyAnagOS:PreFormStep:CompanySelect%&quot;,
    &quot;userId&quot; : &quot;=%bodyAnagOS:UserId%&quot;,
    &quot;username&quot; : &quot;=%bodyAnagOS:UserName%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;bodyAnagOS&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;anagCreateRequestTransformPG&quot;,
  &quot;sendJSONNode&quot; : &quot;bodyAnag&quot;
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>TransfromPFBody</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyReq&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% = &apos;PF&apos;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;professione&quot; : &quot;=professione&quot;,
    &quot;anagrafica:indirizzo&quot; : &quot;=AddressNode&quot;,
    &quot;contatti&quot; : &quot;=LIST(ContactNode)&quot;,
    &quot;altriIndirizzi&quot; : &quot;=IF(ISNOTBLANK(OtherAddressNode),LIST(OtherAddressNode),\&quot;\&quot;)&quot;,
    &quot;compagnia&quot; : &quot;=%bodyAnagOS:PreFormStep:CompanySelect%&quot;,
    &quot;userId&quot; : &quot;=%bodyAnagOS:UserId%&quot;,
    &quot;username&quot; : &quot;=%bodyAnagOS:UserName%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;bodyAnagOS&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;anagCreateRequestTranform&quot;,
  &quot;sendJSONNode&quot; : &quot;bodyAnag&quot;
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>DataRaptor Transform Action</type>
            </childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ElseBlock</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:FormStep:privacyBlock:optOutCheck% != $Vlocity.TRUE&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : false,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>dmProfSi</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PF&apos;) &amp;&amp; (ISNOTBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:professione%))) || \r\n((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PG&apos;) &amp;&amp; (ISNOTBLANK(%bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:tipoPGCode%)))&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CreateIndirizziNodePF</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;ResidenzaNormalizzata&quot;, &quot;ResidenzaNonNormalizzata&quot;, &quot;DomicilioNormalizzato&quot;, &quot;DomicilioNonNormalizzato&quot; ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ ],
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;AddressNode&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;mergeFields&quot; : [ ]
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>List Merge Action</type>
            </childElements>
            <childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>DomicilioNonNormalizzato</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;OtherAddressNode&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISBLANK(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;cabComune&quot; : &quot;=&quot;,
    &quot;localita&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:domBlock:addressFCDom:AnagNoNorm:localita%)&quot;,
    &quot;codiceBelfioreComune&quot; : &quot;=&quot;,
    &quot;tipoIndirizzo&quot; : &quot;=\&quot;DOMI\&quot;&quot;,
    &quot;dus&quot; : &quot;=&quot;,
    &quot;cabStato&quot; : &quot;=&quot;,
    &quot;codiceIstatComune&quot; : &quot;=&quot;,
    &quot;codiceUnsdM49&quot; : &quot;=&quot;,
    &quot;numeroCivico&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:domBlock:addressFCDom:AnagNoNorm:civico%)&quot;,
    &quot;tipoNormalizzato&quot; : &quot;=\&quot;N\&quot;&quot;,
    &quot;codiceIstatAnag1&quot; : &quot;=&quot;,
    &quot;indirizzoBreve&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:domBlock:addressFCDom:AnagNoNorm:indirizzo%, \&quot; \&quot; ,%bodyAnagOS:FormStep:domBlock:addressFCDom:AnagNoNorm:civico%)&quot;,
    &quot;codiceBelfioreStato&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:domBlock:addressFCDom:AnagNoNorm:stato%)&quot;,
    &quot;indirizzoCompleto&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:domBlock:addressFCDom:AnagNoNorm:indirizzo%, \&quot; \&quot; ,%bodyAnagOS:FormStep:domBlock:addressFCDom:AnagNoNorm:civico%)&quot;,
    &quot;latitudine&quot; : &quot;=&quot;,
    &quot;abbreviazioneProvincia&quot; : &quot;=&quot;,
    &quot;dug&quot; : &quot;=&quot;,
    &quot;presso&quot; : &quot;=&quot;,
    &quot;codicePostale&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:domBlock:addressFCDom:AnagNoNorm:cap%)&quot;,
    &quot;distrettoCensuario&quot; : &quot;=&quot;,
    &quot;longitudine&quot; : &quot;=&quot;,
    &quot;flagPreview&quot; : &quot;=$Vlocity.FALSE&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                    <sequenceNumber>2.0</sequenceNumber>
                    <type>Set Values</type>
                </childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>DomicilioNormalizzato</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;OtherAddressNode&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;cabComune&quot; : &quot;=%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:codiceCABComune%&quot;,
    &quot;localita&quot; : &quot;=%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:descrizioneLocalita%&quot;,
    &quot;codiceBelfioreComune&quot; : &quot;=%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:codiceBelfioreComune%&quot;,
    &quot;tipoIndirizzo&quot; : &quot;=\&quot;DOMI\&quot;&quot;,
    &quot;dus&quot; : &quot;=%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:nomeOdonimo%&quot;,
    &quot;cabStato&quot; : &quot;=\&quot;A116\&quot;&quot;,
    &quot;codiceIstatComune&quot; : &quot;=&quot;,
    &quot;codiceUnsdM49&quot; : &quot;=&quot;,
    &quot;numeroCivico&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:civico%)&quot;,
    &quot;tipoNormalizzato&quot; : &quot;=\&quot;S\&quot;&quot;,
    &quot;codiceIstatAnag1&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:codiceISTATComune%)&quot;,
    &quot;indirizzoBreve&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:tipoOdonimoAbbreviato%, \&quot; \&quot;,  %bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:nomeOdonimoAbbreviato%)&quot;,
    &quot;codiceBelfioreStato&quot; : &quot;=\&quot;Z000\&quot;&quot;,
    &quot;indirizzoCompleto&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:tipoOdonimo%, \&quot; \&quot;,  %bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:nomeOdonimo%)&quot;,
    &quot;latitudine&quot; : &quot;=%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:latitudine%&quot;,
    &quot;abbreviazioneProvincia&quot; : &quot;=%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:siglaProvincia%&quot;,
    &quot;dug&quot; : &quot;=%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:tipoOdonimo%&quot;,
    &quot;presso&quot; : &quot;=&quot;,
    &quot;codicePostale&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:codiceTerritoriale%)&quot;,
    &quot;distrettoCensuario&quot; : &quot;=&quot;,
    &quot;longitudine&quot; : &quot;=%bodyAnagOS:FormStep:domBlock:addressFCDom:Anag:indirizzo:longitudine%&quot;,
    &quot;flagPreview&quot; : &quot;=$Vlocity.FALSE&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                    <sequenceNumber>1.0</sequenceNumber>
                    <type>Set Values</type>
                </childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>IndirizzoDomicilio</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:FormStep:DomicilioEqualResSelect% == \&quot;No\&quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>Conditional Block</type>
            </childElements>
            <childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>DomicilioComeResidenza</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;DomicilioComeResidenza&quot;,
  &quot;responseJSONNode&quot; : &quot;OtherAddressNode&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:FormStep:DomicilioEqualResSelect% == \&quot;Si\&quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;DomicilioComeResidenza&quot; : &quot;=AddressNode&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                    <sequenceNumber>3.0</sequenceNumber>
                    <type>Set Values</type>
                </childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>ResidenzaNonNormalizzata</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;AddressNode&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISBLANK(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;cabComune&quot; : &quot;=&quot;,
    &quot;localita&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:AnagNoNorm:localita%)&quot;,
    &quot;codiceBelfioreComune&quot; : &quot;=&quot;,
    &quot;tipoIndirizzo&quot; : &quot;=\&quot;RESI\&quot;&quot;,
    &quot;dus&quot; : &quot;=&quot;,
    &quot;cabStato&quot; : &quot;=&quot;,
    &quot;codiceIstatComune&quot; : &quot;=&quot;,
    &quot;codiceUnsdM49&quot; : &quot;=&quot;,
    &quot;numeroCivico&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:AnagNoNorm:civico%)&quot;,
    &quot;tipoNormalizzato&quot; : &quot;=\&quot;N\&quot;&quot;,
    &quot;codiceIstatAnag1&quot; : &quot;=&quot;,
    &quot;indirizzoBreve&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:AnagNoNorm:indirizzo%, \&quot; \&quot; ,%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:AnagNoNorm:civico%)&quot;,
    &quot;codiceBelfioreStato&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:AnagNoNorm:stato%)&quot;,
    &quot;indirizzoCompleto&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:AnagNoNorm:indirizzo%, \&quot; \&quot; ,%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:AnagNoNorm:civico%)&quot;,
    &quot;latitudine&quot; : &quot;=&quot;,
    &quot;abbreviazioneProvincia&quot; : &quot;=&quot;,
    &quot;dug&quot; : &quot;=&quot;,
    &quot;presso&quot; : &quot;=&quot;,
    &quot;codicePostale&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:AnagNoNorm:cap%)&quot;,
    &quot;distrettoCensuario&quot; : &quot;=&quot;,
    &quot;longitudine&quot; : &quot;=&quot;,
    &quot;flagPreview&quot; : &quot;=$Vlocity.FALSE&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                    <sequenceNumber>2.0</sequenceNumber>
                    <type>Set Values</type>
                </childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>ResidenzaNormalizzata</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;AddressNode&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;cabComune&quot; : &quot;=%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:codiceCABComune%&quot;,
    &quot;localita&quot; : &quot;=%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:descrizioneLocalita%&quot;,
    &quot;codiceBelfioreComune&quot; : &quot;=%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:codiceBelfioreComune%&quot;,
    &quot;tipoIndirizzo&quot; : &quot;=\&quot;RESI\&quot;&quot;,
    &quot;dus&quot; : &quot;=%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:nomeOdonimo%&quot;,
    &quot;cabStato&quot; : &quot;=\&quot;A116\&quot;&quot;,
    &quot;codiceIstatComune&quot; : &quot;=&quot;,
    &quot;codiceUnsdM49&quot; : &quot;=&quot;,
    &quot;numeroCivico&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:civico%)&quot;,
    &quot;tipoNormalizzato&quot; : &quot;=\&quot;S\&quot;&quot;,
    &quot;codiceIstatAnag1&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:codiceISTATComune%)&quot;,
    &quot;indirizzoBreve&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:tipoOdonimoAbbreviato%, \&quot; \&quot;,  %bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:nomeOdonimoAbbreviato%)&quot;,
    &quot;codiceBelfioreStato&quot; : &quot;=\&quot;Z000\&quot;&quot;,
    &quot;indirizzoCompleto&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:tipoOdonimo%, \&quot; \&quot;,  %bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:nomeOdonimo%)&quot;,
    &quot;latitudine&quot; : &quot;=%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:latitudine%&quot;,
    &quot;abbreviazioneProvincia&quot; : &quot;=%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:siglaProvincia%&quot;,
    &quot;dug&quot; : &quot;=%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:tipoOdonimo%&quot;,
    &quot;presso&quot; : &quot;=&quot;,
    &quot;codicePostale&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:codiceTerritoriale%)&quot;,
    &quot;distrettoCensuario&quot; : &quot;=&quot;,
    &quot;longitudine&quot; : &quot;=%bodyAnagOS:FormStep:IndirizziBlock:addressFCRes:Anag:indirizzo:longitudine%&quot;,
    &quot;flagPreview&quot; : &quot;=$Vlocity.FALSE&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                    <sequenceNumber>1.0</sequenceNumber>
                    <type>Set Values</type>
                </childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>SetDomiField</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;Domi&quot;,
  &quot;responseJSONNode&quot; : &quot;OtherAddressNode:tipoIndirizzo&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:FormStep:DomicilioEqualResSelect% == \&quot;Si\&quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;Domi&quot; : &quot;=\&quot;DOMI\&quot;&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                    <sequenceNumber>4.0</sequenceNumber>
                    <type>Set Values</type>
                </childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>IndirizzoResidenza</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Conditional Block</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>IndirizziPF</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PF&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>CreateIndirizziNodePG</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;mergeListsOrder&quot; : [ &quot;SedeLegaleNormalizzato&quot;, &quot;SedeLegaleNonNormalizzato&quot; ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ ],
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;AddressNode&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : true,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;mergeFields&quot; : [ ]
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>List Merge Action</type>
            </childElements>
            <childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>SedeLegaleNonNormalizzato</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;AddressNode&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISBLANK(%bodyAnagOS:FormStep:seleBlock:seleFC:Anag%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;cabComune&quot; : &quot;=&quot;,
    &quot;localita&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:seleBlock:seleFC:AnagNoNorm:localita%)&quot;,
    &quot;codiceBelfioreComune&quot; : &quot;=&quot;,
    &quot;tipoIndirizzo&quot; : &quot;=\&quot;SELE\&quot;&quot;,
    &quot;dus&quot; : &quot;=&quot;,
    &quot;cabStato&quot; : &quot;=&quot;,
    &quot;codiceIstatComune&quot; : &quot;=&quot;,
    &quot;codiceUnsdM49&quot; : &quot;=&quot;,
    &quot;numeroCivico&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:seleBlock:seleFC:AnagNoNorm:civico%)&quot;,
    &quot;tipoNormalizzato&quot; : &quot;=\&quot;N\&quot;&quot;,
    &quot;codiceIstatAnag1&quot; : &quot;=&quot;,
    &quot;indirizzoBreve&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:seleBlock:seleFC:AnagNoNorm:indirizzo%, \&quot; \&quot; ,%bodyAnagOS:FormStep:seleBlock:seleFC:AnagNoNorm:civico%)&quot;,
    &quot;codiceBelfioreStato&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:seleBlock:seleFC:AnagNoNorm:stato%)&quot;,
    &quot;indirizzoCompleto&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:seleBlock:seleFC:AnagNoNorm:indirizzo%, \&quot; \&quot; ,%bodyAnagOS:FormStep:seleBlock:seleFC:AnagNoNorm:civico%)&quot;,
    &quot;latitudine&quot; : &quot;=&quot;,
    &quot;abbreviazioneProvincia&quot; : &quot;=&quot;,
    &quot;dug&quot; : &quot;=&quot;,
    &quot;presso&quot; : &quot;=&quot;,
    &quot;codicePostale&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:seleBlock:seleFC:AnagNoNorm:cap%)&quot;,
    &quot;distrettoCensuario&quot; : &quot;=&quot;,
    &quot;longitudine&quot; : &quot;=&quot;,
    &quot;flagPreview&quot; : &quot;=$Vlocity.FALSE&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                    <sequenceNumber>2.0</sequenceNumber>
                    <type>Set Values</type>
                </childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>SedeLegaleNormalizzato</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;AddressNode&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:seleBlock:seleFC:Anag%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;cabComune&quot; : &quot;=%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:codiceCABComune%&quot;,
    &quot;localita&quot; : &quot;=%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:descrizioneLocalita%&quot;,
    &quot;codiceBelfioreComune&quot; : &quot;=%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:codiceBelfioreComune%&quot;,
    &quot;tipoIndirizzo&quot; : &quot;=\&quot;SELE\&quot;&quot;,
    &quot;dus&quot; : &quot;=%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:nomeOdonimo%&quot;,
    &quot;cabStato&quot; : &quot;=\&quot;A116\&quot;&quot;,
    &quot;codiceIstatComune&quot; : &quot;=&quot;,
    &quot;codiceUnsdM49&quot; : &quot;=&quot;,
    &quot;numeroCivico&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:civico%)&quot;,
    &quot;tipoNormalizzato&quot; : &quot;=\&quot;S\&quot;&quot;,
    &quot;codiceIstatAnag1&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:codiceISTATComune%)&quot;,
    &quot;indirizzoBreve&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:tipoOdonimo%, \&quot; \&quot;,  %bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:nomeOdonimo%)&quot;,
    &quot;codiceBelfioreStato&quot; : &quot;=\&quot;Z000\&quot;&quot;,
    &quot;indirizzoCompleto&quot; : &quot;=CONCAT(%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:tipoOdonimo%, \&quot; \&quot;,  %bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:nomeOdonimo%)&quot;,
    &quot;latitudine&quot; : &quot;=%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:latitudine%&quot;,
    &quot;abbreviazioneProvincia&quot; : &quot;=%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:siglaProvincia%&quot;,
    &quot;dug&quot; : &quot;=%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:tipoOdonimo%&quot;,
    &quot;presso&quot; : &quot;=&quot;,
    &quot;codicePostale&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:codiceTerritoriale%)&quot;,
    &quot;distrettoCensuario&quot; : &quot;=&quot;,
    &quot;longitudine&quot; : &quot;=%bodyAnagOS:FormStep:seleBlock:seleFC:Anag:indirizzo:longitudine%&quot;,
    &quot;flagPreview&quot; : &quot;=$Vlocity.FALSE&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                    <sequenceNumber>1.0</sequenceNumber>
                    <type>Set Values</type>
                </childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>IndirizzoSedeLegale</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Conditional Block</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>IndirizziPG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PG&apos;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Indirizzi</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;1 == 1&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>LowercasePFMail</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;stringManipulation&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;outputString&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyAnagOS:FormStep:recapitiBlock:EmailPERInput&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:recapitiBlock:EmailPERInput%)&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;inputString&quot; : &quot;=%bodyAnagOS:FormStep:recapitiBlock:EmailPERInput%&quot;,
    &quot;action&quot; : &quot;=\&quot;toLowerCase\&quot;&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Remote Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>LowercasePGMail</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;stringManipulation&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;outputString&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyAnagOS:FormStep:RecapitiPGBlock:emailPGInput&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:RecapitiPGBlock:emailPGInput%)&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;inputString&quot; : &quot;=%bodyAnagOS:FormStep:RecapitiPGBlock:emailPGInput%&quot;,
    &quot;action&quot; : &quot;=\&quot;toLowerCase\&quot;&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Remote Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>LowercasePGPec</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;stringManipulation&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;outputString&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyAnagOS:FormStep:RecapitiPGBlock:PECPGInput&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:RecapitiPGBlock:PECPGInput%)&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;inputString&quot; : &quot;=%bodyAnagOS:FormStep:RecapitiPGBlock:PECPGInput%&quot;,
    &quot;action&quot; : &quot;=\&quot;toLowerCase\&quot;&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Remote Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>LowerCaseEmailPec</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;$Vlocity.TRUE&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Impiego</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;professione:impiego&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:impiego%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;descrizione&quot; : &quot;=QUERY(\&quot;SELECT Valore__c FROM AnagMappingCodDesc__mdt WHERE Field__c = &apos;ProfessioneImpiego&apos; and Codice__c = &apos;{0}&apos;\&quot;, %bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:impiego%)&quot;,
    &quot;codice&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:impiego%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>MercatoPreferenziale</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;professione:mercatoPreferenziale&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:mercatoPreferenziale%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;descrizione&quot; : &quot;=QUERY(\&quot;SELECT Valore__c FROM AnagMappingCodDesc__mdt WHERE Field__c = &apos;ProfessioneMercato&apos; and Codice__c = &apos;{0}&apos;\&quot;, %bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:mercatoPreferenziale%)&quot;,
    &quot;codice&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:mercatoPreferenziale%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>ProfessionePF</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;professione:professione&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PF&apos;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;descrizione&quot; : &quot;=QUERY(\&quot;SELECT Valore__c FROM AnagMappingCodDesc__mdt WHERE Field__c = &apos;Professione&apos; and Codice__c = &apos;{0}&apos;\&quot;, %bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:professione%)&quot;,
    &quot;codice&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:professione%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>ProfessionePG</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;professione:professione&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PG&apos;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;descrizione&quot; : &quot;=QUERY(\&quot;SELECT Valore__c FROM AnagMappingCodDesc__mdt WHERE Field__c = &apos;Professione&apos; and Codice__c = &apos;{0}&apos;\&quot;, %bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:profPG%)&quot;,
    &quot;codice&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:profPG%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Professione</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:professione%) || ISNOTBLANK(%bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:profPG%)&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Settore</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;professione:settore&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag.professione:settore%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;descrizione&quot; : &quot;=QUERY(\&quot;SELECT Valore__c FROM AnagMappingCodDesc__mdt WHERE Field__c = &apos;ProfessioneSettore&apos; and Codice__c = &apos;{0}&apos;\&quot;, %bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag.professione:settore%)&quot;,
    &quot;codice&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag.professione:settore%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Specializzazione</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;professione:specializzazione&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:specializzazione%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;descrizione&quot; : &quot;=QUERY(\&quot;SELECT Valore__c FROM AnagMappingCodDesc__mdt WHERE Field__c = &apos;ProfessioneSpecializzazione&apos; and Codice__c = &apos;{0}&apos;\&quot;, %bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:specializzazione%)&quot;,
    &quot;codice&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:specializzazione%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TipoPersonaGiuridica</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;professione:personaGiuridica&quot;,
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:tipoPGCode%)&quot;,
  &quot;elementValueMap&quot; : {
    &quot;descrizione&quot; : &quot;=QUERY(\&quot;SELECT Valore__c FROM AnagMappingCodDesc__mdt WHERE Field__c = &apos;TipoPersonaGiuridica&apos; and Codice__c = &apos;{0}&apos;\&quot;, %bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:tipoPGCode%)&quot;,
    &quot;codice&quot; : &quot;=TOSTRING(%bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:tipoPGCode%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Professioni</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PF&apos;) &amp;&amp; (ISNOTBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:professione%))) || \r\n((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PG&apos;) &amp;&amp; (ISNOTBLANK(%bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:tipoPGCode%)))&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Response</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;respHTTP:statusCode = 201&quot;,
  &quot;additionalOutput&quot; : {
    &quot;accountId&quot; : &quot;=%DR:idAcc%&quot;,
    &quot;accountDetailId&quot; : &quot;=%DR:idAccDetails%&quot;,
    &quot;isPrivacy&quot; : &quot;%bodyAnagOS:FormStep:privacyBlock:consPrivacyCheck%&quot;,
    &quot;isSuccess&quot; : &quot;=true&quot;,
    &quot;isOptOut&quot; : &quot;%bodyAnagOS:FormStep:optOutCheck:consPrivacyCheck%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;response&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>19.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseKO</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;elementValueMap&quot; : { },
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;Status&quot; : &quot;=%respHTTP:Status%&quot;,
    &quot;RemoteResult&quot; : &quot;=%respHTTP:RemoteResult%&quot;,
    &quot;errorDescription&quot; : &quot;=%SetApexInput:respBody:error:errorDescription%&quot;,
    &quot;errore&quot; : &quot;=SERIALIZE(%SetApexInput:respBody:error%)&quot;,
    &quot;correlationId&quot; : &quot;=%SetApexInput:respBody:correlationId%&quot;,
    &quot;StatusCode&quot; : &quot;=%respHTTP:StatusCode%&quot;,
    &quot;errorLabel&quot; : &quot;=\&quot;Errore di Integrazione\&quot;&quot;,
    &quot;timestamp&quot; : &quot;=%SetApexInput:respBody:timestamp%&quot;,
    &quot;status&quot; : &quot;=%SetApexInput:respBody:error:errorStatus%&quot;,
    &quot;isSuccess&quot; : &quot;=false&quot;
  },
  &quot;sendJSONPath&quot; : &quot;respHTTP&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>20.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetAccIdIfSuccessInsertKO</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;idAccount&quot;,
  &quot;responseJSONNode&quot; : &quot;response:RemoteResult:idAcc&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%DR:idAcc% == $Vlocity.NULL&quot;,
  &quot;elementValueMap&quot; : {
    &quot;idAccount&quot; : &quot;=QUERY( \&quot;SELECT Id FROM Account WHERE ExternalId__c = &apos;{0}&apos;\&quot;, %respHTTP:anagrafica:codiceFiscale% )&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>17.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetAddressAndContactNodes</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetApexInput</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;respBody&quot; : &quot;=DESERIALIZE(%respHTTP:result%)&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>14.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetInput</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;respBody&quot; : &quot;=%respHTTP:result%&quot;,
    &quot;tipoSoggetto&quot; : &quot;=bodyReq:anagrafica:tipoSoggetto&quot;,
    &quot;isProfessione&quot; : &quot;=IF((((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PF&apos;) &amp;&amp; (ISNOTBLANK(%bodyAnagOS:FormStep:ProfessioniBlock:ProfessioniFC:Anag:professione%))) || ((%bodyAnagOS:PreFormStep:TipologiaSoggSelect% == &apos;PG&apos;) &amp;&amp; (ISNOTBLANK(%bodyAnagOS:FormStep:TipoPGBlock:tipoPGFC:Anag:tipoPGCode%)))),\&quot;Y\&quot;,\&quot;N\&quot;)&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>13.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>setResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;Status&quot; : &quot;=%respHTTP:status%&quot;,
    &quot;RemoteResult&quot; : &quot;= DESERIALIZE(%remoteResp:result%)&quot;,
    &quot;StatusCode&quot; : &quot;= %respHTTP:statusCode%&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>18.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <description>Remote Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>CreateAnagService</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteMethod&quot; : &quot;call&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;elementValueMap&quot; : { },
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;respHTTP&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;IntegrationUtilityAction&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;integrationId&quot; : &quot;=\&quot;creazioneAnagrafica\&quot;&quot;,
    &quot;body&quot; : &quot;=SERIALIZE(%bodyReq%)&quot;,
    &quot;params&quot; : &quot;=SERIALIZE(%CalloutParams%)&quot;
  },
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Remote Action</type>
        </childElements>
        <childElements>
            <description>Set Values Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetCalloutParams</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;CalloutParams:Params&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;HeaderParams&quot; : &quot;%SetHeaderParams%&quot;
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <description>Set Values Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetHeaderParams</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;X-CHANNEL-SUBJECT-NAME&quot; : &quot;%bodyAnagOS:UserName%&quot;,
    &quot;X-CHANNEL-SUBJECT&quot; : &quot;%bodyAnagOS:UserId%&quot;
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <description>Try-Catch Block</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Try-CatchBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;isActive&quot; : true
}</propertySetConfig>
        <sequenceNumber>12.0</sequenceNumber>
        <type>Try Catch Block</type>
    </omniProcessElements>
    <omniProcessKey>InsertAnag_InsertAnag</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : [ ],
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : [ ],
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : [ ],
  &quot;transientValues&quot; : {
    &quot;activateOrDeactivateConsent&quot; : false
  }
}</propertySetConfig>
    <subType>InsertAnag</subType>
    <type>InsertAnag</type>
    <uniqueName>InsertAnag_InsertAnag_Procedure_21</uniqueName>
    <versionNumber>21.0</versionNumber>
</OmniIntegrationProcedure>
