<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <chart>
        <backgroundColor1>#FFFFFF</backgroundColor1>
        <backgroundColor2>#FFFFFF</backgroundColor2>
        <backgroundFadeDir>Diagonal</backgroundFadeDir>
        <chartSummaries>
            <axisBinding>y</axisBinding>
            <column>RowCount</column>
        </chartSummaries>
        <chartType>Donut</chartType>
        <enableHoverLabels>false</enableHoverLabels>
        <expandOthers>false</expandOthers>
        <groupingColumn>Case$Owner</groupingColumn>
        <legendPosition>Right</legendPosition>
        <location>CHART_BOTTOM</location>
        <showAxisLabels>true</showAxisLabels>
        <showPercentage>false</showPercentage>
        <showTotal>true</showTotal>
        <showValues>false</showValues>
        <size>Medium</size>
        <summaryAxisRange>Auto</summaryAxisRange>
        <textColor>#000000</textColor>
        <textSize>12</textSize>
        <titleColor>#000000</titleColor>
        <titleSize>18</titleSize>
    </chart>
    <columns>
        <field>Case$CaseNumber</field>
    </columns>
    <filter>
        <booleanFilter>1 AND 2 AND 3</booleanFilter>
        <criteriaItems>
            <column>Case$RecordType</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>false</isUnlocked>
            <operator>equals</operator>
            <value>Case.CC_Contact_Center</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case$Status</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>false</isUnlocked>
            <operator>equals</operator>
            <value>In gestione,Chiuso</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case$Owner.Profile</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>false</isUnlocked>
            <operator>equals</operator>
            <value>Contact Center</value>
        </criteriaItems>
        <language>en_US</language>
    </filter>
    <format>Summary</format>
    <groupingsDown>
        <dateGranularity>Day</dateGranularity>
        <field>Case$Owner</field>
        <sortOrder>Asc</sortOrder>
    </groupingsDown>
    <name>Totale Contatti Gestiti per Operatore</name>
    <params>
        <name>co</name>
        <value>1</value>
    </params>
    <reportType>Attivit_di_Contatto__c</reportType>
    <scope>organization</scope>
    <showDetails>true</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>true</showSubTotals>
    <timeFrameFilter>
        <dateColumn>Case$Created_Date__c</dateColumn>
        <interval>INTERVAL_CUSTOM</interval>
    </timeFrameFilter>
</Report>
