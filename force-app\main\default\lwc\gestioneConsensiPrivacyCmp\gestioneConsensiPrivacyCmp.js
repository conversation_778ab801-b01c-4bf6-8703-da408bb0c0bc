import { api, LightningElement } from 'lwc';
import LightningModal from 'lightning/modal';

import gestisciConsensiFromIntegrationProcedure from "@salesforce/apex/GestioneConsensiPrivacyCnt.gestisciConsensiFromIntegrationProcedure";

export default class GestioneConsensiPrivacyCmp extends LightningModal {

    @api accountDetailId;
    
    showSpinner;
    showFEIModal;

    params = {};

    connectedCallback(){
        this.showSpinner = true;
        gestisciConsensiFromIntegrationProcedure({recordId: this.accountDetailId})
            .then(result => {
                this.params = {
                    feiid: 'SFEA.SIGN',
                    feiRequestPayload: result.FeaHelper.feiRequest,
                    permissionSetName: null,
                    fiscalCode: result.FeaHelper.userFiscalCode,
                    recordId: this.accountDetailId,
                    society: result.FeaHelper.compagnia
                }
                this.showFEIModal = true;
                this.showSpinner = false;
            })
            .catch(error => {
                console.log("errore => " + JSON.stringify(error));
                this.showSpinner = false;
            });
    }

    toggleLWCModal() {
        console.log('qui dentro');
        this.showFEIModal = false;
        this.close('okay');
    }
   
    //recupera dati della IP come la flexcard della privacy e richiama fei container
}