<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;results\&quot;]&quot;,&quot;ipMethod&quot;:&quot;UniDocumentale_RecuperoStoricoCompleto&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{Params.c__AccountId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Params.c__AccountId\&quot;:\&quot;{Params.c__AccountId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Params.c__AccountId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:25}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniDocumentazioneVisualizzaStorico</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-bottom: #e5e5e5 1px solid; \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7Bsocieta%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12  slds-medium-size_2-of-12  slds-small-size_2-of-12  slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12  slds-medium-size_2-of-12  slds-small-size_2-of-12  slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element0&quot;,&quot;parsedProperty&quot;:{&quot;record&quot;:{&quot;idRevisione&quot;:6105165,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;A&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;012&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;8887778&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2025-09-05&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250819125401000812&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105165,&quot;enteRilascio&quot;:&quot;MINISTERO&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:1,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:null,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;20-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},&quot;mergeField&quot;:&quot;%3Cdiv%3E%7Bsocieta%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;}},{&quot;key&quot;:&quot;element_element_block_0_0_outputField_1_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BtipoDelDocumento%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12  slds-medium-size_2-of-12  slds-small-size_2-of-12  slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-0-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12  slds-medium-size_2-of-12  slds-small-size_2-of-12  slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element1&quot;,&quot;parsedProperty&quot;:{&quot;record&quot;:{&quot;idRevisione&quot;:6105165,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;A&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;012&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;8887778&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2025-09-05&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250819125401000812&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105165,&quot;enteRilascio&quot;:&quot;MINISTERO&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:1,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:null,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;20-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BtipoDelDocumento%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;}},{&quot;key&quot;:&quot;element_element_block_0_0_outputField_2_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BdatiStoricizzati.numeroDocumento%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-1-clone-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element2&quot;},{&quot;key&quot;:&quot;element_element_block_0_0_outputField_3_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BdataInizioValidita%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-2-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element3&quot;,&quot;parsedProperty&quot;:{&quot;record&quot;:{&quot;idRevisione&quot;:6105165,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;A&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;012&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;8887778&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2025-09-05&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250819125401000812&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105165,&quot;enteRilascio&quot;:&quot;MINISTERO&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:1,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:null,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;20-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BdataInizioValidita%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;}},{&quot;key&quot;:&quot;element_element_block_0_0_outputField_4_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BdataScadenza%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-3-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0block_element4&quot;,&quot;parsedProperty&quot;:{&quot;record&quot;:{&quot;idRevisione&quot;:6105165,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;A&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;012&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;8887778&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2025-09-05&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250819125401000812&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105165,&quot;enteRilascio&quot;:&quot;MINISTERO&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:1,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:null,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;20-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BdataScadenza%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;}},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7Bnazione%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_5_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element5&quot;,&quot;parsedProperty&quot;:{&quot;record&quot;:{&quot;idRevisione&quot;:6105165,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;A&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;012&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;8887778&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2025-09-05&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250819125401000812&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105165,&quot;enteRilascio&quot;:&quot;MINISTERO&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:1,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:null,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;20-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},&quot;mergeField&quot;:&quot;%3Cdiv%3E%7Bnazione%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;}},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BdatiStoricizzati.codiceProvincia%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Text-5-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_6_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element6&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7Bcomune%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Text-6-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_7_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element7&quot;,&quot;parsedProperty&quot;:{&quot;record&quot;:{&quot;idRevisione&quot;:6105165,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;A&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;012&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;8887778&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2025-09-05&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250819125401000812&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105165,&quot;enteRilascio&quot;:&quot;MINISTERO&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:1,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:null,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;20-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},&quot;mergeField&quot;:&quot;%3Cdiv%3E%7Bcomune%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;}},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BenteRilascio%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Text-7-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_8_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element8&quot;,&quot;parsedProperty&quot;:{&quot;record&quot;:{&quot;idRevisione&quot;:6105165,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;A&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;012&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;8887778&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2025-09-05&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250819125401000812&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105165,&quot;enteRilascio&quot;:&quot;MINISTERO&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:1,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:null,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;20-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BenteRilascio%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;}},{&quot;name&quot;:&quot;Menu&quot;,&quot;element&quot;:&quot;flexMenu&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;type&quot;:&quot;action&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;variant&quot;:&quot;&quot;,&quot;iconName&quot;:&quot;utility:down&quot;,&quot;overflow&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;menuItems&quot;:[{&quot;name&quot;:&quot;menu-item-1743177862039-0&quot;,&quot;label&quot;:&quot;Scarica Documento&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743177926683-o0yo2loow&quot;,&quot;label&quot;:&quot;Scarica Documento&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753452055776&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1753452638627-a82dq4brg&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753452782149&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;customLwc&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;flyoutLwc&quot;:&quot;feiContainer&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;feiRequestPayload&quot;:&quot;{feiRequestPayload}&quot;,&quot;FiscalCode&quot;:&quot;{FiscalCode}&quot;,&quot;permissionSetName&quot;:&quot;{permissionSetName}&quot;,&quot;FEIID&quot;:&quot;{FEIID}&quot;}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;iconPosition&quot;:&quot;&quot;},{&quot;name&quot;:&quot;menu-item-1743413158864-0&quot;,&quot;label&quot;:&quot;Storico Documento&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743413158877-5bsh0eczd&quot;,&quot;label&quot;:&quot;Storico Documento&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756833253564&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;cardName&quot;:&quot;UniDocumentazioneStoricoDocumento&quot;,&quot;flyoutLwc&quot;:&quot;UniDocumentazioneStoricoDocumento&quot;,&quot;cardNode&quot;:&quot;&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;nomeCompagnia&quot;:&quot;{compagnia}&quot;,&quot;ciu&quot;:&quot;{datiStoricizzati.ciu}&quot;,&quot;tipoDocumento&quot;:&quot;{datiStoricizzati.tipoDelDocumento}&quot;,&quot;accountDetailsId&quot;:&quot;{accountDetailsId}&quot;,&quot;activeUserId&quot;:&quot;{User.userId}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;iconPosition&quot;:&quot;&quot;},{&quot;name&quot;:&quot;menu-item-*************-0&quot;,&quot;label&quot;:&quot;Modifica&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;*************-6w44vbt6n&quot;,&quot;label&quot;:&quot;Modifica&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;OmniScripts&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;layoutType&quot;:&quot;lightning&quot;,&quot;osName&quot;:&quot;UniDoc/UpdateDocument/English&quot;,&quot;flyoutLwc&quot;:&quot;uni-doc-update-document-english&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;ContextId&quot;:&quot;{recordId}&quot;,&quot;dataScadenza&quot;:&quot;{dataScadenza}&quot;,&quot;tipologia&quot;:&quot;{tipologia}&quot;,&quot;nomeCompagnia&quot;:&quot;{nomeCompagnia}&quot;,&quot;ciu&quot;:&quot;{ciu}&quot;,&quot;provincia&quot;:&quot;{provincia}&quot;,&quot;numeroDocumento&quot;:&quot;{numeroDocumento}&quot;,&quot;externalId&quot;:&quot;{externalId}&quot;,&quot;comune&quot;:&quot;{comune}&quot;,&quot;stato&quot;:&quot;{stato}&quot;,&quot;dataValidata&quot;:&quot;{dataValidita}&quot;,&quot;tipologiaRT&quot;:&quot;{tipologiaRT}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},{&quot;name&quot;:&quot;menu-item-1743413256473-0&quot;,&quot;label&quot;:&quot;Elimina&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743413256485-tdc8igpp6&quot;,&quot;label&quot;:&quot;Elimina&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753451535561&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;cardName&quot;:&quot;UniDocumentale_Elimina_Flyout&quot;,&quot;flyoutLwc&quot;:&quot;UniDocumentale_Elimina_Flyout&quot;,&quot;cardNode&quot;:&quot;{record}&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;ciu&quot;:&quot;{ciu}&quot;,&quot;externalId&quot;:&quot;{externalId}&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;compagnia&quot;:&quot;{nomeCompagnia}&quot;,&quot;accountDetailsId&quot;:&quot;{accountDetailsId}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}],&quot;position&quot;:&quot;right&quot;,&quot;iconOnly&quot;:false,&quot;iconPosition&quot;:&quot;right&quot;,&quot;iconSize&quot;:&quot;x-small&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: -webkit-center;\n&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_right &quot;,&quot;style&quot;:&quot;      \n         text-align: -webkit-center;\n&quot;,&quot;theme&quot;:&quot;theme_default&quot;},&quot;elementLabel&quot;:&quot;Menu-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;text-align: -webkit-center;\n&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_right &quot;,&quot;style&quot;:&quot;      \n         text-align: -webkit-center;\n&quot;,&quot;theme&quot;:&quot;theme_default&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_flexMenu_9_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element9&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-bottom: #e5e5e5 1px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element0&quot;,&quot;uKey&quot;:&quot;*************-473&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;results\&quot;]&quot;,&quot;ipMethod&quot;:&quot;UniDocumentale_RecuperoStoricoCompleto&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{Params.c__AccountId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Params.c__AccountId\&quot;:\&quot;{Params.c__AccountId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Params.c__AccountId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:25}]},&quot;title&quot;:&quot;UniDocumentazioneVisualizzaStorico&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfUniDocumentazioneVisualizzaStorico_9_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003froDSAQ&quot;,&quot;MasterLabel&quot;:&quot;cfUniDocumentazioneVisualizzaStorico_9_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlObject&quot;:{&quot;apiVersion&quot;:56,&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX0FwcFBhZ2UiPgogICAgICAgICAgICAgICAgICAgICAgPHByb3BlcnR5IG5hbWU9ImRlYnVnIiB0eXBlPSJCb29sZWFuIj48L3Byb3BlcnR5PgogICAgICAgICAgICAgICAgICAgICAgPHByb3BlcnR5IG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciPjwvcHJvcGVydHk+CiAgICAgICAgICAgICAgICAgICAgPC90YXJnZXRDb25maWc+PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX1JlY29yZFBhZ2UiPgogICAgICAgICAgICAgICAgICAgICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iPjwvcHJvcGVydHk+CiAgICAgICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIj48L3Byb3BlcnR5PgogICAgICAgICAgICAgICAgICAgICAgPC90YXJnZXRDb25maWc+&quot;,&quot;isExplicitImport&quot;:false,&quot;isExposed&quot;:true,&quot;id&quot;:&quot;0Rb9O000003foTsSAI&quot;,&quot;masterLabel&quot;:&quot;UniDocumentazioneVisualizzaStorico&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;]}},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]}],&quot;listenToWidthResize&quot;:false}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;results&quot;:[{&quot;idRevisione&quot;:6105165,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;A&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;012&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;8887778&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2025-09-05&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250819125401000812&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105165,&quot;enteRilascio&quot;:&quot;MINISTERO&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:1,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:null,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;20-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},{&quot;idRevisione&quot;:6105159,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;V&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;012&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;8887778&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2025-09-05&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250819125401000812&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105159,&quot;enteRilascio&quot;:&quot;MINISTERO&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:2,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:&quot;2025-08-20T11:52:14.092000&quot;,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;19-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},{&quot;idRevisione&quot;:6105158,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;V&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;012&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;8887778&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2025-09-05&quot;,&quot;codiceDocumentaleAllegato&quot;:null},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105158,&quot;enteRilascio&quot;:&quot;MINISTERO&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:3,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:&quot;2025-08-19T12:54:02.513000&quot;,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;19-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},{&quot;idRevisione&quot;:6105157,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;V&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;012&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;8887778&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2025-09-05&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250806210633000015&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105157,&quot;enteRilascio&quot;:&quot;MINISTERO&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:4,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:&quot;2025-08-19T12:54:01.779000&quot;,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;19-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},{&quot;idRevisione&quot;:6105155,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;V&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;014&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;9998887&quot;,&quot;dataRilascio&quot;:&quot;2025-07-31&quot;,&quot;dataScadenza&quot;:&quot;2025-09-06&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250806210633000015&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105155,&quot;enteRilascio&quot;:&quot;INPDAP&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;06-09-2025&quot;,&quot;LoopBlockIterationIndex&quot;:5,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:&quot;2025-08-19T12:53:59.311000&quot;,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;19-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},{&quot;idRevisione&quot;:6105144,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;V&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;014&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;********&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2041-08-07&quot;,&quot;codiceDocumentaleAllegato&quot;:&quot;CMZMHL85D19D150MLIP20250806210633000015&quot;},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105144,&quot;enteRilascio&quot;:&quot;INPDAP&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;07-08-2041&quot;,&quot;LoopBlockIterationIndex&quot;:6,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:&quot;2025-08-19T12:52:54.743000&quot;,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;06-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},{&quot;idRevisione&quot;:6105143,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;V&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;014&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;********&quot;,&quot;dataRilascio&quot;:&quot;2025-07-28&quot;,&quot;dataScadenza&quot;:&quot;2041-08-07&quot;,&quot;codiceDocumentaleAllegato&quot;:null},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105143,&quot;enteRilascio&quot;:&quot;INPDAP&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;07-08-2041&quot;,&quot;LoopBlockIterationIndex&quot;:7,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:&quot;2025-08-06T21:06:34.265000&quot;,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;06-08-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},{&quot;idRevisione&quot;:6105045,&quot;idEntitaAssociata&quot;:6102695,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102695,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;V&quot;,&quot;tipoDelDocumento&quot;:&quot;PAT&quot;,&quot;enteRilascio&quot;:&quot;008&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;H224&quot;,&quot;codiceProvincia&quot;:&quot;RC&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;U1X109554X&quot;,&quot;dataRilascio&quot;:&quot;2025-03-04&quot;,&quot;dataScadenza&quot;:&quot;2025-11-05&quot;,&quot;codiceDocumentaleAllegato&quot;:null},&quot;tipoDelDocumento&quot;:&quot;PATENTE&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105045,&quot;enteRilascio&quot;:&quot;MCTC&quot;,&quot;comune&quot;:&quot;REGGIO DI CALABRIA&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;05-11-2025&quot;,&quot;LoopBlockIterationIndex&quot;:8,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:null,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;30-07-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true},{&quot;idRevisione&quot;:6105040,&quot;idEntitaAssociata&quot;:6102690,&quot;datiStoricizzati&quot;:{&quot;id&quot;:6102690,&quot;compagnia&quot;:null,&quot;ciu&quot;:1719,&quot;statoDelDocumento&quot;:&quot;V&quot;,&quot;tipoDelDocumento&quot;:&quot;LIP&quot;,&quot;enteRilascio&quot;:&quot;014&quot;,&quot;allegato&quot;:null,&quot;codiceBelfioreComune&quot;:&quot;L500&quot;,&quot;codiceProvincia&quot;:&quot;PU&quot;,&quot;codiceBelfioreNazione&quot;:&quot;Z000&quot;,&quot;numeroDocumento&quot;:&quot;123t123&quot;,&quot;dataRilascio&quot;:&quot;2025-07-01&quot;,&quot;dataScadenza&quot;:&quot;2030-07-01&quot;,&quot;codiceDocumentaleAllegato&quot;:null},&quot;tipoDelDocumento&quot;:&quot;LIBRETTO PENSIONISTICO&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;nazione&quot;:&quot;ITALIA&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;idDocumento&quot;:6105040,&quot;enteRilascio&quot;:&quot;INPDAP&quot;,&quot;comune&quot;:&quot;URBINO&quot;,&quot;getEnteStatus&quot;:true,&quot;dataScadenza&quot;:&quot;01-07-2030&quot;,&quot;LoopBlockIterationIndex&quot;:9,&quot;totalCount&quot;:9,&quot;dataFineValidita&quot;:&quot;2025-08-06T21:06:30.162000&quot;,&quot;societa&quot;:&quot;Unipol&quot;,&quot;dataInizioValidita&quot;:&quot;27-07-2025&quot;,&quot;LoopBlockIterationStatus&quot;:true}]}</sampleDataSourceResponse>
    <stylingConfiguration>{}</stylingConfiguration>
    <versionNumber>10</versionNumber>
</OmniUiCard>
