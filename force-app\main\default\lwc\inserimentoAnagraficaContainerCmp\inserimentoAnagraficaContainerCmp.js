import { LightningElement, wire } from 'lwc';
import pubsub from 'omnistudio/pubsub';
import { setTabLabel, getFocusedTabInfo, setTabIcon, focusTab, closeTab } from "lightning/platformWorkspaceApi";
import { NavigationMixin } from 'lightning/navigation';
import MyModal from "c/gestioneConsensiPrivacyCmp";

export default class InserimentoAnagraficaContainerCmp extends NavigationMixin(LightningElement) {

    showOmniscriptInserimentoAnagrafica = true;
    showModaleGestioneConsensi = false;

    title;
    message;
    showModalError = false;
    accountId;

    consensiPrivacyInput;

    connectedCallback() {
       
        getFocusedTabInfo().then(async (tabInfo) => {
            console.log('tabInfo ' , JSON.stringify(tabInfo));
            if((tabInfo.customTitle ==  null || tabInfo.customTitle == undefined) &&
                    (tabInfo.title == 'Loading...' || tabInfo.title == 'Caricamento in corso...')){
                await focusTab(tabInfo.tabId);
                await setTabLabel(tabInfo.tabId, "Nuova Anagrafica");
                await setTabIcon(tabInfo.tabId, "custom:custom18");
                
            }else if(tabInfo.customTitle ==  'Nuova Anagrafica'){
                console.log('else');
                await this.closeTab();
            }
            
        });

        pubsub.register('omniscript_action', {
            data: this.handleConferma.bind(this)
        });
        
    }

    renderedcallback(){
        
    }

    handleConferma(data) {
        data.flag_privacy = 'true';
        if(data.isSuccess == false){

            this.gestisciErrori(data);
            return;
        }else{
            if(data.isPrivacy === true){
                this.accountId = data.accountId;
                this.gestisciPrivacy(data.accountDetailId);
                return;
            }

            if(data.isPrivacy === false || data.isPrivacy == ''){
                console.log(' nel navigatre');
                this.navigateToRecordPage(data.accountId);
            }
        }
    }

    gestisciPrivacy(recordId){

        this.consensiPrivacyInput = recordId;
        this.showOmniscriptInserimentoAnagrafica = false;
        this.showModaleGestioneConsensi = true;
        MyModal.open({
            // maps to developer-created `@api options`
            accountDetailId: recordId
            }).then((result) => {
                console.log(result);
            });
    }

    navigateToRecordPage(recordId) {
        
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: recordId,
                objectApiName: 'Account',
                actionName: 'view'
            }
        });
        
    }

    gestisciErrori(data) { 
 
        this.title = data.errorLabel;
        this.message = data.errore;

        this.showModalError = true;
    } 

    handleClose() {
        this.showModalError = false;
    }
    
    handleCloseTotal() {
        
        this.navigateToRecordPage(this.accountId);
        //await this.closeTab();
        
    }

    async closeTab() {
      
        const { tabId } = await getFocusedTabInfo();
        await closeTab(tabId);
    }
    /*async handleOpen() {
    try {
        const tabInfo = await getAllTabInfo();
        let customTab={};
        tabInfo.forEach(item => {
            if(item.url.includes('your Tab Api Name')){ //like my_tab_lwc
                customTab=item;
            }
        });
        await focusTab(customTab.tabId);
        await setTabLabel(customTab.tabId,'YOUR Tab Label NAME');
        await setTabIcon(customTab.tabId, 'utility:page_structure', {
        iconAlt: 'alt name'
        });
        } catch (error) {
            console.log(error);
        }
    }*/

}