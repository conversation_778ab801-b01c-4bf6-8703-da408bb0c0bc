<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>true</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniGetAccAgeDetInfo</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>ciu</filterValue>
        <globalKey>UniGetAccAgeDetInfoCustom5179</globalKey>
        <inputFieldName>SourceSystemIdentifier__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccAgeDetInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetAccAgeDetInfoCustom9939</globalKey>
        <inputFieldName>AccountAgencyDet:Mobile__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccAgeDetInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>mobile</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetails:Relation__r.FinServ__Account__c</filterValue>
        <globalKey>UniGetAccAgeDetInfoCustom6200</globalKey>
        <inputFieldName>Relation__r.FinServ__Account__c</inputFieldName>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccAgeDetInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAgencyDet</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>UniGetAccAgeDetInfoCustom8142</globalKey>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccAgeDetInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAgencyDet</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetAccAgeDetInfoCustom6095</globalKey>
        <inputFieldName>AccountAgencyDet:Email__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccAgeDetInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>userIdAzienda</filterValue>
        <globalKey>UniGetAccAgeDetInfoCustom9020</globalKey>
        <inputFieldName>Relation__r.FinServ__RelatedAccount__c</inputFieldName>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccAgeDetInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAgencyDet</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>UniGetAccAgeDetInfoCustom3968</globalKey>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccAgeDetInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetails:Relation__r.FinServ__RelatedAccount__c</filterValue>
        <globalKey>8b164995-571d-439f-8d9a-32f709d4beb9</globalKey>
        <inputFieldName>Society__c</inputFieldName>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccAgeDetInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAgencyDet</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;userIdAzienda&quot; : &quot;0019X00001IRL2JQAX&quot;,
  &quot;ciu&quot; : &quot;********&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>UniGetAccAgeDetInfo_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
