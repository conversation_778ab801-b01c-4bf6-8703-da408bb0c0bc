<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>true</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <name>NetworkUserExtract</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>NetworkUserExtractCustom8358</globalKey>
        <inputFieldName>NetUser:Society__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>NetworkUserExtract</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>societa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>ORDER BY</filterOperator>
        <filterValue>Preferred__c desc , LastModifiedDate desc</filterValue>
        <globalKey>NetworkUserExtractCustom4486</globalKey>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>NetworkUserExtract</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetUser</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>NetworkUserExtractCustom2332</globalKey>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>NetworkUserExtract</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetUser</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterDataType>STRING</filterDataType>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>societa</filterValue>
        <globalKey>NetworkUserExtractCustom2922</globalKey>
        <inputFieldName>Society__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>NetworkUserExtract</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetUser</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterDataType>BOOLEAN</filterDataType>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;true&apos;</filterValue>
        <globalKey>NetworkUserExtractCustom0jI9O000000u0IHUAYItem3</globalKey>
        <inputFieldName>IsActive__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>NetworkUserExtract</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetUser</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterDataType>STRING</filterDataType>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:FiscalCode__c</filterValue>
        <globalKey>NetworkUserExtractCustom4221</globalKey>
        <inputFieldName>FiscalCode__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>NetworkUserExtract</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetUser</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterDataType>REFERENCE</filterDataType>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>NetworkUserExtractCustom9550</globalKey>
        <inputFieldName>Agency__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>NetworkUserExtract</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetUser</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>UserId</filterValue>
        <globalKey>NetworkUserExtractCustom0jI9O000000u0IHUAYItem0</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>User</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>NetworkUserExtract</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>User</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>NetworkUserExtractCustom0jI9O000000u0IHUAYItem7</globalKey>
        <inputFieldName>NetUser:NetworkUser__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>NetworkUserExtract</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>NetUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>57ccc2e9-baef-4486-a312-86f01592897b</globalKey>
        <inputFieldName>User:Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>NetworkUserExtract</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Name</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;UserId&quot; : &quot;0059X00000XUdwFQAT&quot;,
  &quot;societa&quot; : &quot;SOC_1&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>NetworkUserExtract_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
