<?xml version="1.0" encoding="UTF-8"?>
<CustomLabels xmlns="http://soap.sforce.com/2006/04/metadata">
    <labels>
        <fullName>AccountAgencyRT</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>AccountAgencyRT</shortDescription>
        <value>Agency</value>
    </labels>
    <labels>
        <fullName>AccountPersonAccountRT</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>AccountPersonAccountRT</shortDescription>
        <value>PersonAccount</value>
    </labels>
    <labels>
        <fullName>AccountProspectRT</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>AccountProspectRT</shortDescription>
        <value>Prospect</value>
    </labels>
    <labels>
        <fullName>AccountSocietyRT</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>AccountSocietyRT</shortDescription>
        <value>Society</value>
    </labels>
    <labels>
        <fullName>AgencyAccRecType</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>AgencyAccRecType</shortDescription>
        <value>Agency</value>
    </labels>
    <labels>
        <fullName>AgencyExtIdAAR</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>AgencyExtIdAAR</shortDescription>
        <value>R_</value>
    </labels>
    <labels>
        <fullName>AgencyExtIdStr</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>AgencyExtIdStr</shortDescription>
        <value>AGE_</value>
    </labels>
    <labels>
        <fullName>Appointment_Deleted</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Appointment Deleted</shortDescription>
        <value>Appointment Deleted</value>
    </labels>
    <labels>
        <fullName>Appointment_Modified</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Appointment Modified</shortDescription>
        <value>Appointment Modified</value>
    </labels>
    <labels>
        <fullName>Appointment_Taken</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Appointment Taken</shortDescription>
        <value>Appointment Taken</value>
    </labels>
    <labels>
        <fullName>Asset</fullName>
        <categories>Case</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Asset</shortDescription>
        <value>Asset</value>
    </labels>
    <labels>
        <fullName>Assignee</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Assignee</shortDescription>
        <value>assignee</value>
    </labels>
    <labels>
        <fullName>CIPExtIdStr</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>CIPExtIdStr</shortDescription>
        <value>CIP_</value>
    </labels>
    <labels>
        <fullName>Cancel</fullName>
        <categories>Case, CorrelatoA</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Cancel</shortDescription>
        <value>Cancel</value>
    </labels>
    <labels>
        <fullName>CaseAccountError</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>CaseAccountError</shortDescription>
        <value>Non è presente nel DB un anagrafica con il seguente CF o P.Iva:</value>
    </labels>
    <labels>
        <fullName>CaseAgentError</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>CaseAgentError</shortDescription>
        <value>Non è presente sul DB un Agenzia:</value>
    </labels>
    <labels>
        <fullName>CaseAssignedError</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>CaseAssignedError</shortDescription>
        <value>Case visibile solo ai responsabili da riassegnare manualmente</value>
    </labels>
    <labels>
        <fullName>CaseId</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>CaseId</shortDescription>
        <value>CaseId</value>
    </labels>
    <labels>
        <fullName>CasePolizzaError</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>CasePolizzaError</shortDescription>
        <value>Non è presente nel DB un Polizza:</value>
    </labels>
    <labels>
        <fullName>ChildObjectNames</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ChildObjectNames</shortDescription>
        <value>Opportunities,InsurancePolicies,ServiceAppointments,Cases</value>
    </labels>
    <labels>
        <fullName>ConiAccessLevelKey</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiAccessLevelKey</shortDescription>
        <value>accessLevel</value>
    </labels>
    <labels>
        <fullName>ConiAccessLevelValue</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiAccessLevelValue</shortDescription>
        <value>AccessLevel</value>
    </labels>
    <labels>
        <fullName>ConiAccountId</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiAccountId</shortDescription>
        <value>AccountId</value>
    </labels>
    <labels>
        <fullName>ConiAccountKey</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiAccountKey</shortDescription>
        <value>account</value>
    </labels>
    <labels>
        <fullName>ConiAgency</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiAgency</shortDescription>
        <value>Agency__c</value>
    </labels>
    <labels>
        <fullName>ConiAssignedGroup</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiAssignedGroup</shortDescription>
        <value>AssignedGroup__c</value>
    </labels>
    <labels>
        <fullName>ConiAssignedTo</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiAssignedTo</shortDescription>
        <value>AssignedTo__c</value>
    </labels>
    <labels>
        <fullName>ConiCaseAccessLevelValue</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiCaseAccessLevelValue</shortDescription>
        <value>CaseAccessLevel</value>
    </labels>
    <labels>
        <fullName>ConiCaseShare</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiCaseShare</shortDescription>
        <value>CaseShare</value>
    </labels>
    <labels>
        <fullName>ConiConiAccountValue</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiConiAccountValue</shortDescription>
        <value>Coni_Account</value>
    </labels>
    <labels>
        <fullName>ConiConiObjectShare</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiConiObjectShare</shortDescription>
        <value>Coni_Object_Share</value>
    </labels>
    <labels>
        <fullName>ConiConiQueue</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiConiQueue</shortDescription>
        <value>Coni_Queue</value>
    </labels>
    <labels>
        <fullName>ConiEdit</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiEdit</shortDescription>
        <value>Edit</value>
    </labels>
    <labels>
        <fullName>ConiEditMapKey</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiEditMapKey</shortDescription>
        <value>editMap</value>
    </labels>
    <labels>
        <fullName>ConiGroup</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiGroup</shortDescription>
        <value>Group__c</value>
    </labels>
    <labels>
        <fullName>ConiGruppoReferenti</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiGruppoReferenti</shortDescription>
        <value>R_AGE_</value>
    </labels>
    <labels>
        <fullName>ConiId</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiId</shortDescription>
        <value>Id</value>
    </labels>
    <labels>
        <fullName>ConiIdKey</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiIdKey</shortDescription>
        <value>id</value>
    </labels>
    <labels>
        <fullName>ConiInsurancePolicyShare</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiInsurancePolicyShare</shortDescription>
        <value>InsurancePolicyShare</value>
    </labels>
    <labels>
        <fullName>ConiIsSetRef</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiIsSetRef</shortDescription>
        <value>isSetRef__c</value>
    </labels>
    <labels>
        <fullName>ConiName</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiName</shortDescription>
        <value>Name</value>
    </labels>
    <labels>
        <fullName>ConiObjNameKey</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiObjNameKey</shortDescription>
        <value>objName</value>
    </labels>
    <labels>
        <fullName>ConiOpportunityAccessLevelValue</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiOpportunityAccessLevelValue</shortDescription>
        <value>OpportunityAccessLevel</value>
    </labels>
    <labels>
        <fullName>ConiOpportunityShare</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiOpportunityShare</shortDescription>
        <value>OpportunityShare</value>
    </labels>
    <labels>
        <fullName>ConiQueryField</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiQueryField</shortDescription>
        <value>[field]</value>
    </labels>
    <labels>
        <fullName>ConiQueryObjName</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiQueryObjName</shortDescription>
        <value>[objName]</value>
    </labels>
    <labels>
        <fullName>ConiQueryObjShare</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiQueryObjShare</shortDescription>
        <value>[objShare]</value>
    </labels>
    <labels>
        <fullName>ConiQueryShares</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiQueryShares</shortDescription>
        <value>SELECT Id, Name, (SELECT Id, Name, AccountId, (SELECT Id, UserOrGroupId, UserOrGroup.Name, OpportunityAccessLevel, RowCause FROM Shares WHERE OpportunityAccessLevel = &apos;Edit&apos;) FROM Opportunities), (SELECT Id, Name, (SELECT Id, UserOrGroupId, UserOrGroup.Name, AccessLevel, RowCause FROM Shares WHERE AccessLevel = &apos;Edit&apos;) FROM InsurancePolicies), (SELECT Id, AccountId, (SELECT Id, UserOrGroupId, UserOrGroup.Name, AccessLevel, RowCause FROM Shares WHERE AccessLevel = &apos;Edit&apos;) FROM ServiceAppointments), (SELECT Id, CaseNumber, AccountId, (SELECT Id, UserOrGroupId, UserOrGroup.Name, CaseAccessLevel, RowCause FROM Shares WHERE CaseAccessLevel = &apos;Edit&apos;) FROM Cases) FROM Account WHERE Id IN :setAccsOpps</value>
    </labels>
    <labels>
        <fullName>ConiRead</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiRead</shortDescription>
        <value>Read</value>
    </labels>
    <labels>
        <fullName>ConiReadMapKey</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiReadMapKey</shortDescription>
        <value>readMap</value>
    </labels>
    <labels>
        <fullName>ConiRefUpdateIdKey</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiRefUpdateIdKey</shortDescription>
        <value>refUpdateId</value>
    </labels>
    <labels>
        <fullName>ConiServiceAppointmentShare</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiServiceAppointmentShare</shortDescription>
        <value>ServiceAppointmentShare</value>
    </labels>
    <labels>
        <fullName>ConiShare</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiShare</shortDescription>
        <value>Share</value>
    </labels>
    <labels>
        <fullName>ConiUserOrGroup</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiUserOrGroup</shortDescription>
        <value>UserOrGroup</value>
    </labels>
    <labels>
        <fullName>ConiUserOrGroupId</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ConiUserOrGroupId</shortDescription>
        <value>UserOrGroupId</value>
    </labels>
    <labels>
        <fullName>CustomObjGroupRecType</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>CustomObjGroupRecType</shortDescription>
        <value>UserGroup</value>
    </labels>
    <labels>
        <fullName>Customer</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Customer</shortDescription>
        <value>Customer</value>
    </labels>
    <labels>
        <fullName>Date</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Date</shortDescription>
        <value>date</value>
    </labels>
    <labels>
        <fullName>EditAccessLevel</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>EditAccessLevel</shortDescription>
        <value>Edit</value>
    </labels>
    <labels>
        <fullName>Error</fullName>
        <categories>Case, CorrelatoA</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Error</shortDescription>
        <value>Error</value>
    </labels>
    <labels>
        <fullName>ErrorUpdatingRecord</fullName>
        <categories>Case, CorrelatoA</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ErrorUpdatingRecord</shortDescription>
        <value>An Error Happened While Updating the Record(s)</value>
    </labels>
    <labels>
        <fullName>EventCollaborativoDeleteCustomError</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>EventCollaborativoDeleteCustomError</shortDescription>
        <value>Delete this Appointment is not possible before taking charge</value>
    </labels>
    <labels>
        <fullName>EventCollaborativoUpdateCustomError</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>EventCollaborativoUpdateCustomError</shortDescription>
        <value>Changes to this appointment are not possible before taking charge</value>
    </labels>
    <labels>
        <fullName>EventDeleteCustomError</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>EventDeleteCustomError</shortDescription>
        <value>Delete this Appointment is not possible</value>
    </labels>
    <labels>
        <fullName>EventSyncContactHistory_Cancel</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>EventSyncContactHistory_Cancel</shortDescription>
        <value>Event Cancelled</value>
    </labels>
    <labels>
        <fullName>EventSyncContactHistory_Create</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>EventSyncContactHistory_Create</shortDescription>
        <value>New Event Created</value>
    </labels>
    <labels>
        <fullName>EventSyncContactHistory_Update</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>EventSyncContactHistory_Update</shortDescription>
        <value>Event Updated</value>
    </labels>
    <labels>
        <fullName>EventUpdateCustomError</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>EventUpdateCustomError</shortDescription>
        <value>Changes to this Appointment are not possible</value>
    </labels>
    <labels>
        <fullName>FederationIdWarningMsg</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>FederationIdWarningMsg</shortDescription>
        <value>L&apos;utente corrente NON ha il FederationIdentifier impostato. Il cruscotto non può essere utilizzato.</value>
    </labels>
    <labels>
        <fullName>Incident</fullName>
        <categories>Case, CorrelatoA</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Incident</shortDescription>
        <value>Sinistro</value>
    </labels>
    <labels>
        <fullName>InsurancePolicy</fullName>
        <categories>Case</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>InsurancePolicy</shortDescription>
        <value>Insurance Policy</value>
    </labels>
    <labels>
        <fullName>InsurancePolicyShare</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>InsurancePolicyShare</shortDescription>
        <value>InsurancePolicyShare</value>
    </labels>
    <labels>
        <fullName>LeaveDaysCase</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>LeaveDaysCase</shortDescription>
        <value>4</value>
    </labels>
    <labels>
        <fullName>New</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>New</shortDescription>
        <value>new</value>
    </labels>
    <labels>
        <fullName>New_Female</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>New_Female</shortDescription>
        <value>new</value>
    </labels>
    <labels>
        <fullName>New_Male</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>New_Male</shortDescription>
        <value>new</value>
    </labels>
    <labels>
        <fullName>NoRecordId</fullName>
        <categories>Case, CorrelatoA</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>NoRecordId</shortDescription>
        <value>There was a problem</value>
    </labels>
    <labels>
        <fullName>Opportunity</fullName>
        <categories>Opportunity</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Opportunity</shortDescription>
        <value>Opportunity</value>
    </labels>
    <labels>
        <fullName>OpportunityAccessLevel</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>OpportunityAccessLevel</shortDescription>
        <value>OpportunityAccessLevel</value>
    </labels>
    <labels>
        <fullName>OpportunityId</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>OpportunityId</shortDescription>
        <value>OpportunityId</value>
    </labels>
    <labels>
        <fullName>OpportunityShare</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>OpportunityShare</shortDescription>
        <value>OpportunityShare</value>
    </labels>
    <labels>
        <fullName>OwnerId</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>OwnerId</shortDescription>
        <value>Integration Mulesoft</value>
    </labels>
    <labels>
        <fullName>OwnerId2</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>OwnerId2</shortDescription>
        <value>Mulesoft Integration</value>
    </labels>
    <labels>
        <fullName>ParentId</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ParentId</shortDescription>
        <value>ParentId</value>
    </labels>
    <labels>
        <fullName>PersonAccRecType</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>PersonAccRecType</shortDescription>
        <value>PersonAccount</value>
    </labels>
    <labels>
        <fullName>PushNotificationBodyCreation</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>PushNotificationBodyCreation</shortDescription>
        <value>You have a new task [task ID] to do - Regulatory obligation</value>
    </labels>
    <labels>
        <fullName>PushNotificationBodyReassignment</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>PushNotificationBodyReassignment</shortDescription>
        <value>The task [task ID] has been assigned to you by job</value>
    </labels>
    <labels>
        <fullName>PushNotificationLeoActivityCodeCheck</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>PushNotificationLeoActivityCodeCheck</shortDescription>
        <value>********
********
********
********
********
********
********
********
********
********
10031038
10031039
10031040
10031041
10031042
10031043
10031044
10031045
10031046
10031047
10031048
10031049
10031050
10031051
10031052
10031053
10031054
10031055
10031056
10031057
10031058
10031059
10031060
10031061
10031062
10031063
10031064
10031065
10031066
10031067
10031068
10031069
10031070
10031071
10031072
10031073
10031074
10031075
10031078
10031079</value>
    </labels>
    <labels>
        <fullName>PushNotificationTitleCreation</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>PushNotificationTitleCreation</shortDescription>
        <value>Regulatory activities</value>
    </labels>
    <labels>
        <fullName>PushNotificationTitleReaasign</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>PushNotificationTitleReaasign</shortDescription>
        <value>Internal re-assignment</value>
    </labels>
    <labels>
        <fullName>ReadAccessLevel</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ReadAccessLevel</shortDescription>
        <value>Read</value>
    </labels>
    <labels>
        <fullName>RefGroupLbl</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>RefGroupLbl</shortDescription>
        <value>- Referenti</value>
    </labels>
    <labels>
        <fullName>ReferentsGroup</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>ReferentsGroup</shortDescription>
        <value>- Referenti</value>
    </labels>
    <labels>
        <fullName>RelatedRecordsUpdated</fullName>
        <categories>Case, CorrelatoA</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>RelatedRecordsUpdated</shortDescription>
        <value>Records Updated Successfully</value>
    </labels>
    <labels>
        <fullName>RespGrpAgency</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>RespGrpAgency</shortDescription>
        <value>R_AGE_</value>
    </labels>
    <labels>
        <fullName>Save</fullName>
        <categories>Case, CorrelatoA</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Save</shortDescription>
        <value>Save</value>
    </labels>
    <labels>
        <fullName>Search</fullName>
        <categories>Case, CorrelatoA</categories>
        <language>it</language>
        <protected>false</protected>
        <shortDescription>Search</shortDescription>
        <value>Search</value>
    </labels>
    <labels>
        <fullName>Shares</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Shares</shortDescription>
        <value>Shares</value>
    </labels>
    <labels>
        <fullName>SocietyExtIdStr</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>SocietyExtIdStr</shortDescription>
        <value>SOC_</value>
    </labels>
    <labels>
        <fullName>Success</fullName>
        <categories>Case, CorrelatoA</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>Success</shortDescription>
        <value>Success</value>
    </labels>
    <labels>
        <fullName>TakeInCharge</fullName>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>TakeInCharge</shortDescription>
        <value>Prendi in carico</value>
    </labels>
    <labels>
        <fullName>UserOrGroupId</fullName>
        <categories>ConiVisibilitaLabels</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>UserOrGroupId</shortDescription>
        <value>UserOrGroupId</value>
    </labels>
    <labels>
        <fullName>by</fullName>
        <categories>Event, ContactHistory</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>by</shortDescription>
        <value>by</value>
    </labels>
    <labels>
        <fullName>noAccountWarning</fullName>
        <categories>Case, CorrelatoA</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>noAccountWarning</shortDescription>
        <value>There is no Account linked to this Case.</value>
    </labels>
</CustomLabels>
