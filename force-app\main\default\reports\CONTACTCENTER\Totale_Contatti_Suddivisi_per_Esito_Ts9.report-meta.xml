<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <chart>
        <backgroundColor1>#FFFFFF</backgroundColor1>
        <backgroundColor2>#FFFFFF</backgroundColor2>
        <backgroundFadeDir>Diagonal</backgroundFadeDir>
        <chartSummaries>
            <axisBinding>y</axisBinding>
            <column>RowCount</column>
        </chartSummaries>
        <chartType>HorizontalBarGrouped</chartType>
        <enableHoverLabels>false</enableHoverLabels>
        <expandOthers>true</expandOthers>
        <groupingColumn>Case$Owner</groupingColumn>
        <legendPosition>Right</legendPosition>
        <location>CHART_BOTTOM</location>
        <secondaryGroupingColumn>CDF1</secondaryGroupingColumn>
        <showAxisLabels>true</showAxisLabels>
        <showPercentage>false</showPercentage>
        <showTotal>false</showTotal>
        <showValues>false</showValues>
        <size>Medium</size>
        <summaryAxisRange>Auto</summaryAxisRange>
        <textColor>#000000</textColor>
        <textSize>12</textSize>
        <titleColor>#000000</titleColor>
        <titleSize>18</titleSize>
    </chart>
    <columns>
        <field>Case$CaseNumber</field>
    </columns>
    <columns>
        <field>Case$Esito__c</field>
    </columns>
    <columns>
        <field>Case$Intermediate_Outcome__c</field>
    </columns>
    <customDetailFormulas>
        <calculatedFormula>IF(
    NOT(ISBLANK(TEXT(Case.Esito__c))),
    TEXT(Case.Esito__c),
    TEXT(Case.Intermediate_Outcome__c)
)</calculatedFormula>
        <dataType>Text</dataType>
        <developerName>CDF1</developerName>
        <label>Esito attuale</label>
        <scale>2</scale>
    </customDetailFormulas>
    <filter>
        <booleanFilter>1 AND (2 OR 3) AND 4</booleanFilter>
        <criteriaItems>
            <column>Case$RecordType</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>false</isUnlocked>
            <operator>equals</operator>
            <value>Case.CC_Contact_Center</value>
        </criteriaItems>
        <criteriaItems>
            <column>Case$Esito__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>false</isUnlocked>
            <operator>notEqual</operator>
            <value></value>
        </criteriaItems>
        <criteriaItems>
            <column>Case$Intermediate_Outcome__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>false</isUnlocked>
            <operator>notEqual</operator>
            <value></value>
        </criteriaItems>
        <criteriaItems>
            <column>Case$Owner.Profile</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>false</isUnlocked>
            <operator>equals</operator>
            <value>Contact Center</value>
        </criteriaItems>
        <language>en_US</language>
    </filter>
    <format>Summary</format>
    <groupingsDown>
        <dateGranularity>Day</dateGranularity>
        <field>Case$Owner</field>
        <sortOrder>Asc</sortOrder>
    </groupingsDown>
    <groupingsDown>
        <dateGranularity>Day</dateGranularity>
        <field>CDF1</field>
        <sortOrder>Asc</sortOrder>
    </groupingsDown>
    <name>Totale Contatti Suddivisi per Esito</name>
    <params>
        <name>co</name>
        <value>1</value>
    </params>
    <reportType>Attivit_di_Contatto__c</reportType>
    <scope>organization</scope>
    <showDetails>true</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>true</showSubTotals>
    <sortColumn>Case$Esito__c</sortColumn>
    <sortOrder>Asc</sortOrder>
    <timeFrameFilter>
        <dateColumn>Case$Created_Date__c</dateColumn>
        <interval>INTERVAL_CUSTOM</interval>
    </timeFrameFilter>
</Report>
