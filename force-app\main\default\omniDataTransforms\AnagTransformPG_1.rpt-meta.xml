<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <description><PERSON><PERSON>: 1387195 fix VOC KPI field key</description>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;Key&quot; : {
    &quot;AccountKeyAgency&quot; : null,
    &quot;AccountKey&quot; : {
      &quot;&quot; : &quot;&quot;,
      &quot;CRMA_VALUS&quot; : {
        &quot;Value&quot; : 2
      },
      &quot;MDM_CONTATTABILITA&quot; : {
        &quot;Value&quot; : &quot;No&quot;
      },
      &quot;TPD_DATAULTIMOACCESSO&quot; : {
        &quot;Value&quot; : &quot;01/05/2024&quot;
      },
      &quot;CRMA_CAPACITADISPESA&quot; : {
        &quot;Value&quot; : &quot;Alta&quot;
      },
      &quot;PP_TITOLARITACLIENTE&quot; : {
        &quot;Value&quot; : &quot;Cliente Unipol&quot;
      },
      &quot;MEDAGLIA_VOC&quot; : null,
      &quot;TPD_ACCESSO&quot; : null,
      &quot;ANAG2_STATOCIVILE&quot; : {
        &quot;Value&quot; : &quot;Libero/Single&quot;
      },
      &quot;ANAG2_FLAGFIGLI&quot; : {
        &quot;Value&quot; : true
      },
      &quot;ANAG2_TITOLODISTUDIO&quot; : {
        &quot;Value&quot; : &quot;Laurea/Master&quot;
      },
      &quot;ANAG2_TIPORISPARMIO&quot; : {
        &quot;Value&quot; : &quot;Alto&quot;
      },
      &quot;ANAG2_FLAGCONIUGECARICO&quot; : {
        &quot;Value&quot; : false
      },
      &quot;ANAG2_NUMEROFIGLIOCARICO&quot; : {
        &quot;Value&quot; : 2
      },
      &quot;ANAG2_REDDITOLAVORO&quot; : {
        &quot;Value&quot; : 40
      },
      &quot;ANAG2_EVOLUZIONEREDDITOLAVORO&quot; : {
        &quot;Value&quot; : &quot;Media&quot;
      },
      &quot;ANAG2_EVOLUZIONEALTRIREDDITI&quot; : {
        &quot;Value&quot; : &quot;Alta&quot;
      },
      &quot;ANAG2_SPESEINCOMPRIMIBILI&quot; : {
        &quot;Value&quot; : 2500
      },
      &quot;ANAG2_SPESECOMPRIMIBILI&quot; : {
        &quot;Value&quot; : 1000
      },
      &quot;ANAG2_DATANASCITACONIUGE&quot; : {
        &quot;Value&quot; : &quot;22/06/1981&quot;
      },
      &quot;ANAG2_NUMERONUCLEOFAMIGLIA&quot; : {
        &quot;Value&quot; : 2
      },
      &quot;ANAG2_FLAGANIMALIDOMESTICI&quot; : {
        &quot;Value&quot; : true
      },
      &quot;ANAG2_FLAGCOLLABORATORIDOMESTICI&quot; : {
        &quot;Value&quot; : false
      },
      &quot;ANAG2_FLAGCASAPROPRIETA&quot; : {
        &quot;Value&quot; : true
      },
      &quot;ANAG2_FLAGCASAAFFITTO&quot; : {
        &quot;Value&quot; : false
      },
      &quot;ANAG2_FLAGSECONDACASA&quot; : {
        &quot;Value&quot; : false
      },
      &quot;ANAG2_FLAGMUTUO&quot; : {
        &quot;Value&quot; : true
      },
      &quot;BPER_MARCHIATURASOGGETTO&quot; : {
        &quot;Value&quot; : &quot;Bancassurance&quot;
      },
      &quot;BPER_TUTELASALVAGUARDIABPER&quot; : {
        &quot;Value&quot; : &quot;17/12/2025&quot;
      },
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMOANNO&quot; : null,
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMI3ANNI&quot; : null,
      &quot;CRIBIS_NDIPENDENTIPERINQUADRAMENTO&quot; : null,
      &quot;CRIBIS_DETTAGLIOTFR&quot; : null,
      &quot;ConditionalBlock2Status&quot; : true,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;LoopBlockIterationIndex&quot; : 24
    }
  },
  &quot;Value&quot; : {
    &quot;accountDetail_IsCorporate&quot; : &quot;Si&quot;,
    &quot;accountDetail_companyType&quot; : &quot;Persona Giur L1&quot;,
    &quot;accountDetail_originator&quot; : &quot;Sistema ANAG 1&quot;,
    &quot;sinistri&quot; : &quot;ND&quot;,
    &quot;accountAccountRelationSociey_Id&quot; : &quot;a009Q00000NqPzdQAF&quot;,
    &quot;account_cf&quot; : &quot;***********&quot;,
    &quot;user_name&quot; : &quot;Luca Parisi&quot;,
    &quot;accountDetail_pagamento&quot; : &quot;[pagamento]&quot;,
    &quot;accounDetail_VatNumber&quot; : &quot;PT234567890&quot;,
    &quot;contenzioso&quot; : &quot;ND&quot;,
    &quot;accountAgencyDetail_FeaFlagSubsription&quot; : &quot;N/D&quot;,
    &quot;agenzia_Id&quot; : &quot;0019X00000sU7HvQAK&quot;,
    &quot;accountDetail_CodiceAteco&quot; : &quot;AT 5556&quot;,
    &quot;accountAccountRelationAgency_Id&quot; : &quot;a009Q00000NqoOTQAZ&quot;,
    &quot;user_id&quot; : &quot;0059Q00000PIMo2QAH&quot;,
    &quot;compagnia_Id&quot; : &quot;0019X00000sU898QAC&quot;,
    &quot;accountAgencyDetail_mobile&quot; : &quot;N/D&quot;,
    &quot;accountDetail_Id&quot; : &quot;a1i9Q00000cvAZRQA2&quot;,
    &quot;accountAgencyDetail_email&quot; : &quot;N/D&quot;,
    &quot;accountDetail_sedeLegale&quot; : &quot;N/D&quot;
  }
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>AnagTransformPG</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem12</globalKey>
        <inputFieldName>Value:accountDetailMDM_emailStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:StatoEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem49</globalKey>
        <inputFieldName>Value:accountDetail_feaVersionType</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:FeaVersionType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem13</globalKey>
        <inputFieldName>Key:AccountKeyAgency:CRM_CLIENTE_VIP:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:ClienteVip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem50</globalKey>
        <inputFieldName>Key:AccountKey:TPD_COMPORTAMENTOOMNICANALE:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:ModUltimoAccesso:ComportamentoOmnicanale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem22</globalKey>
        <inputFieldName>Value:accountAgencyDetail_clienteAltraAgenzia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ClienteAltraAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem33</globalKey>
        <inputFieldName>Value:ReferenteAziendale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ReferenteAziendale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem21</globalKey>
        <inputFieldName>Key:AccountKey:TPD_CANALEACCESSO:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:ModUltimoAccesso:CanaleAccesso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;TPD_APP&quot; : &quot;App&quot;,
  &quot;TPD_WEB&quot; : &quot;Web&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>--</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem32</globalKey>
        <inputFieldName>Value:accountAccountRelationSociey_Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>RappBancari:AccountAccountRelationId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem15</globalKey>
        <inputFieldName>Value:accountDetail_companyType</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:FormaGiuridica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem39</globalKey>
        <inputFieldName>Value:accountDetailMDM_Email</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem52</globalKey>
        <inputFieldName>Value:account_cf</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:PartitaIVA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem34</globalKey>
        <inputFieldName>Value:accountDetailMDM_mobileStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:StatoCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem10</globalKey>
        <inputFieldName>Value:gestoreAnagrafica</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:GestoreAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem47</globalKey>
        <inputFieldName>Value:accountDetail_feaAdesione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:feaAdesione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem20</globalKey>
        <inputFieldName>Key:AccountKey:CRMA_VALUS:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:Valus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem31</globalKey>
        <inputFieldName>Value:accountDetail_FormulaAdesioneFEA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:FeaAdesione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem19</globalKey>
        <inputFieldName>Key:AccountKey:BANK_CLIENTEBPER.Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>RappBancari:SegnalazioneBanche</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem8</globalKey>
        <inputFieldName>CounterConvenzioni</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Convenzioni:counter</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem45</globalKey>
        <inputFieldName>Key:AccountKey:MDM_CONTATTABILITA:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:Contattabilita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem9</globalKey>
        <inputFieldName>Key:AccountKey:PP_TITOLARITACLIENTE:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:TitolaritaCliente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem46</globalKey>
        <inputFieldName>Key:AccountKey:TPD_PRESENZAAPP:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:ModUltimoAccesso:PresenzaApp</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;false&quot; : &quot;No&quot;,
  &quot;true&quot; : &quot;Si&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem14</globalKey>
        <inputFieldName>Value:accountDetail_fea</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:Adesione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem51</globalKey>
        <inputFieldName>Value:accountDetail_feaCellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:Cellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem7</globalKey>
        <inputFieldName>Key:AccountKey:CRMA_CAPACITADISPESA:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:CapacitaDiSpesa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem44</globalKey>
        <inputFieldName>Value:accountDetail_feaEmail</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:Email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem28</globalKey>
        <inputFieldName>statoSoggetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:StatoSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem27</globalKey>
        <inputFieldName>Value:accountDetail_IsCorporate</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:IsCorporate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem30</globalKey>
        <inputFieldName>Value:accountDetailMDM_Mobile</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Cellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem38</globalKey>
        <inputFieldName>Value:account_eta</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Eta</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem29</globalKey>
        <inputFieldName>Abbinato</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>Anagrafica:IsAbbinato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem37</globalKey>
        <inputFieldName>Abbinato</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>RappBancari:IsAbbinato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem18</globalKey>
        <inputFieldName>Value:accountDetail_isGroupMember</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:AppartenenzaNucleo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>Mancante</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem35</globalKey>
        <inputFieldName>Value:accountDetail_SourceSystemConsentCode</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ConsensoPrivacy</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem55</globalKey>
        <inputFieldName>Convenzioni:conv</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Convenzioni:conv</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem4</globalKey>
        <inputFieldName>Value:accountDetail_originator</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Originator</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;1&quot; : &quot;Unipol Assicurazioni S.p.A&quot;,
  &quot;2&quot; : &quot;BIM&quot;,
  &quot;4&quot; : &quot;UNISALUTE&quot;,
  &quot;5&quot; : &quot;LINEAR&quot;,
  &quot;7&quot; : &quot;FATTORIE DEL CERRO&quot;,
  &quot;8&quot; : &quot;LINEAR LIFE&quot;,
  &quot;10&quot; : &quot;ALG&quot;,
  &quot;11&quot; : &quot;NOVAAEG&quot;,
  &quot;12&quot; : &quot;BPER&quot;,
  &quot;13&quot; : &quot;TIM&quot;,
  &quot;14&quot; : &quot;SANTINI&quot;,
  &quot;15&quot; : &quot;CONAD&quot;,
  &quot;16&quot; : &quot;FINITALIA&quot;,
  &quot;17&quot; : &quot;UNIPOLTECH&quot;,
  &quot;18&quot; : &quot;UNA_NAXOS&quot;,
  &quot;19&quot; : &quot;CRIF/CRIBIS&quot;,
  &quot;20&quot; : &quot;PRONTO ASSISTANCE&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem36</globalKey>
        <inputFieldName>Key:AccountKey:MEDAGLIA_VOC:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:VoC</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&apos;-&apos;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem16</globalKey>
        <inputFieldName>Consensi:StatoPrivacy</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ConsensoPrivacy</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem40</globalKey>
        <inputFieldName>Key:AccountKey:RPO</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:RPO</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem53</globalKey>
        <inputFieldName>Value:accountDetail_fea</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:AdesioneFEA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| &quot;SELECT/\/\/Valore__c/\/\/FROM/\/\/AnagMappingCodDesc__mdt/\/\/WHERE/\/\/Field__c/\/\/=/\/\/&apos;AtecoBreve&apos;/\/\/and/\/\//\/\/Codice__c/\/\/=/\/\/&apos;{0}&apos;/\/\/limit/\/\/1&quot; var:Value:accountDetail_CodiceAteco QUERY</formulaConverted>
        <formulaExpression>QUERY( &quot;SELECT Valore__c FROM AnagMappingCodDesc__mdt WHERE Field__c = &apos;AtecoBreve&apos; and  Codice__c = &apos;{0}&apos; limit 1&quot;, %Value:accountDetail_CodiceAteco% )</formulaExpression>
        <formulaResultPath>descrizioneAteco</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>--</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem17</globalKey>
        <inputFieldName>Key:AccountKey:BPER_MARCHIATURASOGGETTO:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>RappBancari:MarchiaturaSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem3</globalKey>
        <inputFieldName>Value:accountAgencyDetail_mobile</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Cellulare_Agency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>--</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem54</globalKey>
        <inputFieldName>Key:AccountKey:BPER_TUTELASALVAGUARDIABPER:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>RappBancari:TutelaBPER</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem2</globalKey>
        <inputFieldName>Value:accountDetail_ProfessionLegalEntityType</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:TipoPersonaGiuridica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem6</globalKey>
        <inputFieldName>Value:accountDetail_ciu</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:Ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>Non effettuato</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem43</globalKey>
        <inputFieldName>Key:AccountKey:TPD_DATAULTIMOACCESSO:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:UltimoAccesso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem11</globalKey>
        <inputFieldName>Value:accountDetail_compagnia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:Compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem48</globalKey>
        <inputFieldName>Value:accountAgencyDetail_email</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Email_Agency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem24</globalKey>
        <inputFieldName>Value:accountDetail_sedeLegale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:SedeLegale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem41</globalKey>
        <inputFieldName>Value:accountDetail_pagamento</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:MetodiPagamento</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem23</globalKey>
        <inputFieldName>descrizioneAteco</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:DescrizioneATECO</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value var:null != var:Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value &apos;Prospect&apos; IF</formulaConverted>
        <formulaExpression>IF(Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value != null, Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value, &apos;Prospect&apos;))</formulaExpression>
        <formulaResultPath>statoSoggetto</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem26</globalKey>
        <inputFieldName>Value:accountDetail_Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>AccountDetailId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem5</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ReferenteAziendale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem25</globalKey>
        <inputFieldName>Value:accountDetail_feaDataInizio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:DataInizio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPGCustom0jI9O000000vADxUAMItem42</globalKey>
        <inputFieldName>Key:AccountKeyAgency:MDM_CONTATTABILITA:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:Contattabilita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;true&quot; : &quot;Si&quot;,
  &quot;false&quot; : &quot;No&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;Key&quot; : {
    &quot;AccountKeyAgency&quot; : {
      &quot;&quot; : &quot;&quot;,
      &quot;PP_AGENZIA_STATO&quot; : null,
      &quot;PP_AGENZIA_CLIENTE_DAL&quot; : null,
      &quot;PP_AGENZIA_SOCIETA_STATO&quot; : null,
      &quot;PP_AGENZIA_SOCIETA_CLIENTE_DAL&quot; : null,
      &quot;PP_CONVENZIONI&quot; : null,
      &quot;CRM_CLIENTE_VIP&quot; : null,
      &quot;MDM_CONTATTABILITA&quot; : null,
      &quot;ConditionalBlock3Status&quot; : true,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;LoopBlockIterationIndex&quot; : 1
    },
    &quot;AccountKey&quot; : {
      &quot;&quot; : &quot;&quot;,
      &quot;CRMA_VALUS&quot; : null,
      &quot;TPD_DATAULTIMOACCESSO&quot; : null,
      &quot;CRMA_CAPACITADISPESA&quot; : null,
      &quot;PP_TITOLARITACLIENTE&quot; : null,
      &quot;MEDAGLIA_VOC&quot; : null,
      &quot;TPD_ACCESSO&quot; : null,
      &quot;ANAG2_STATOCIVILE&quot; : null,
      &quot;ANAG2_FLAGFIGLI&quot; : null,
      &quot;ANAG2_TITOLODISTUDIO&quot; : null,
      &quot;ANAG2_TIPORISPARMIO&quot; : null,
      &quot;ANAG2_FLAGCONIUGECARICO&quot; : null,
      &quot;ANAG2_NUMEROFIGLIOCARICO&quot; : null,
      &quot;ANAG2_REDDITOLAVORO&quot; : null,
      &quot;ANAG2_EVOLUZIONEREDDITOLAVORO&quot; : null,
      &quot;ANAG2_EVOLUZIONEALTRIREDDITI&quot; : null,
      &quot;ANAG2_SPESEINCOMPRIMIBILI&quot; : null,
      &quot;ANAG2_SPESECOMPRIMIBILI&quot; : null,
      &quot;ANAG2_DATANASCITACONIUGE&quot; : null,
      &quot;ANAG2_NUMERONUCLEOFAMIGLIA&quot; : null,
      &quot;ANAG2_FLAGANIMALIDOMESTICI&quot; : null,
      &quot;ANAG2_FLAGCOLLABORATORIDOMESTICI&quot; : null,
      &quot;ANAG2_FLAGCASAPROPRIETA&quot; : null,
      &quot;ANAG2_FLAGCASAAFFITTO&quot; : null,
      &quot;ANAG2_FLAGSECONDACASA&quot; : null,
      &quot;ANAG2_FLAGMUTUO&quot; : null,
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMOANNO&quot; : null,
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMI3ANNI&quot; : null,
      &quot;CRIBIS_NDIPENDENTIPERINQUADRAMENTO&quot; : null,
      &quot;CRIBIS_DETTAGLIOTFR&quot; : null,
      &quot;V_QUEST&quot; : null,
      &quot;CRIBIS&quot; : null,
      &quot;BPER_MARCHIATURASOGGETTO&quot; : null,
      &quot;BPER_TUTELASALVAGUARDIABPER&quot; : null,
      &quot;ASSURBANCA_PROCESSI&quot; : null,
      &quot;BANKASSURANCE_BISOGNI&quot; : null,
      &quot;BANKASSURANCE_PROCESSI&quot; : null,
      &quot;TPD_COMPORTAMENTOOMNICANALE&quot; : null,
      &quot;TPD_CANALEACCESSO&quot; : null,
      &quot;TPD_PRESENZAAPP&quot; : null,
      &quot;ANAG2_DATIATTRIBUTI&quot; : null,
      &quot;ConditionalBlock2Status&quot; : true,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;LoopBlockIterationIndex&quot; : 1
    }
  },
  &quot;Value&quot; : {
    &quot;accountDetail_IsCorporate&quot; : &quot;No&quot;,
    &quot;accountDetail_originator&quot; : &quot;-&quot;,
    &quot;user_name&quot; : &quot;Maria Calvino&quot;,
    &quot;accountDetail_pagamento&quot; : &quot;-&quot;,
    &quot;accountDetail_ProfessionLegalEntityType&quot; : &quot;-&quot;,
    &quot;accountDetailMDM_emailStatus&quot; : &quot;-&quot;,
    &quot;accountAgencyDetail_FeaFlagSubsription&quot; : &quot;-&quot;,
    &quot;agenzia_Id&quot; : &quot;0019X00000sfj5iQAA&quot;,
    &quot;accountDetail_SourceSystemConsentCode&quot; : &quot;Mancante&quot;,
    &quot;accountAgencyDetail_mobile&quot; : &quot;-&quot;,
    &quot;accountDetail_companyType&quot; : &quot;2&quot;,
    &quot;sinistri&quot; : &quot;ND&quot;,
    &quot;accountAccountRelationSociey_Id&quot; : &quot;a009X00000YImtpQAD&quot;,
    &quot;accountDetail_adesioneFea&quot; : &quot;Si&quot;,
    &quot;account_cf&quot; : &quot;***********&quot;,
    &quot;accounDetail_VatNumber&quot; : &quot;-&quot;,
    &quot;contenzioso&quot; : &quot;ND&quot;,
    &quot;accountDetailMDM_Mobile&quot; : &quot;-&quot;,
    &quot;accountDetailMDM_mobileStatus&quot; : &quot;-&quot;,
    &quot;accountDetail_CodiceAteco&quot; : &quot;812202&quot;,
    &quot;user_id&quot; : &quot;0059X00000Jr08cQAB&quot;,
    &quot;compagnia_Id&quot; : &quot;0019X000019VA0lQAG&quot;,
    &quot;accountDetailMDM_Email&quot; : &quot;-&quot;,
    &quot;accountDetail_Id&quot; : &quot;a1i9X000006pueDQAQ&quot;,
    &quot;accountDetail_sedeLegale&quot; : &quot;VIA AMEDEO TONANI 39, CR, CREMONA, 26100&quot;,
    &quot;accountAgencyDetail_email&quot; : &quot;-&quot;
  },
  &quot;Consensi&quot; : {
    &quot;Checkbox&quot; : {
      &quot;error&quot; : &quot;XML or JSON parsing error.&quot;
    },
    &quot;DataInizio&quot; : &quot; - &quot;,
    &quot;TipoConsenso&quot; : &quot;NULL&quot;,
    &quot;StatoPrivacy&quot; : &quot;Mancante&quot;
  }
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>AnagTransformPG_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
