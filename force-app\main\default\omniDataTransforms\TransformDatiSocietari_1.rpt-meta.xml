<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <description>Fabrizio Cesare: add esito crif</description>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;StatusCode&quot; : 200,
  &quot;log&quot; : {
    &quot;data&quot; : null,
    &quot;utente&quot; : null,
    &quot;messaggio&quot; : &quot;ACCESSO CRIF OK&quot;,
    &quot;codice&quot; : &quot;OK&quot;
  },
  &quot;esg&quot; : {
    &quot;scoreEsgWithNotching&quot; : null,
    &quot;scoreEsg&quot; : null,
    &quot;scoreGWithNotching&quot; : null,
    &quot;scoreG&quot; : null,
    &quot;scoreSWithNotching&quot; : null,
    &quot;scoreS&quot; : null,
    &quot;scoreEWithNotching&quot; : null,
    &quot;scoreE&quot; : null
  },
  &quot;eiscrifRecordFound&quot; : true,
  &quot;datiTracciatura&quot; : {
    &quot;proceduraChiamante&quot; : null,
    &quot;applicazioneChiamante&quot; : null,
    &quot;sottosistemaAggiornamento&quot; : null,
    &quot;sistemaAggiornamento&quot; : null,
    &quot;compagniaAggiornamento&quot; : null,
    &quot;canaleAggiornamento&quot; : null,
    &quot;sottosistemaCreazione&quot; : null,
    &quot;sistemaCreazione&quot; : null,
    &quot;compagniaCreazione&quot; : null,
    &quot;canaleCreazione&quot; : null,
    &quot;usernameUltimoAggiornamento&quot; : null,
    &quot;userIdUltimoAggiornamento&quot; : null,
    &quot;dataUltimoAggiornamento&quot; : null,
    &quot;usernameCreazione&quot; : &quot;PROVA TEST&quot;,
    &quot;userIdCreazione&quot; : &quot;UGH0007X&quot;,
    &quot;dataCreazione&quot; : null
  },
  &quot;flagCorporate&quot; : null,
  &quot;flag&quot; : null,
  &quot;esitoCollegamento&quot; : null,
  &quot;dataAggiornamento&quot; : null,
  &quot;attivita&quot; : [ {
    &quot;importanza&quot; : {
      &quot;descrizione&quot; : &quot;Prevalente Svolta Dall&apos;Impresa&quot;,
      &quot;codice&quot; : &quot;I&quot;
    },
    &quot;ateco&quot; : {
      &quot;descrizione&quot; : &quot;Farmacie&quot;,
      &quot;codice&quot; : &quot;47731&quot;
    },
    &quot;tipo&quot; : &quot;P&quot;
  }, {
    &quot;importanza&quot; : {
      &quot;descrizione&quot; : &quot;Primaria Registro Imprese&quot;,
      &quot;codice&quot; : &quot;P&quot;
    },
    &quot;ateco&quot; : {
      &quot;descrizione&quot; : &quot;Commercio Al Dettaglio Effettuato Per Mezzo Di Distributori Automatici&quot;,
      &quot;codice&quot; : &quot;47992&quot;
    },
    &quot;tipo&quot; : &quot;S&quot;
  } ],
  &quot;unitaProduttive&quot; : [ {
    &quot;dataFine&quot; : null,
    &quot;dataInizio&quot; : &quot;2012-10-15&quot;,
    &quot;indirizzi&quot; : [ {
      &quot;stato&quot; : &quot;IT&quot;,
      &quot;provincia&quot; : &quot;MI&quot;,
      &quot;cap&quot; : &quot;&quot;,
      &quot;frazione&quot; : &quot;&quot;,
      &quot;comune&quot; : &quot;ASSAGO&quot;,
      &quot;civico&quot; : &quot;SNC&quot;,
      &quot;via&quot; : &quot;MILANOFIORI&quot;,
      &quot;toponimo&quot; : &quot;VIALE&quot;,
      &quot;tipoIndirizzo&quot; : &quot;M&quot;
    } ],
    &quot;attivita&quot; : [ {
      &quot;importanza&quot; : {
        &quot;descrizione&quot; : &quot;Primaria Registro Imprese&quot;,
        &quot;codice&quot; : &quot;P&quot;
      },
      &quot;ateco&quot; : {
        &quot;descrizione&quot; : &quot;Farmacie&quot;,
        &quot;codice&quot; : &quot;47731&quot;
      },
      &quot;tipo&quot; : &quot;P&quot;
    } ],
    &quot;formato&quot; : [ {
      &quot;sottotipoDesc&quot; : &quot;Farmacia&quot;,
      &quot;tipoDesc&quot; : &quot;FRM&quot;
    } ],
    &quot;statoAttivita&quot; : &quot;A&quot;,
    &quot;progressivo&quot; : &quot;&quot;
  } ],
  &quot;contatti&quot; : null,
  &quot;indirizzi&quot; : [ {
    &quot;stato&quot; : &quot;IT&quot;,
    &quot;provincia&quot; : &quot;MI&quot;,
    &quot;cap&quot; : &quot;&quot;,
    &quot;frazione&quot; : &quot;&quot;,
    &quot;comune&quot; : &quot;ASSAGO&quot;,
    &quot;civico&quot; : &quot;7&quot;,
    &quot;via&quot; : &quot;DEI CADUTI&quot;,
    &quot;toponimo&quot; : &quot;VIA&quot;,
    &quot;tipoIndirizzo&quot; : &quot;H&quot;
  }, {
    &quot;stato&quot; : &quot;IT&quot;,
    &quot;provincia&quot; : &quot;MI&quot;,
    &quot;cap&quot; : &quot;&quot;,
    &quot;frazione&quot; : &quot;&quot;,
    &quot;comune&quot; : &quot;ASSAGO&quot;,
    &quot;civico&quot; : &quot;&quot;,
    &quot;via&quot; : &quot;MILANOFIORI&quot;,
    &quot;toponimo&quot; : &quot;VIALE&quot;,
    &quot;tipoIndirizzo&quot; : &quot;M&quot;
  } ],
  &quot;indirizzoPrincipale&quot; : null,
  &quot;datiEconomici&quot; : {
    &quot;flagOutOfBusiness&quot; : false,
    &quot;addettiIndipendenti&quot; : null,
    &quot;addettiDipendenti&quot; : null,
    &quot;dataRifIndicatori&quot; : null,
    &quot;indiceAbitudPagam&quot; : null,
    &quot;probabRitardoPagam&quot; : 0,
    &quot;probabCessAttivita&quot; : null,
    &quot;indiceAffidComm&quot; : null,
    &quot;salariStipendi&quot; : null,
    &quot;valoreProduzione&quot; : &quot;1547495&quot;,
    &quot;dataIscrizioneCCIAA&quot; : &quot;2012-05-11&quot;,
    &quot;costoPersonalePL&quot; : null,
    &quot;costoPersonaleIC&quot; : &quot;131804&quot;,
    &quot;annoBilancio&quot; : &quot;2023&quot;,
    &quot;fatturato&quot; : &quot;1529426&quot;,
    &quot;dipendenti&quot; : 6
  },
  &quot;datiIdentificativi&quot; : {
    &quot;codiceAttivita&quot; : null,
    &quot;atecoRiqualificato&quot; : null,
    &quot;codiceSintetico&quot; : {
      &quot;descrizione&quot; : &quot;COMMERCIO AL MINUTO&quot;,
      &quot;codice&quot; : &quot;511&quot;
    },
    &quot;tipoAttivita&quot; : {
      &quot;descrizione&quot; : &quot;SCONOSCIUTO&quot;,
      &quot;codice&quot; : &quot;000&quot;
    },
    &quot;atecoBreve&quot; : {
      &quot;descrizione&quot; : &quot;Commercio al dettaglio di articoli di seconda mano in negozi&quot;,
      &quot;codice&quot; : &quot;477&quot;
    },
    &quot;atecoPrimario&quot; : {
      &quot;descrizione&quot; : &quot;Farmacie&quot;,
      &quot;codice&quot; : &quot;47731&quot;
    },
    &quot;flagForzaturaAteco&quot; : false,
    &quot;codGruppoSocietario&quot; : 0,
    &quot;naturaGiuridica&quot; : {
      &quot;descrizione&quot; : &quot;Societa&apos; Di Capitali&quot;,
      &quot;codice&quot; : &quot;SC&quot;
    },
    &quot;sae&quot; : {
      &quot;descrizione&quot; : &quot;IMPRESE CONTROLLATE DA AMMINISTRAZIONI LOCALI&quot;,
      &quot;codice&quot; : &quot;476&quot;
    },
    &quot;rae&quot; : {
      &quot;descrizione&quot; : &quot;C.M. ARTICOLI NON ALTROVE CLASS.&quot;,
      &quot;codice&quot; : &quot;654&quot;
    },
    &quot;fineAttivita&quot; : null,
    &quot;inizioAttivita&quot; : &quot;2012-10-15&quot;,
    &quot;codStatoAttivita&quot; : &quot;A&quot;,
    &quot;formaGiuridica&quot; : &quot;SR&quot;,
    &quot;ragioneSociale&quot; : &quot;FARMACIA ASSAGO MILANOFIORI SRL&quot;,
    &quot;codCciaa&quot; : null,
    &quot;codRea&quot; : &quot;1985816&quot;,
    &quot;codCrif&quot; : &quot;IT8AE60102S0001&quot;,
    &quot;partitaIva&quot; : &quot;07850040960&quot;,
    &quot;codiceFiscale&quot; : &quot;07850040960&quot;
  },
  &quot;id&quot; : 516
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>TransformDatiSocietari</name>
    <nullInputsIncludedInOutput>true</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom1784</globalKey>
        <inputFieldName>indirizzi:stato</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>stato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem27</globalKey>
        <inputFieldName>datiEconomici:dipendenti</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>dipendenti</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom4809</globalKey>
        <inputFieldName>indirizzi:toponimo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>toponimo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem26</globalKey>
        <inputFieldName>datiEconomici:fatturato</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Integer</outputFieldFormat>
        <outputFieldName>fatturato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem25</globalKey>
        <inputFieldName>datiIdentificativi:formaGiuridica</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>formaGiuridica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom6357</globalKey>
        <inputFieldName>datiIdentificativi:flagForzaturaAteco</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>flagForzaturaAteco</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem30</globalKey>
        <inputFieldName>datiIdentificativi:sae:codice</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codiceSae</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem29</globalKey>
        <inputFieldName>datiIdentificativi:codCciaa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codCciaa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem28</globalKey>
        <inputFieldName>datiIdentificativi:codCrif</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codCrif</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem31</globalKey>
        <inputFieldName>eiscrifRecordFound</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>esitoCrif</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom8527</globalKey>
        <inputFieldName>indirizzi:comune</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>comune</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom2532</globalKey>
        <inputFieldName>indirizzi:cap</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>cap</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:indirizzoPrincipale var:null != var:indirizzoPrincipale:toponimo &quot;/\/\/&quot; + var:indirizzoPrincipale:via + &quot;&quot; IF</formulaConverted>
        <formulaExpression>IF(indirizzoPrincipale != null, indirizzoPrincipale:toponimo +&quot; &quot;+indirizzoPrincipale:via, &quot;&quot;)</formulaExpression>
        <formulaResultPath>IndirizzoPrincipale</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom3608</globalKey>
        <inputFieldName>indirizzi:civico</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>civico</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem4</globalKey>
        <inputFieldName>datiEconomici:dataIscrizioneCCIAA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>dataIscrizioneCCIAA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem3</globalKey>
        <inputFieldName>datiIdentificativi:fineAttivita</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>dataFineAttivita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem2</globalKey>
        <inputFieldName>datiIdentificativi:naturaGiuridica:codice</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codiceNaturaGiuridica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem1</globalKey>
        <inputFieldName>datiIdentificativi:atecoPrimario:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>descrizioneAtecoPrimario</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem14</globalKey>
        <inputFieldName>datiEconomici:flagOutOfBusiness</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>flagOutOfBusiness</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;false&quot; : &quot;No&quot;,
  &quot;true&quot; : &quot;Si&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem13</globalKey>
        <inputFieldName>datiIdentificativi:codiceFiscale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem12</globalKey>
        <inputFieldName>datiIdentificativi:rae:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>descrizioneRae</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem11</globalKey>
        <inputFieldName>datiIdentificativi:atecoRiqualificato:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>descrizioneAtecoRiqualificato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem17</globalKey>
        <inputFieldName>datiIdentificativi:codStatoAttivita</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codStatoAttivita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem16</globalKey>
        <inputFieldName>datiIdentificativi:inizioAttivita</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>dataInizioAttivita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem15</globalKey>
        <inputFieldName>datiEconomici:addettiIndipendenti</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>addettiIndipendenti</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom702</globalKey>
        <inputFieldName>esitoCollegamento</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>esitoCollegamento</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem7</globalKey>
        <inputFieldName>datiIdentificativi:rae:codice</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codiceRae</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem6</globalKey>
        <inputFieldName>datiIdentificativi:atecoPrimario:codice</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codiceAtecoPrimario</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem5</globalKey>
        <inputFieldName>datiIdentificativi:sae:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>descrizioneSAE</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom4811</globalKey>
        <inputFieldName>datiTracciatura:dataUltimoAggiornamento</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>dataUltimoAggiornamento</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom6356</globalKey>
        <inputFieldName>indirizzi:tipoIndirizzo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>tipoIndirizzo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem10</globalKey>
        <inputFieldName>datiIdentificativi:ragioneSociale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>ragioneSociale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem9</globalKey>
        <inputFieldName>datiIdentificativi:codiceSintetico:codice</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codiceSintetico</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem8</globalKey>
        <inputFieldName>datiIdentificativi:fineAttivita</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>fineAttivita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem21</globalKey>
        <inputFieldName>datiIdentificativi:naturaGiuridica:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>descrizioneNaturaGiuridica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem20</globalKey>
        <inputFieldName>datiEconomici:addettiDipendenti</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>addettiDipendenti</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem24</globalKey>
        <inputFieldName>datiEconomici:annoBilancio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>annoBilancio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem23</globalKey>
        <inputFieldName>contatti:pec</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>pec</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem22</globalKey>
        <inputFieldName>datiIdentificativi:partitaIva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>partitaIva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom6728</globalKey>
        <inputFieldName>indirizzi:via</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>via</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem19</globalKey>
        <inputFieldName>IndirizzoPrincipale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>indirizzoPrincipale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom521</globalKey>
        <inputFieldName>indirizzi:frazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>frazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom0jI9V000000ws9hUAAItem18</globalKey>
        <inputFieldName>datiIdentificativi:codCrif</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codiceCrif</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformDatiSocietariCustom9854</globalKey>
        <inputFieldName>indirizzi:provincia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformDatiSocietari</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>provincia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;StatusCode&quot; : 200,
  &quot;log&quot; : {
    &quot;data&quot; : null,
    &quot;utente&quot; : null,
    &quot;messaggio&quot; : &quot;ACCESSO CRIF OK&quot;,
    &quot;codice&quot; : &quot;OK&quot;
  },
  &quot;esg&quot; : {
    &quot;scoreEsgWithNotching&quot; : null,
    &quot;scoreEsg&quot; : null,
    &quot;scoreGWithNotching&quot; : null,
    &quot;scoreG&quot; : null,
    &quot;scoreSWithNotching&quot; : null,
    &quot;scoreS&quot; : null,
    &quot;scoreEWithNotching&quot; : null,
    &quot;scoreE&quot; : null
  },
  &quot;eiscrifRecordFound&quot; : true,
  &quot;datiTracciatura&quot; : {
    &quot;proceduraChiamante&quot; : null,
    &quot;applicazioneChiamante&quot; : null,
    &quot;sottosistemaAggiornamento&quot; : null,
    &quot;sistemaAggiornamento&quot; : null,
    &quot;compagniaAggiornamento&quot; : null,
    &quot;canaleAggiornamento&quot; : null,
    &quot;sottosistemaCreazione&quot; : null,
    &quot;sistemaCreazione&quot; : null,
    &quot;compagniaCreazione&quot; : null,
    &quot;canaleCreazione&quot; : null,
    &quot;usernameUltimoAggiornamento&quot; : null,
    &quot;userIdUltimoAggiornamento&quot; : null,
    &quot;dataUltimoAggiornamento&quot; : null,
    &quot;usernameCreazione&quot; : &quot;PROVA TEST&quot;,
    &quot;userIdCreazione&quot; : &quot;UGH0007X&quot;,
    &quot;dataCreazione&quot; : null
  },
  &quot;flagCorporate&quot; : null,
  &quot;flag&quot; : null,
  &quot;esitoCollegamento&quot; : null,
  &quot;dataAggiornamento&quot; : null,
  &quot;attivita&quot; : [ {
    &quot;importanza&quot; : {
      &quot;descrizione&quot; : &quot;Prevalente Svolta Dall&apos;Impresa&quot;,
      &quot;codice&quot; : &quot;I&quot;
    },
    &quot;ateco&quot; : {
      &quot;descrizione&quot; : &quot;Farmacie&quot;,
      &quot;codice&quot; : &quot;47731&quot;
    },
    &quot;tipo&quot; : &quot;P&quot;
  }, {
    &quot;importanza&quot; : {
      &quot;descrizione&quot; : &quot;Primaria Registro Imprese&quot;,
      &quot;codice&quot; : &quot;P&quot;
    },
    &quot;ateco&quot; : {
      &quot;descrizione&quot; : &quot;Commercio Al Dettaglio Effettuato Per Mezzo Di Distributori Automatici&quot;,
      &quot;codice&quot; : &quot;47992&quot;
    },
    &quot;tipo&quot; : &quot;S&quot;
  } ],
  &quot;unitaProduttive&quot; : [ {
    &quot;dataFine&quot; : null,
    &quot;dataInizio&quot; : &quot;2012-10-15&quot;,
    &quot;indirizzi&quot; : [ {
      &quot;stato&quot; : &quot;IT&quot;,
      &quot;provincia&quot; : &quot;MI&quot;,
      &quot;cap&quot; : &quot;&quot;,
      &quot;frazione&quot; : &quot;&quot;,
      &quot;comune&quot; : &quot;ASSAGO&quot;,
      &quot;civico&quot; : &quot;SNC&quot;,
      &quot;via&quot; : &quot;MILANOFIORI&quot;,
      &quot;toponimo&quot; : &quot;VIALE&quot;,
      &quot;tipoIndirizzo&quot; : &quot;M&quot;
    } ],
    &quot;attivita&quot; : [ {
      &quot;importanza&quot; : {
        &quot;descrizione&quot; : &quot;Primaria Registro Imprese&quot;,
        &quot;codice&quot; : &quot;P&quot;
      },
      &quot;ateco&quot; : {
        &quot;descrizione&quot; : &quot;Farmacie&quot;,
        &quot;codice&quot; : &quot;47731&quot;
      },
      &quot;tipo&quot; : &quot;P&quot;
    } ],
    &quot;formato&quot; : [ {
      &quot;sottotipoDesc&quot; : &quot;Farmacia&quot;,
      &quot;tipoDesc&quot; : &quot;FRM&quot;
    } ],
    &quot;statoAttivita&quot; : &quot;A&quot;,
    &quot;progressivo&quot; : &quot;&quot;
  } ],
  &quot;contatti&quot; : null,
  &quot;indirizzi&quot; : [ {
    &quot;stato&quot; : &quot;IT&quot;,
    &quot;provincia&quot; : &quot;MI&quot;,
    &quot;cap&quot; : &quot;&quot;,
    &quot;frazione&quot; : &quot;&quot;,
    &quot;comune&quot; : &quot;ASSAGO&quot;,
    &quot;civico&quot; : &quot;7&quot;,
    &quot;via&quot; : &quot;DEI CADUTI&quot;,
    &quot;toponimo&quot; : &quot;VIA&quot;,
    &quot;tipoIndirizzo&quot; : &quot;H&quot;
  }, {
    &quot;stato&quot; : &quot;IT&quot;,
    &quot;provincia&quot; : &quot;MI&quot;,
    &quot;cap&quot; : &quot;&quot;,
    &quot;frazione&quot; : &quot;&quot;,
    &quot;comune&quot; : &quot;ASSAGO&quot;,
    &quot;civico&quot; : &quot;&quot;,
    &quot;via&quot; : &quot;MILANOFIORI&quot;,
    &quot;toponimo&quot; : &quot;VIALE&quot;,
    &quot;tipoIndirizzo&quot; : &quot;M&quot;
  } ],
  &quot;indirizzoPrincipale&quot; : null,
  &quot;datiEconomici&quot; : {
    &quot;flagOutOfBusiness&quot; : false,
    &quot;addettiIndipendenti&quot; : null,
    &quot;addettiDipendenti&quot; : null,
    &quot;dataRifIndicatori&quot; : null,
    &quot;indiceAbitudPagam&quot; : null,
    &quot;probabRitardoPagam&quot; : 0,
    &quot;probabCessAttivita&quot; : null,
    &quot;indiceAffidComm&quot; : null,
    &quot;salariStipendi&quot; : null,
    &quot;valoreProduzione&quot; : &quot;1547495&quot;,
    &quot;dataIscrizioneCCIAA&quot; : &quot;2012-05-11&quot;,
    &quot;costoPersonalePL&quot; : null,
    &quot;costoPersonaleIC&quot; : &quot;131804&quot;,
    &quot;annoBilancio&quot; : &quot;2023&quot;,
    &quot;fatturato&quot; : &quot;1529426&quot;,
    &quot;dipendenti&quot; : 6
  },
  &quot;datiIdentificativi&quot; : {
    &quot;codiceAttivita&quot; : null,
    &quot;atecoRiqualificato&quot; : null,
    &quot;codiceSintetico&quot; : {
      &quot;descrizione&quot; : &quot;COMMERCIO AL MINUTO&quot;,
      &quot;codice&quot; : &quot;511&quot;
    },
    &quot;tipoAttivita&quot; : {
      &quot;descrizione&quot; : &quot;SCONOSCIUTO&quot;,
      &quot;codice&quot; : &quot;000&quot;
    },
    &quot;atecoBreve&quot; : {
      &quot;descrizione&quot; : &quot;Commercio al dettaglio di articoli di seconda mano in negozi&quot;,
      &quot;codice&quot; : &quot;477&quot;
    },
    &quot;atecoPrimario&quot; : {
      &quot;descrizione&quot; : &quot;Farmacie&quot;,
      &quot;codice&quot; : &quot;47731&quot;
    },
    &quot;flagForzaturaAteco&quot; : false,
    &quot;codGruppoSocietario&quot; : 0,
    &quot;naturaGiuridica&quot; : {
      &quot;descrizione&quot; : &quot;Societa&apos; Di Capitali&quot;,
      &quot;codice&quot; : &quot;SC&quot;
    },
    &quot;sae&quot; : {
      &quot;descrizione&quot; : &quot;IMPRESE CONTROLLATE DA AMMINISTRAZIONI LOCALI&quot;,
      &quot;codice&quot; : &quot;476&quot;
    },
    &quot;rae&quot; : {
      &quot;descrizione&quot; : &quot;C.M. ARTICOLI NON ALTROVE CLASS.&quot;,
      &quot;codice&quot; : &quot;654&quot;
    },
    &quot;fineAttivita&quot; : null,
    &quot;inizioAttivita&quot; : &quot;2012-10-15&quot;,
    &quot;codStatoAttivita&quot; : &quot;A&quot;,
    &quot;formaGiuridica&quot; : &quot;SR&quot;,
    &quot;ragioneSociale&quot; : &quot;FARMACIA ASSAGO MILANOFIORI SRL&quot;,
    &quot;codCciaa&quot; : null,
    &quot;codRea&quot; : &quot;1985816&quot;,
    &quot;codCrif&quot; : &quot;IT8AE60102S0001&quot;,
    &quot;partitaIva&quot; : &quot;07850040960&quot;,
    &quot;codiceFiscale&quot; : &quot;07850040960&quot;
  },
  &quot;id&quot; : 516
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>TransformDatiSocietari_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
