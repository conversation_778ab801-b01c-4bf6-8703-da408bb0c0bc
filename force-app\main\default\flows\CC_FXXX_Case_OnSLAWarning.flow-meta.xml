<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Custom_Notification</name>
        <label>Send Custom Notification</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <connector>
            <targetReference>Update_Owner</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>Get_Notification.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>notificationRecipients</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>NotificationTitle</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>BodyNotification</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <assignments>
        <name>Add_User_Id</name>
        <label>Add User Id</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>userIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop.UserOrGroupId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Recipient</name>
        <label>Assign Recipient</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>notificationRecipients</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_Supervisor.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Send_Custom_Notification</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Found_group_member</name>
        <label>Found group member?</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Group_Member</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>CC-FXXX_Case_OnSLAWarning {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC-FXXX_Case_OnSLAWarning</label>
    <loops>
        <name>Loop</name>
        <label>Loop</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>Get_Group_Member</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_User_Id</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Role</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Group_Member</name>
        <label>Get Group Member</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Found_group_member</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>GroupId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Queue.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>GroupMember</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Notification</name>
        <label>Get Notification</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Recipient</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>CC_Case_Avviso_scadenza_SLA</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Queue</name>
        <label>Get Queue</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Group_Member</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.CC_QueuePriority__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Role</name>
        <label>Get Role</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Supervisor</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Supervisor_CC</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>UserRole</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Supervisor</name>
        <label>Get Supervisor</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Notification</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>userIds</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Role.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Owner</name>
        <label>Update Owner</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_Queue.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Queue</targetReference>
        </connector>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SLA_Expiration_Notice__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Case</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
    <textTemplates>
        <name>BodyNotification</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>L&apos;attività {!$Record.CaseNumber}, assegnata all&apos;operatore {!$Record.Owner:User.FirstName} {!$Record.Owner:User.LastName}, è in scadenza il giorno {!$Record.Expiration_Date__c}. 
Invio notifica: {!$Flow.CurrentDateTime}.</text>
    </textTemplates>
    <textTemplates>
        <name>NotificationTitle</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Scadenza Attività</text>
    </textTemplates>
    <variables>
        <name>notificationRecipients</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>userIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
