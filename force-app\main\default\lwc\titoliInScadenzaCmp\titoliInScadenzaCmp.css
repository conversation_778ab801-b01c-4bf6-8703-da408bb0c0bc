.radioButt{
    font-weight: bold;
}

.container{
    min-width: 35rem;
    height: fit-content;
    background-color: #eef0f2;
    width: fit-content;
}

.sect{
    align-items: center;
    display: flex;
}

.titolo{
    font-weight: 500;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}
.titoloContainer{
    margin-bottom: 1rem;
    border-bottom: 2px solid #e5e5e5;
}

.buttonStyle{
    box-shadow: 1px 4px 9px #888888;
    background-color: white;
    border: none;
    border-radius: 4px;
    text-transform: uppercase;
    min-width: 11rem;
    padding: 11px 32px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
}

.annullaButton{
    color: #d60303;
}

.confermaButton:disabled{
    color: rgb(142, 142, 142);
    cursor: default;
}

input[type='radio'] {
    -ms-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -webkit-transform: scale(1.5);
    -o-transform: scale(1.5);
    transform: scale(1.5); 
}

.select{
    border: 1px solid rgb(210 210 210);
    box-shadow: 0px -1px 2px #a09d9d;
    width: 100%;
    height: 2rem;
}

.posta-tab-container {
    margin-bottom: 5rem;
}

.corsivo{
    font-style: italic;
}

.textColor{
    color: #1D314B;
}

select::picker-icon {
    color: #d41212;
    transition: 0.4s rotate;
}

input[type=radio]:checked {
    accent-color: #0176D3;
}

.picklist-container{
    margin-top: 15px;
}