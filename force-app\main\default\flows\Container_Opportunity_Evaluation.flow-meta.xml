<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Clean_Container_Areas_of_Need</name>
        <label>Clean Container Areas of Need</label>
        <locationX>314</locationX>
        <locationY>2774</locationY>
        <actionName>SanitizeMultiPicklistField</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Clean_Container_Overall_Areas_of_Need</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>input</name>
            <value>
                <elementReference>allAreasOfNeed</elementReference>
            </value>
        </inputParameters>
        <nameSegment>SanitizeMultiPicklistField</nameSegment>
        <offset>0</offset>
        <outputParameters>
            <assignToReference>cleanAreasOfNeed</assignToReference>
            <name>output</name>
        </outputParameters>
    </actionCalls>
    <actionCalls>
        <name>Clean_Container_Overall_Areas_of_Need</name>
        <label>Clean Container Overall Areas of Need</label>
        <locationX>314</locationX>
        <locationY>2882</locationY>
        <actionName>SanitizeMultiPicklistField</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Assign_Areas_of_Need</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>input</name>
            <value>
                <elementReference>allOverallAreasOfNeed</elementReference>
            </value>
        </inputParameters>
        <nameSegment>SanitizeMultiPicklistField</nameSegment>
        <offset>0</offset>
        <outputParameters>
            <assignToReference>cleanOverallAreasOfNeed</assignToReference>
            <name>output</name>
        </outputParameters>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <assignments>
        <name>Assign_Areas_of_Need</name>
        <label>Assign Areas of Need</label>
        <locationX>314</locationX>
        <locationY>2990</locationY>
        <assignmentItems>
            <assignToReference>Get_Container_Record.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>cleanAreasOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Container_Record.OverallAreasOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>cleanOverallAreasOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Container_Record.Amount</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>totalAmount</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>check_Owner_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_New_Maximum</name>
        <label>Assign New Maximum</label>
        <locationX>402</locationX>
        <locationY>1574</locationY>
        <assignmentItems>
            <assignToReference>maxStageIndex</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>stageIndexFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>check_owner</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_New_Minimum</name>
        <label>Assign New Minimum</label>
        <locationX>402</locationX>
        <locationY>1274</locationY>
        <assignmentItems>
            <assignToReference>minStageIndex</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>stageIndexFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Max_Stage</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Output</name>
        <label>Assign Output</label>
        <locationX>314</locationX>
        <locationY>3698</locationY>
        <assignmentItems>
            <assignToReference>reEvaluatedContainer</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Container_Record</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>Assign_Owner</name>
        <label>Assign Owner</label>
        <locationX>402</locationX>
        <locationY>1874</locationY>
        <assignmentItems>
            <assignToReference>ownerVar</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Products.OwnerId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Products</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Owner_Container</name>
        <label>Assign Owner Container</label>
        <locationX>182</locationX>
        <locationY>3206</locationY>
        <assignmentItems>
            <assignToReference>Get_Container_Record.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ownerVar</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_if_Update_Requested</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assignment_Products_To_Bundle</name>
        <label>Assignment Products To Bundle</label>
        <locationX>314</locationX>
        <locationY>2258</locationY>
        <assignmentItems>
            <assignToReference>Get_Container_Record.Products__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productsVar</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Target_Stage</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Evaluate_Aggregates</name>
        <label>Evaluate Aggregates</label>
        <locationX>534</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>totalAmount</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Products.Amount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>allAreasOfNeed</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Products.AreaOfNeed__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>allAreasOfNeed</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>;</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>allOverallAreasOfNeed</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Products.OverallAreasOfNeed__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>allOverallAreasOfNeed</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>;</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Take_In_Charge_Expiry</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Container_Stage</name>
        <label>Update Container Stage</label>
        <locationX>314</locationX>
        <locationY>2666</locationY>
        <assignmentItems>
            <assignToReference>Get_Container_Record.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>indexStageFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Clean_Container_Areas_of_Need</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Container_TIC</name>
        <label>Update Container TIC</label>
        <locationX>402</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>Get_Container_Record.TakenInChargeSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Products.TakenInChargeSLAExpiryDate__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Working_Expiry</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Container_Working</name>
        <label>Update Container Working</label>
        <locationX>402</locationX>
        <locationY>974</locationY>
        <assignmentItems>
            <assignToReference>Get_Container_Record.WorkingSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Products.WorkingSLAExpiryDate__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Min_Stage</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_to_Current_Stage</name>
        <label>Update to Current Stage</label>
        <locationX>50</locationX>
        <locationY>2474</locationY>
        <assignmentItems>
            <assignToReference>targetStage</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>minStageIndex</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Container_Stage</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_to_Minimum_Stage</name>
        <label>Update to Minimum Stage</label>
        <locationX>578</locationX>
        <locationY>2474</locationY>
        <assignmentItems>
            <assignToReference>targetStage</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>maxStageIndex</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Container_Stage</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_to_Working_Stage</name>
        <label>Update to Working Stage</label>
        <locationX>314</locationX>
        <locationY>2474</locationY>
        <assignmentItems>
            <assignToReference>targetStage</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>minStageIndex</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Container_Stage</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_if_Update_Requested</name>
        <label>Check if Update Requested</label>
        <locationX>314</locationX>
        <locationY>3398</locationY>
        <defaultConnector>
            <targetReference>Assign_Output</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Requested</defaultConnectorLabel>
        <rules>
            <name>Update_Requested</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>updateRequested</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Container</targetReference>
            </connector>
            <label>Update Requested</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Max_Stage</name>
        <label>Check Max Stage</label>
        <locationX>534</locationX>
        <locationY>1466</locationY>
        <defaultConnector>
            <targetReference>check_owner</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Continue</defaultConnectorLabel>
        <rules>
            <name>New_Maximum</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>stageIndexFormula</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>maxStageIndex</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_New_Maximum</targetReference>
            </connector>
            <label>New Maximum</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Min_Stage</name>
        <label>Check Min Stage</label>
        <locationX>534</locationX>
        <locationY>1166</locationY>
        <defaultConnector>
            <targetReference>Check_Max_Stage</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Continue</defaultConnectorLabel>
        <rules>
            <name>New_Minimum</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>stageIndexFormula</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>minStageIndex</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_New_Minimum</targetReference>
            </connector>
            <label>New Minimum</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_owner</name>
        <label>check owner</label>
        <locationX>534</locationX>
        <locationY>1766</locationY>
        <defaultConnector>
            <targetReference>Loop_Products</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>valid_Owner</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Products.OwnerId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_Products.OwnerId</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>ownerVar</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Owner</targetReference>
            </connector>
            <label>valid Owner</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_Owner_Container</name>
        <label>check Owner Container</label>
        <locationX>314</locationX>
        <locationY>3098</locationY>
        <defaultConnector>
            <targetReference>Check_if_Update_Requested</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>update_value</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Container_Record.OwnerId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>ownerVar</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Owner_Container</targetReference>
            </connector>
            <label>update value</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Take_In_Charge_Expiry</name>
        <label>Check Take In Charge Expiry</label>
        <locationX>534</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <targetReference>Check_Working_Expiry</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Do Not Update</defaultConnectorLabel>
        <rules>
            <name>Update_TIC</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_Container_Record.TakenInChargeSLAExpiryDate__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Container_Record.TakenInChargeSLAExpiryDate__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>Loop_Products.TakenInChargeSLAExpiryDate__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Container_TIC</targetReference>
            </connector>
            <label>Update TIC</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Target_Stage</name>
        <label>Check Target Stage</label>
        <locationX>314</locationX>
        <locationY>2366</locationY>
        <defaultConnector>
            <targetReference>Update_to_Minimum_Stage</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Other</defaultConnectorLabel>
        <rules>
            <name>Uniform_Value</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>minStageIndex</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>maxStageIndex</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_to_Current_Stage</targetReference>
            </connector>
            <label>Uniform Value</label>
        </rules>
        <rules>
            <name>Working</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>minStageIndex</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>2.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_to_Working_Stage</targetReference>
            </connector>
            <label>Working</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Working_Expiry</name>
        <label>Check Working Expiry</label>
        <locationX>534</locationX>
        <locationY>866</locationY>
        <defaultConnector>
            <targetReference>Check_Min_Stage</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Do Not Update</defaultConnectorLabel>
        <rules>
            <name>Update_Working</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Container_Record.WorkingSLAExpiryDate__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Container_Record.WorkingSLAExpiryDate__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>Loop_Products.WorkingSLAExpiryDate__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Container_Working</targetReference>
            </connector>
            <label>Update Working</label>
        </rules>
    </decisions>
    <description>vers prec v3 
aggiunta logica del campo products su bundle</description>
    <environments>Default</environments>
    <formulas>
        <name>indexStageFormula</name>
        <dataType>String</dataType>
        <expression>CASE({!targetStage},
          0, &apos;Nuovo&apos;, 
          1, &apos;Assegnato&apos;, 
          2, &apos;In gestione&apos;,
          &apos;Chiuso&apos;)</expression>
    </formulas>
    <formulas>
        <name>stageIndexFormula</name>
        <dataType>Number</dataType>
        <expression>CASE(TEXT({!Loop_Products.StageName}),
          &apos;Nuovo&apos;, 0,
          &apos;Assegnato&apos;, 1,
          &apos;In gestione&apos;, 2,
          3)</expression>
        <scale>2</scale>
    </formulas>
    <interviewLabel>Container Opportunity Evaluation {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Container Opportunity Evaluation</label>
    <loops>
        <name>Loop_Products</name>
        <label>Loop Products</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <collectionReference>Get_Product_Records</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Evaluate_Aggregates</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Evaluate_Bundle_Products</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Container_Record</name>
        <label>Get Container Record</label>
        <locationX>314</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Product_Records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Product_Records</name>
        <label>Get Product Records</label>
        <locationX>314</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Products</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Parent__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Container</name>
        <label>Update Container</label>
        <locationX>182</locationX>
        <locationY>3506</locationY>
        <connector>
            <targetReference>Assign_Output</targetReference>
        </connector>
        <inputReference>Get_Container_Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Container_Record</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Evaluate_Bundle_Products</name>
        <label>Evaluate Bundle Products</label>
        <locationX>314</locationX>
        <locationY>2150</locationY>
        <connector>
            <targetReference>Assignment_Products_To_Bundle</targetReference>
        </connector>
        <flowName>Evaluate_Bundle_Products</flowName>
        <inputAssignments>
            <name>BundleOpportunity</name>
            <value>
                <elementReference>Get_Container_Record</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>ProductsList</name>
            <value>
                <elementReference>Get_Product_Records</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>productsVar</assignToReference>
            <name>ProductsOutput</name>
        </outputAssignments>
    </subflows>
    <variables>
        <name>allAreasOfNeed</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>allOverallAreasOfNeed</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>cleanAreasOfNeed</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>cleanOverallAreasOfNeed</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>maxStageIndex</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>-1.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>minStageIndex</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>5.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>ownerVar</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>productsVar</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>reEvaluatedContainer</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>targetStage</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>totalAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>updateRequested</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
</Flow>
