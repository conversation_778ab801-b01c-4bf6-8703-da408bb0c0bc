/**
 * @File Name         : urcs_DC_GetInterventi_Test.cls
 * @Description       : Test class per urcs_DC_GetInterventi
 * <AUTHOR> VE
 * @Group             :
 * @Last Modified On  : 25-09-2025
 * @Last Modified By  : VE
**/
@isTest
public class urcs_DC_GetInterventi_Test {

    @TestSetup
    static void makeData(){
        Asset testAsset = new Asset();
        testAsset.Name = 'Test Vehicle Asset';
        testAsset.idAutoLeo__c = '12345';
        testAsset.ExternalId__c = 'UR_V_12345';
        insert testAsset;
    }

    @isTest
    static void testCallMethod() {
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };

        urcs_DC_GetInterventi controller = new urcs_DC_GetInterventi();

        Test.startTest();
        Object result = controller.call('testAction', args);
        Test.stopTest();
        
        // Verifica che non ci siano errori
        System.assertEquals(null, result);
    }

    @isTest
    static void testInvokeMethod() {
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetInterventi.invokeMethod(input, output, options);
        Test.stopTest();
        
        // Verifica che il risultato non sia null
        System.assertNotEquals(null, result);
    }

    @isTest
    static void testGetInterventi() {
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetInterventi.getInterventi(input, output, options);
            System.assertNotEquals(null, result);
        }catch(Exception ex){
            // Gestione eccezione per test
            System.debug('Exception in test: ' + ex.getMessage());
        }
        Test.stopTest();
    }

    @isTest
    static void testGetInterventiWithNullRecordId() {
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => null
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetInterventi.getInterventi(input, output, options);
            System.assertNotEquals(null, result);
        }catch(Exception ex){
            // Gestione eccezione per test
            System.debug('Exception in test: ' + ex.getMessage());
        }
        Test.stopTest();
    }

    @isTest
    static void testInvokeMethodException() {
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => 'invalid',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetInterventi.invokeMethod(input, output, options);
            // Verifica che sia presente un errore nell'output
            System.assert(result.containsKey('error') || result.containsKey('data'));
        }catch(Exception ex){
            // Gestione eccezione per test
            System.debug('Exception in test: ' + ex.getMessage());
        }
        Test.stopTest();
    }

    @isTest
    static void testGetInterventiWithInvalidMethod() {
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'invalidMethod'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetInterventi.invokeMethod(input, output, options);
        Test.stopTest();
        
        // Verifica che il risultato non sia null
        System.assertNotEquals(null, result);
    }
}
