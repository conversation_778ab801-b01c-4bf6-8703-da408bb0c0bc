<?xml version="1.0" encoding="UTF-8"?>
<ReportType xmlns="http://soap.sforce.com/2006/04/metadata">
    <baseObject>Case</baseObject>
    <category>other</category>
    <deployed>true</deployed>
    <description>Mostra le attività di contatto</description>
    <label>Attività di Contatto</label>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>CaseNumber</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contact</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Account</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Asset</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Product</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Entitlement</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BusinessHours</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Parent</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SuppliedName</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SuppliedEmail</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SuppliedPhone</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SuppliedCompany</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Type</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RecordType</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Status</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Reason</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Language</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Subject</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Priority</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Description</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IsClosed</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ClosedDate</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IsEscalated</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Owner</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IsClosedOnCreate</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SlaStartDate</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SlaExitDate</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IsStopped</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>StopStartDate</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ServiceContract</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__FinancialAccount__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>FinServ__Household__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Agency__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AssignedGroup__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AssignedTo__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IsSetRef__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Opportunity__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AreaOfNeed__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Area__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Esito__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Outcome__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Activity_Code__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CCEngaged__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DueDate__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Incident__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insurance_Policy__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Requestfromclient__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Activity__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Detail__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sottoesito__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SubOutcome__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Assignee__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Created_Date__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Name_Activity__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Subject_Name__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ClaimAgency__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ClaimFinancialYear__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ClaimNumber__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ClaimUrl__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ClosedDate__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ContactRequestDate__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ContactRequestStep__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ContactRequestTimeSlot__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrAGSIN__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrAdminContractId__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrAgenziaFiglia__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrAgenziaMadre__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrAnnotazione__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrAssegnatarioDesc__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrAssegnatario__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrAssegnazione__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrAvvioDt__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCampAssocSourceTpCd__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCanale__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCategoriaProdotto__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCfPivaCliente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCodCanaleContatto__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCodStatoOpp__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCodiceEssig__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCodice__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCognomeCliente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCompagnia__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrContId__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCreatoDaNominativo__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCreatoDaTipo__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCreatoDaUtente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrCrmaTrackingId__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrDataSospRiattPolizza__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrDataUltimoInt__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrDeltaPremio__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrDesCanaleContatto__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrDescEsito__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrDescEstesa__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrDescProdotto__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrDominio__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrESSIN__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrEmailCliente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrEscalation__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrEsito__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrEventId__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrFasciaOrarioRicontatto__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrIdAppComm__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrIdAttivitaLeonardo__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrIdCampagnaAssociata__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrIdDocumenti__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrIdFolderPU__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrIdPolizza__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrIdProcesso__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrMotivazioneRichiestaRiscatto__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrNMSIN__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrNomeCliente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrNotaContatto__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrNumeroRicontatto__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrOffsetDays__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrPosizioniPU__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrPrivacy__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrRamoPolizza__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrRegVerId__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrScadenzaDt__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrSistemaId__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrSottoesito__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrStepRichiestaPU__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrTelefonoCliente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrTipoCarico__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrTipoOpportunita__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrTipoTarget__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrTipologiaCampagnia__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrTipologiaCliente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ExternalId__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LeoActivityCode__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Nature__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SourceSystemId__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Source__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>StartDate__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_AssignmentRules__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_IsPlannable__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_LeoHeat__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_LeoPriority__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_NeedsCloseCallout__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_PossibleAssignemnt__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_RequiredIncident__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_RequiredPolicy__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_ShowCloseManual__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_ShowOutcome__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_getInTouch__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AssignedToFormula__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CommercialAreasOfNeed__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrAttSTTPCD__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrContextEntity__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrDataFineSospPolizza__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrDataProrogaSospensione__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrDataRiattAntic__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrExtContrId__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrFlagAnnotazione__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrGestioneMC__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrIdVoucher__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrLabelMotivazione__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrMomentoMc__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrNrEvento__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrNrOfferta__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrProdotto__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrRiattPolizza__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DrSistemaUltimaModifica__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>isCCEnabled__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>UserIsAssegnee__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BusinessKey__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_Hide_DrDataRiattAntic__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_Hide_DrDataSospRiattPolizza__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_Hide_DrRiattPolizza__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_Hide_CommercialAreasOfNeed__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TECH_Hide_DrDataProrogaSospensione__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Agente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Agenzia__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CodAgente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CodAgenzia__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ContractAsset__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>DriverId__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EmailAgente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EmailChiamante__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EntChiamante__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Macrostato__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>NomeChiamante__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>NoteChiusuraAgente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>NoteChiusuraCliente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>NoteChiusura__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>NumSolleciti__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Risolto__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sollecitato__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SubAgente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TelAgente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TelChiamante__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TipoRichiesta__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>UfficioApertura__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>UtAssegnatario__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>UtenteChiusura__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Violazione_SLA__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Zona_di_circolazione__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>dtInAttesaRispCliente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Categoria__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Indicatore_SLA__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SottoCategoria__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Assigned_to_Current_User__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Intermediate_Outcome__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AppointmentDate__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Vendita__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>NotesRecontact__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>UnansweredAttempts__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ActualCalls__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Engagement_Channel__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sale_Point__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Point_of_Sale_Address__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Activity_Notes__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>OptOput_Sent__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RevocationCC_Sent__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Digital_Step__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>TimeSlot__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AppointmentTime__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EmailStatus__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IsMuteCall__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>MuteHandled__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>MuteBlacklistUntil__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>NrCometBlacklist__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>NrRemovedFromOriginalQueueComet__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CanBeRecontacted__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CC_ContactCenter__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CC_QueuePriority__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CC_QueueToAssigne__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CC_Ore_Lavorate__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CCAgentWork__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CC_Tempo_medio_presa_in_carico__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CCVendite__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CCTipo_Cliente__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ProductandScopeforVoice__c</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Owner.Profile</field>
            <table>Case</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Owner.Profile.Name</field>
            <table>Case</table>
        </columns>
        <masterLabel>Cases</masterLabel>
    </sections>
</ReportType>
