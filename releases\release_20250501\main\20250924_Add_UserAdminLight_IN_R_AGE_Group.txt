-----------------------------------------------------------------------------
------------------ ASSIG<PERSON> MEMBERSHIP TO  USER UNIPOLADMINLIGHT FOR R_AGE GROUPS -----------------------
------------------------------------------------------------------------------
------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ---------------------------
------------------------------------------------------------------------------

Use Salesforce Inspector to perform the insert using a prepared Excel file:
    1. Open Salesforce and log in with an account that has permission to insert GroupMember records.
    2. Click on the Salesforce Inspector icon in your browser (usually at the right edge).
    3. Select "Data Import" from the Inspector menu.
    4. In the "Object" field, type and select `GroupMember`.
    5. Open the file `UnipolAdminLighMembership_R_Age.xls` located in the same directory as this manual procedure.
    6. Copy the content of the sheet corresponding to the environment where you are performing the insert (e.g., UAT, DEV, etc.).
    7. Paste the copied rows into the data area of Inspector.
    8. Click "Insert" to upload the memberships. Wait for the confirmation of successful insert.

6. For the PRODUCTION environment, you must prepare a new Excel file with the same template as the one used above. To do this:
    1. Extract all users with the "Unipol Admin Light" profile using this query:
        "SELECT Id, Profile.Name, Name FROM User WHERE Profile.Name = 'Unipol Admin Light'"
    2. Extract all R_AGE groups using this query:
        "SELECT Id, Name, DeveloperName FROM Group WHERE DeveloperName LIKE 'R_AGE%'"
    3. For each user, assign membership to all the R_AGE groups by creating a row for each combination in the Excel file (one row per GroupMember to insert).
    4. Save the Excel file and use Salesforce Inspector as described above to perform the insert in production.

This procedure ensures that all users with the "Unipol Admin Light" profile are members of all R_AGE groups, both in sandbox and production, using either script or Inspector as appropriate.
