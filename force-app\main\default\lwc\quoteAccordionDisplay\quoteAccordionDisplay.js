import { LightningElement, api, track,wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';
import { getRecord, getFieldValue } from 'lightning/uiRecordApi';
import STAGENAME_FIELD from '@salesforce/schema/Opportunity.StageName';
import DOCUMENT_URL_FIELD from '@salesforce/schema/Quote.DocumentURL__c';
import { registerRefreshContainer, REFRESH_ERROR, REFRESH_COMPLETE, REFRESH_COMPLETE_WITH_ERRORS } from 'lightning/refresh';
import areas_of_need_images from '@salesforce/resourceUrl/areas_of_need';
import getStoredQuotesInfo from '@salesforce/apex/AccordionController.getStoredQuotesInfo';
import retrieveProducts from '@salesforce/apex/STProductTabsetController.retrieveProducts';
import { publish, MessageContext } from 'lightning/messageService';
import FEI_CHANNEL from '@salesforce/messageChannel/FeiContainerChannel__c';
import getPermissionSetName    from '@salesforce/apex/FeiHelper.getPermissionSetName';
import getFederationIdentifier from '@salesforce/apex/FeiHelper.getFederationIdentifier';
import getCustomerAndAgencyIds from '@salesforce/apex/FeiHelper.getCustomerAndAgencyIds';
const EXCLUDED_FOR_ACCESS = ['ESSIG_VITA_PREVIDENZA','ESSIG_VITA_INDIVIDUALE','ESSIG_VITA_PREVIDENZA_SIM'];
// FEI flow constants
const PREVENTIVI_FLOW = 'FEIQuickActionPreventivi';
const FEI_PREVENTIVI_VIEW = {
  ESSIG_AUTO:                 'IP.CRUSCOTTO.DISPATCH',
  ESSIG_RE:                   'RE.CRUSCOTTO.DISPATCH',
  ESSIG_VITA:                 'VITA.PROPOSTA',            
  ESSIG_VITA_PREVIDENZA:      'IP.CRUSCOTTO.DISPATCH'
};
const TITLES_PREVENTIVI_VIEW = {
  ESSIG_AUTO:                 'Visualizza preventivo',
  ESSIG_RE:                   'Visualizza preventivo',
  ESSIG_VITA:                 'Visualizza preventivo',   
  ESSIG_VITA_PREVIDENZA:      'Visualizza preventivo'
};
// map per il “modify” FEI
const FEI_PREVENTIVI_MODIFY = {
  ESSIG_AUTO:                 'NPAC.CRUSCOTTO.DISPATCH',
  ESSIG_RE:                   null,
  ESSIG_VITA:                 'VITA.PROPOSTA',
  ESSIG_VITA_PREVIDENZA:      'RE.CRUSCOTTO.DISPATCH',
  ESSIG_VITA_INDIVIDUALE:     'VITA.PROPOSTA'
};
// titoli per il “modify”
const TITLES_PREVENTIVI_MODIFY = {
  ESSIG_AUTO:                 'Definisci in Polizza',
  ESSIG_RE:                   'Modifica preventivo',
  ESSIG_VITA:                 'Modifica preventivo',
  ESSIG_VITA_PREVIDENZA:      'Riprendi preventivo',
  ESSIG_VITA_INDIVIDUALE:     'Riprendi preventivo'
};

const LINEA_PRODOTTO_SOC = {
    ESSIG_AUTO:                  'SOC_1',
    ESSIG_RE :                   'SOC_4',
    ESSIG_VITA :                 'SOC_1',
    ESSIG_VITA_PREVIDENZA:       'SOC_1',
    ESSIG_VITA_INDIVIDUALE:      'SOC_1'
}

const ESSIG_DOMAINS = new Set(['ESSIG_AUTO','ESSIG_RE','ESSIG_VITA','ESSIG_VITA_PREVIDENZIALE','ESSIG_VITA_INDIVIDUALE']);
const MIN_SCREEN_SIZE_FOR_PC = 1200;
const QUOTE_FIELDS = [DOCUMENT_URL_FIELD];
const OPPORTUNITY_FIELDS = [ STAGENAME_FIELD ];
// Constants for image URLs
const BASE = areas_of_need_images + '/areas_of_need/on';
const PET_IMG        = BASE + '/pet.png';
const HOUSE_IMG      = BASE + '/casa.png';
const FAMILY_IMG     = BASE + '/famiglia.png';
const INJURIES_IMG   = BASE + '/infortuni.png';
const MOTORCYCLE_IMG = BASE + '/mobilita.png';
const HEALTH_IMG     = BASE + '/salute.png';
const CAR_IMG        = BASE + '/veicoli.png';
const TRAVEL_IMG     = BASE + '/viaggi.png';
const VITA_IMG       = BASE + '/vita.png';
const ESSIG_AUTO_IMG = BASE + '/essigauto.png'; 

export default class QuoteAccordionDisplay extends NavigationMixin(LightningElement) {
    @api recordId;
    
    @api isStored = false;
    @api source;
    @track isLoading = true;
    @track quotes = [];
    @track hasQuote = false;
    @track isTablet = window.innerWidth <= MIN_SCREEN_SIZE_FOR_PC;
     // Flow & preview
  @track showFEI            = false;
  @track showPreview;
  @track flowName           = 'FEIQuickActionUNICA';
  @track flowTitle          = '';
  @track flowInputVariables = [];
  @track currentPreviewUrl     = '';
  @track currentPreviewDomain  = '';
  @track currentQuotes = [];
  @track storedQuotes = [];
  @track hasStoredQuotes = false;
@track isFeiTypeModify     = false;
@track feiTypeModify       = 'MODIFICA_PREVENTIVO';
@track feiTypeView         = 'CONSULTAZIONE_CONTRATTO';

@track flowTitleModifica   = 'Modifica Preventivo';
@track flowTitleVisualizza = 'Visualizza Preventivo';

    _refreshContainerId;
get unicaLabel() {
    const all = this.currentQuotes.length ? this.currentQuotes : this.storedQuotes;
    if (!all.length) {
        return 'UNICA';
    }

    const dt = (all[0].domainType || '').trim().toUpperCase();
    console.log('Domain Type retrieved (from list): ' + dt);

    if (PREVIDENZA.includes(dt)) {
        return 'PREVIDENZA';
    }

    return ESSIG_DOMAINS.has(dt) ? 'ESSIG' : 'UNICA';
}


@wire(getRecord, { recordId: '$opportunityRecordId', fields: OPPORTUNITY_FIELDS })
  wiredOpp;
  @wire(getRecord, {
    recordId: '$quote.recordId',
    fields: QUOTE_FIELDS
  })
  wiredQuote;

  get opportunityStageName() {
    return getFieldValue(this.wiredOpp.data, STAGENAME_FIELD);
  }
    @api
    get targetId() {
        return this.recordId;
    }
    set targetId(val) {
        this.recordId = val;
    }
  get hasDocument() {
    const docUrl = this.documentUrl;
    const hasDoc = !!docUrl;
    console.log('⚙️ hasDocument – documentUrl:', docUrl, '→', hasDoc);
    return hasDoc;
  }
// inside AccordionHeader class
  get showPdfLink() {
    const hasDoc = this.hasDocument;
    const stage  = this.opportunityStageName?.trim();
    const inGest = stage === 'In gestione';
    const closed = stage === 'Chiuso';
    console.log(`🔎 showPdfLink → hasDoc=${hasDoc}, stage=${stage}`);
    return hasDoc && (inGest || closed);
  }

@track quote;
@track opportunityStageName;

get showAccediLink() {
    if (!this.quote || !this.opportunityStageName) return false; // protezione
    const dt = (this.quote.domainType || '').trim().toUpperCase();
    const inGest = this.opportunityStageName.trim() === 'In gestione';
    const excluded = EXCLUDED_FOR_ACCESS;
    console.log(`🔎 showAccediLink → dt=${dt}, inGest=${inGest}`);
    return (inGest && !excluded.includes(dt));
}


      @wire(MessageContext) messageContext;

  @track storedOpen = false;
get storedSectionIcon() {
  return this.storedOpen ? 'utility:chevrondown' : 'utility:chevronright';
}
toggleStoredSection() {
  this.storedOpen = !this.storedOpen;
}

    @api
    get quoteStored() {
        return this.isStored;
    }
    set quoteStored(val) {
        this.isStored = val;
    }

    @api
    get productChannel() {
        return this.source;
    }
    set productChannel(val) {
        this.source = val;
    }
  get isPrevidenza() {
    return this.source === 'Preventivatore Previdenza';
  }

async connectedCallback() {
  console.log('✅ recordId ricevuto dal FlexCard in pre:', this.recordId);
  await new Promise(resolve => setTimeout(resolve, 3000));
console.log('✅ recordId ricevuto dal FlexCard:', this.recordId);
  window.addEventListener('resize', this.handleResize);
  this._refreshContainerId = registerRefreshContainer(this, this.refreshContainer);
  
  await this.fetchQuotes();
}



    disconnectedCallback() {
        window.removeEventListener('resize', this.handleResize);
    }

    handleResize = () => {
        this.isTablet = window.innerWidth <= MIN_SCREEN_SIZE_FOR_PC;
    }

   async fetchQuotes() {
  this.isLoading = true;
  try {
    /*const products = await retrieveProducts({ recordId: this.recordId });
    if (!products || !products.length) {
      console.warn('❗ Nessun prodotto trovato per recordId:', this.recordId);
      this.productRecordId = null;
      this.hasQuote = false;
      this.hasStoredQuotes = false;
      return;
    }
    this.productRecordId = products[0].id;
    */
    console.log('🧩 Prodotto trovato:', this.recordId);

    // 🔎 Recupero preventivi correnti + storici in parallelo
    const [dataCurrent, dataStored] = await Promise.all([
      getStoredQuotesInfo({
        recordId: this.recordId,
        isStored: false
      }),
      getStoredQuotesInfo({
        recordId: this.recordId,
        isStored: true
      })
    ]);

    console.log('📨 current:', JSON.stringify(dataCurrent));
    console.log('📨 stored:', JSON.stringify(dataStored));

    // 🔎 Mapping
    this.currentQuotes = dataCurrent.map(qd => this._mapQuote(qd));
    this.storedQuotes  = dataStored.map(qd => this._mapQuote(qd));

    this.hasQuote        = this.currentQuotes.length > 0;
    this.hasStoredQuotes = this.storedQuotes.length  > 0;

    console.log('✅ after mapping → hasQuote:', this.hasQuote,
                ' currentQuotes:', this.currentQuotes.length,
                ' storedQuotes:', this.storedQuotes.length);
  } catch (err) {
    console.error('❌ Errore fetchQuotes:', err);
    this.showToast('Errore', 'Contattare l’amministratore di sistema.', 'error');
  } finally {
    this.isLoading = false;
  }
}


// helper privato che estrae la logica di mapping
_mapQuote(qd) {
  console.log('🎯 _mapQuote INPUT qd:', JSON.stringify(qd));

  const stage  = (this.opportunityStageName || '').trim();
  const inGest = stage === 'In gestione';
  const closed = stage === 'Chiuso';
  const dt     = (qd.domainType || '').trim().toUpperCase();

  console.log(`➡️ stage=${stage}, inGest=${inGest}, closed=${closed}, dt=${dt}`);
let areasOfNeedImages = this.convertAreasOfNeedToImages(qd.areasOfNeed);
  if (dt === 'ESSIG_AUTO') {
    areasOfNeedImages = [ESSIG_AUTO_IMG];
    console.log('🔧 override immagini per ESSIG_AUTO:', JSON.stringify(areasOfNeedImages));
  }
  const opportunityCoverages = (qd.opportunityCoverages || []).map((cov, idx) => {
    console.log(`   🧩 Coverage[${idx}] IN:`, JSON.stringify(cov));

    const areaIcon = this.convertAreaOfNeedToImage(cov.areaOfNeed);
    console.log(`   🎨 Coverage[${idx}] area=${cov.areaOfNeed} → areaIcon=${areaIcon}`);

    const mappedCov = {
      key: `${cov.areaOfNeed}-${cov.amount}-${idx}`,
      areaIcon: areaIcon,
      assetItems: cov.areaOfNeed === 'Famiglia' ? ['Famiglia'] : cov.assets,
      descriptionItems: cov.description,
      amount: cov.amount,
      stage: cov.stage,
      fractionation: cov.fractionation,
      conventions: cov.conventions || '-',
      fullName: cov.fullName || `${cov.firstName} ${cov.lastName}`,
      targetProduct: cov.targetProduct,
      ral: cov.ral,
      yearlyGrowth: cov.yearlyGrowth,
      previdentialGap: cov.previdentialGap,
      retirementYear: cov.retirementYear,
      numOfChildren: cov.numOfChildren,
      sector: cov.sector
    };

    console.log(`   ✅ Coverage[${idx}] OUT:`, JSON.stringify(mappedCov));
    return mappedCov;
  });

  const mapped = {
    documentUrl: qd.documentUrl,
    domainType: qd.domainType,
    recordId:          qd.recordId,
    name:              qd.name,
    status:            qd.commercialStatus,
    totalAmount:       qd.totalAmount,
    monthlyContribution: qd.monthlyContribution,
    digitalStep:       qd.digitalStep || '-',
    source:            qd.source,
    cip:               qd.cip,
    creationDate:      qd.creationDate,
    expirationDate:    qd.expirationDate,
    unicaLink:         qd.unicaLink,
    isOpen:            false,
    isStored:          qd.isStored,
    areasOfNeedImages: areasOfNeedImages,
    opportunityCoverages
  };

  console.log('✅ _mapQuote OUTPUT mapped:', JSON.stringify(mapped));
  return mapped;
}

/** Restituisce l’array di URL immagini per gli ambiti */
convertAreasOfNeedToImages(areas = []) {
  return areas
    .map(a => this.convertAreaOfNeedToImage(a))
    .filter(u => u); // scarta eventuali null
}


convertAreaOfNeedToImage(area) {
  const normalized = (area || '').trim().toLowerCase()
    .normalize("NFD").replace(/[\u0300-\u036f]/g, ''); 
    // 👆 toglie gli accenti

  let img = null;

  switch (normalized) {
    case 'cane e gatto': img = PET_IMG; break;
    case 'casa': img = HOUSE_IMG; break;
    case 'famiglia': img = FAMILY_IMG; break;
    case 'infortuni': img = INJURIES_IMG; break;
    case 'mobilita': img = MOTORCYCLE_IMG; break; // gestisce sia Mobilità che Mobilita
    case 'salute': img = HEALTH_IMG; break;
    case 'veicoli': img = CAR_IMG; break;
    case 'viaggio': img = TRAVEL_IMG; break;
    case 'vita': img = VITA_IMG; break;
    case 'previdenza integrativa': img = VITA_IMG; break;
    case 'persona': img = VITA_IMG; break;
    case 'casa e famiglia': img = VITA_IMG; break;
    default: img = null; break;
  }

  console.log(`🎨 convertAreaOfNeedToImage area='${area}' normalized='${normalized}' → ${img}`);
  return img;
}




    toggleSection(event) {
        const id = event.currentTarget.dataset.id;
        // Aggiorna currentQuotes
        this.currentQuotes = this.currentQuotes.map(q => {
            if (q.recordId === id) {
                return { ...q, isOpen: !q.isOpen };
            }
            return q;
        });
        // Aggiorna storedQuotes
        this.storedQuotes = this.storedQuotes.map(q => {
            if (q.recordId === id) {
                return { ...q, isOpen: !q.isOpen };
            }
            return q;
        });
    }


    handleQuoteClick(event) {
        event.stopPropagation();
        const quoteId = event.currentTarget.dataset.id;
        if (quoteId) {
            this[NavigationMixin.Navigate]({
                type: 'standard__recordPage',
                attributes: { recordId: quoteId, actionName: 'view' }
            });
        }
    }

  handleUnicaClick(event) {
    const recId = event.currentTarget.dataset.id;
    const q = this.quotes.find(x => x.recordId === recId);
    if (q && q.unicaLink) {
      window.open(q.unicaLink, '_blank');
    }
  }

async myPDFPreview(event) {
        const recId = event.currentTarget.dataset.id;
        const q = this.currentQuotes.concat(this.storedQuotes).find(x => x.recordId === recId);
        if (!q) return;

        const dt = q.domainType?.trim().toUpperCase();
        const hasDoc = !!q.documentUrl;

        // UNICA PDF preview
        if ((dt === 'PU' || dt === 'ESSIG_RE') && hasDoc) {
            this.currentPreviewUrl = q.documentUrl;
            this.currentPreviewDomain = q.domainType;
            this.openPDFPreview();
        }
        // ESSIG_AUTO: publish LMS payload
        else if (dt === 'ESSIG_AUTO') {
            try {
                const [fiscalCode, permissionSetName] = await Promise.all([
                    getFederationIdentifier(),
                    getPermissionSetName()
                ]);
                const { customerId, agencyId } = await getCustomerAndAgencyIds({ quoteId: q.recordId });

                const feiRequestPayload = {
                    tipoOperazione:  'SS',
                    compagnia:       customerId,
                    agenzia:         agencyId,
                    progressivo:     '1',
                    numeroArchivio:  q.SourceSystemIdentifier0Formula__c,
                    numeroVersione:  q.SourceSystemIdentifier1Formula__c
                };

                publish(this.messageContext, FEI_CHANNEL, {
                    recordId:          q.recordId,
                    FEIID:             'RA.STAMPA.DIFFERITA',
                    FiscalCode:        fiscalCode,
                    permissionSetName: permissionSetName,
                    society:           'SOC_1',
                    feiRequestPayload: feiRequestPayload
                });
                console.log('📤 Published ESSIG_AUTO LMS message');
            } catch (err) {
                console.error('Errore FEI ESSIG_AUTO:', err);
                this.showToast('Errore FEI', err.body?.message || err.message || err, 'error');
            }
        }
        // Other ESSIG flows → existing logic
        else {
            console.log('myPDFPreview: LANCIO FEI PREVIEW');
            this.handleManageFEI(q);
        }
    }

handleManageFEI(event) {
  const recId = event.currentTarget.dataset.id;
  const q = this.currentQuotes.concat(this.storedQuotes)
             .find(x => x.recordId === recId);
  if (!q) return;
  const dt = q.domainType?.trim().toUpperCase();
  if (dt === 'PU') {
    this.handleVisualizzaClick(q);
  } else {
    this.handleVisualizzaClickPDF(q);
  }
}


handleVisualizzaClick(q) {
  const key  = (q.domainType||'').trim().toUpperCase();
  this.isFeiTypeModify = false;
  this.flowInputVariables = [
    { name: 'recordId', type: 'String', value: q.recordId },
    { name: 'feiType',   type: 'String', value: this.feiTypeView }
  ];
  
  const socMap = LINEA_PRODOTTO_SOC;
  this.flowInputVariables.push({ name: 'society', type: 'String', value: socMap[key] });
  console.log('launchPreventivi: aggiunto parametro source = ' + socMap[key]);

  this.flowTitle = this.flowTitleVisualizza;
  this.showFEI   = true;
}

handleVisualizzaClickPDF(q) {
  const key  = (q.domainType||'').trim().toUpperCase();
  const map  = this.isFeiTypeModify ? FEI_PREVENTIVI_MODIFY : FEI_PREVENTIVI_VIEW;
  const tit  = this.isFeiTypeModify ? TITLES_PREVENTIVI_MODIFY : TITLES_PREVENTIVI_VIEW;

  if (!map[key]) {
    console.warn(`FEI non valido: ${key}`);
    return;
  }
  this.flowName = PREVENTIVI_FLOW;
  this.flowTitle = tit[key];
  this.flowInputVariables = [
    { name: 'recordId', type: 'String', value: q.recordId },
    { name: 'FEIID',    type: 'String', value: map[key] }
  ];
  /*if (key === 'ESSIG_VITA_INDIVIDUALE') {
    this.flowInputVariables.push({ name:'society', type:'String', value:'SOC_1' });
  }*/
  const socMap = LINEA_PRODOTTO_SOC;
  this.flowInputVariables.push({ name: 'society', type: 'String', value: socMap[key] });
  console.log('launchPreventivi: aggiunto parametro source = ' + socMap[key]);
        
  this.showFEI = true;
}


    // invece di this.quote.recordId:
invokeFlow(q) {
  this.flowInputVariables = [
    { name: 'recordId', type: 'String', value: q.recordId },
    { name: 'feiType',   type: 'String', value: this.isFeiTypeModify ? this.feiTypeModify : this.feiTypeView }
  ];
  this.flowTitle = this.isFeiTypeModify ? this.flowTitleModifica : this.flowTitleVisualizza;
  this.showFEI = true;
}



    openPDFPreview()  { this.showPreview = true; }
  closePDFPreview() { this.showPreview = false; }


  // chiude il flow container
  handleClose() {
    this.showFEI = false;
  }


    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({ title, message, variant }));
    }

    refreshContainer(refreshPromise) {
        console.log("Refreshing QuoteAccordionDisplay (simplified)");
        this.fetchQuotes();
        return refreshPromise.then(status => {
            if (status === REFRESH_COMPLETE) {
                console.log("Refresh Done!");
            } else if (status === REFRESH_COMPLETE_WITH_ERRORS) {
                console.warn("Refresh Done with issues");
            } else if (status === REFRESH_ERROR) {
                console.error("Refresh Major error");
            }
        });
    }
}