.page-background {
    background-color: #e6f0fa;
    padding: 2rem 0; /* margine sopra e sotto */
    display: flex;
    justify-content: center;
}

.form-container {
    background-color: white;
    width: 720px;
    padding: 1.5rem;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

.form-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    text-align: center;
}

.container {
    padding: 0;
}

.row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.label-col {
    flex: 1;
    margin-right: 1rem;
}

.input-col {
    flex: 1;
}

.readonly-input lightning-input {
    --slds-c-input-color-background: #f3f3f3;
}

.dropdown {
    width: 100%;
    max-width: 250px;
}

.buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
}

.readonly-input lightning-input {
    --slds-c-input-color-background: #e5e5e5; /* grigio più visibile */
    --slds-c-input-color-text: #000; /* testo leggibile */
    --slds-c-input-readonly-color-background: #e5e5e5;
    --slds-c-input-readonly-color-border: #ccc;
}


.readonly-input lightning-input::part(input) {
    color: #000;
}